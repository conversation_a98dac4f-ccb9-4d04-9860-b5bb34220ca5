{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@app/services/projets.service\";\nexport let UpdateProjectComponent = /*#__PURE__*/(() => {\n  class UpdateProjectComponent {\n    constructor(route, fb, projetService, router) {\n      this.route = route;\n      this.fb = fb;\n      this.projetService = projetService;\n      this.router = router;\n    }\n    ngOnInit() {\n      this.projectId = this.route.snapshot.paramMap.get('id') || '';\n      this.updateForm = this.fb.group({\n        titre: ['', Validators.required],\n        description: [''],\n        groupe: [''],\n        dateLimite: ['', Validators.required]\n      });\n      this.projetService.getProjetById(this.projectId).subscribe(projet => {\n        this.updateForm.patchValue({\n          titre: projet.titre,\n          description: projet.description,\n          groupe: projet.groupe,\n          dateLimite: projet.dateLimite\n        });\n      });\n    }\n    onSubmit() {\n      if (this.updateForm.valid) {\n        this.projetService.updateProjet(this.projectId, this.updateForm.value).subscribe(() => {\n          alert('Projet mis à jour avec succès');\n          this.router.navigate(['/projects']); // adapte selon ta route\n        });\n      }\n    }\n\n    static {\n      this.ɵfac = function UpdateProjectComponent_Factory(t) {\n        return new (t || UpdateProjectComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ProjetService), i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UpdateProjectComponent,\n        selectors: [[\"app-update-project\"]],\n        decls: 22,\n        vars: 2,\n        consts: [[1, \"max-w-xl\", \"mx-auto\", \"mt-10\", \"p-6\", \"bg-white\", \"shadow-md\", \"rounded-xl\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-4\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"border\", \"border-gray-300\", \"rounded-md\", \"p-2\"], [\"formControlName\", \"groupe\", 1, \"mt-1\", \"block\", \"w-full\", \"border\", \"border-gray-300\", \"rounded-md\", \"p-2\"], [\"type\", \"date\", \"formControlName\", \"dateLimite\", 1, \"mt-1\", \"block\", \"w-full\", \"border\", \"border-gray-300\", \"rounded-md\", \"p-2\"], [\"formControlName\", \"description\", 1, \"mt-1\", \"block\", \"w-full\", \"border\", \"border-gray-300\", \"rounded-md\", \"p-2\"], [\"type\", \"submit\", 1, \"bg-[#7826b5]\", \"text-white\", \"px-4\", \"py-2\", \"rounded\", \"hover:bg-[#5f1d8f]\", 3, \"disabled\"]],\n        template: function UpdateProjectComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1);\n            i0.ɵɵtext(2, \"Modifier le projet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"form\", 2);\n            i0.ɵɵlistener(\"ngSubmit\", function UpdateProjectComponent_Template_form_ngSubmit_3_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"label\", 4);\n            i0.ɵɵtext(6, \"Titre\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(7, \"input\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\", 3)(9, \"label\", 4);\n            i0.ɵɵtext(10, \"Groupe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(11, \"input\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 3)(13, \"label\", 4);\n            i0.ɵɵtext(14, \"Date limite\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(15, \"input\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 3)(17, \"label\", 4);\n            i0.ɵɵtext(18, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(19, \"textarea\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"button\", 9);\n            i0.ɵɵtext(21, \" Mettre \\u00E0 jour \");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"formGroup\", ctx.updateForm);\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"disabled\", !ctx.updateForm.valid);\n          }\n        },\n        dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName]\n      });\n    }\n  }\n  return UpdateProjectComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}