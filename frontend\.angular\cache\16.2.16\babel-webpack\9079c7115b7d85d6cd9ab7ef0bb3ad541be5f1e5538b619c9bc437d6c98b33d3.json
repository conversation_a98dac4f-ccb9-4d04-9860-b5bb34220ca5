{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { MessageType, CallType } from 'src/app/models/message.model';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/services/user-status.service\";\nimport * as i6 from \"src/app/services/toast.service\";\nimport * as i7 from \"src/app/services/logger.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../../components/voice-recorder/voice-recorder.component\";\nimport * as i10 from \"../../../../components/voice-message-player/voice-message-player.component\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 58);\n  }\n}\nfunction MessageChatComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"span\", 60);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otherParticipant.isOnline ? \"En ligne\" : ctx_r1.formatLastActive(ctx_r1.otherParticipant.lastActive), \" \");\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"animate-pulse\": a0\n  };\n};\nfunction MessageChatComponent_ng_container_14_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r32 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassMap(\"absolute -top-2 -right-2 min-w-[20px] h-5 px-1.5 text-white text-xs rounded-md flex items-center justify-center font-bold shadow-lg border border-white/20 \" + action_r32.badge.class);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c2, action_r32.badge.animate));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", action_r32.badge.count > 99 ? \"99+\" : action_r32.badge.count, \" \");\n  }\n}\nconst _c3 = function () {\n  return {};\n};\nfunction MessageChatComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_14_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const action_r32 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(action_r32.onClick());\n    });\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵtemplate(3, MessageChatComponent_ng_container_14_span_3_Template, 2, 6, \"span\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const action_r32 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"whatsapp-action-button \" + action_r32.class);\n    i0.ɵɵproperty(\"ngClass\", action_r32.activeClass && action_r32.isActive ? action_r32.activeClass : i0.ɵɵpureFunction0(7, _c3))(\"title\", action_r32.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(action_r32.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", action_r32.badge && action_r32.badge.count > 0);\n  }\n}\nfunction MessageChatComponent_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 64);\n  }\n}\nconst _c4 = function (a0) {\n  return {\n    \"bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20\": a0\n  };\n};\nfunction MessageChatComponent_div_19_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_19_button_8_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const status_r38 = restoredCtx.$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      ctx_r39.updateUserStatus(status_r38.key);\n      return i0.ɵɵresetView(ctx_r39.toggleStatusSelector());\n    });\n    i0.ɵɵelementStart(1, \"div\", 73);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementStart(3, \"div\")(4, \"div\", 75);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 76);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const status_r38 = ctx.$implicit;\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r37.isUpdatingStatus)(\"ngClass\", i0.ɵɵpureFunction1(6, _c4, ctx_r37.currentUserStatus === status_r38.key));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(status_r38.icon + \" \" + status_r38.color + \" mr-3 text-xs\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(status_r38.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", status_r38.description, \" \");\n  }\n}\nfunction MessageChatComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66)(2, \"div\", 67)(3, \"span\");\n    i0.ɵɵtext(4, \"Statut actuel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 68);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 69);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_19_button_8_Template, 8, 8, \"button\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 71)(10, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_19_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext();\n      ctx_r41.toggleUserStatusPanel();\n      return i0.ɵɵresetView(ctx_r41.toggleStatusSelector());\n    });\n    i0.ɵɵelementStart(11, \"div\", 73);\n    i0.ɵɵelement(12, \"i\", 74);\n    i0.ɵɵelementStart(13, \"div\")(14, \"div\", 75);\n    i0.ɵɵtext(15, \"Voir tous les utilisateurs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 76);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getStatusColor(ctx_r4.currentUserStatus));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStatusText(ctx_r4.currentUserStatus), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getStatusOptions());\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getOnlineUsersCount(), \" en ligne \");\n  }\n}\nfunction MessageChatComponent_div_23_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 81);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_23_a_4_Template_a_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r46);\n      const theme_r44 = restoredCtx.$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.changeTheme(theme_r44.key));\n    });\n    i0.ɵɵelementStart(1, \"div\", 73);\n    i0.ɵɵelement(2, \"div\", 17);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const theme_r44 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"hover:bg-\" + theme_r44.hoverColor + \"/10 dark:hover:bg-\" + theme_r44.hoverColor + \"/10\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", \"w-4 h-4 rounded-full bg-\" + theme_r44.color + \" mr-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(theme_r44.label);\n  }\n}\nfunction MessageChatComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵtext(2, \" Choisir un th\\u00E8me \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 69);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_23_a_4_Template, 5, 3, \"a\", 80);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.getThemeOptions());\n  }\n}\nfunction MessageChatComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 79);\n    i0.ɵɵtext(2, \" Options de conversation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 69)(4, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.clearConversation());\n    });\n    i0.ɵɵelementStart(5, \"div\", 73);\n    i0.ɵɵelement(6, \"i\", 83);\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \"Vider la conversation\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.exportConversation());\n    });\n    i0.ɵɵelementStart(10, \"div\", 73);\n    i0.ɵɵelement(11, \"i\", 84);\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13, \"Exporter la conversation\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.toggleConversationInfo());\n    });\n    i0.ɵɵelementStart(15, \"div\", 73);\n    i0.ɵɵelement(16, \"i\", 85);\n    i0.ɵɵelementStart(17, \"div\");\n    i0.ɵɵtext(18, \"Informations\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_27_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.toggleConversationSettings());\n    });\n    i0.ɵɵelementStart(20, \"div\", 73);\n    i0.ɵɵelement(21, \"i\", 86);\n    i0.ɵɵelementStart(22, \"div\");\n    i0.ɵɵtext(23, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_28_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵelement(1, \"div\", 100);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_28_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_28_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r56.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 102);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_28_div_10_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_28_div_10_button_4_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r61);\n      const result_r59 = restoredCtx.$implicit;\n      const ctx_r60 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(result_r59.id && ctx_r60.navigateToMessage(result_r59.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 108)(2, \"div\", 109);\n    i0.ɵɵelement(3, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 111)(5, \"div\", 112);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 113);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const result_r59 = ctx.$implicit;\n    const ctx_r58 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r58.formatMessageTime(result_r59.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r58.highlightSearchTerms(result_r59.content || \"\", ctx_r58.searchQuery), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MessageChatComponent_div_28_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 105);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_28_div_10_button_4_Template, 8, 2, \"button\", 106);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r54.searchResults.length, \" r\\u00E9sultat(s) trouv\\u00E9(s) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r54.searchResults);\n  }\n}\nfunction MessageChatComponent_div_28_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵelement(1, \"i\", 115);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Aucun message trouv\\u00E9 pour \\\"\", ctx_r55.searchQuery, \"\\\" \");\n  }\n}\nfunction MessageChatComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"div\", 89);\n    i0.ɵɵelement(3, \"i\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 91)(5, \"input\", 92);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_28_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.searchQuery = $event);\n    })(\"input\", function MessageChatComponent_div_28_Template_input_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.onSearchInput($event));\n    })(\"keydown\", function MessageChatComponent_div_28_Template_input_keydown_5_listener($event) {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.onSearchKeyPress($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, MessageChatComponent_div_28_div_6_Template, 2, 0, \"div\", 93);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_28_button_7_Template, 2, 0, \"button\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_28_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.toggleSearchBar());\n    });\n    i0.ɵɵelement(9, \"i\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, MessageChatComponent_div_28_div_10_Template, 5, 2, \"div\", 97);\n    i0.ɵɵtemplate(11, MessageChatComponent_div_28_div_11_Template, 3, 1, \"div\", 98);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r7.searchQuery);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.isSearching);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.searchQuery && !ctx_r7.isSearching);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.searchMode && ctx_r7.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.searchMode && ctx_r7.searchResults.length === 0 && !ctx_r7.isSearching && ctx_r7.searchQuery.length >= 2);\n  }\n}\nfunction MessageChatComponent_div_29_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125)(1, \"div\", 126);\n    i0.ɵɵelement(2, \"i\", 127);\n    i0.ɵɵelementStart(3, \"div\", 128);\n    i0.ɵɵtext(4, \"Aucun message \\u00E9pingl\\u00E9\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_29_div_10_button_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pinnedMessage_r70 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(pinnedMessage_r70.content);\n  }\n}\nfunction MessageChatComponent_div_29_div_10_button_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 146);\n    i0.ɵɵelement(1, \"i\", 147);\n    i0.ɵɵtext(2, \" Image \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_29_div_10_button_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 146);\n    i0.ɵɵelement(1, \"i\", 148);\n    i0.ɵɵtext(2, \" Message vocal \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_29_div_10_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_div_10_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r76);\n      const pinnedMessage_r70 = restoredCtx.$implicit;\n      const ctx_r75 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r75.scrollToPinnedMessage(pinnedMessage_r70.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 132)(2, \"div\", 133);\n    i0.ɵɵelement(3, \"img\", 134);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 111)(5, \"div\", 135)(6, \"span\", 136);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 137);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 138);\n    i0.ɵɵtemplate(11, MessageChatComponent_div_29_div_10_button_1_span_11_Template, 2, 1, \"span\", 139);\n    i0.ɵɵtemplate(12, MessageChatComponent_div_29_div_10_button_1_span_12_Template, 3, 0, \"span\", 140);\n    i0.ɵɵtemplate(13, MessageChatComponent_div_29_div_10_button_1_span_13_Template, 3, 0, \"span\", 140);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 141);\n    i0.ɵɵelement(15, \"i\", 142);\n    i0.ɵɵelementStart(16, \"span\", 143);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 144);\n    i0.ɵɵelement(19, \"i\", 145);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const pinnedMessage_r70 = ctx.$implicit;\n    const ctx_r69 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (pinnedMessage_r70.sender == null ? null : pinnedMessage_r70.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", (pinnedMessage_r70.sender == null ? null : pinnedMessage_r70.sender.username) || \"User\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (pinnedMessage_r70.sender == null ? null : pinnedMessage_r70.sender.username) || \"Utilisateur inconnu\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r69.formatMessageTime(pinnedMessage_r70.timestamp), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", pinnedMessage_r70.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r69.hasImage(pinnedMessage_r70));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r69.isVoiceMessage(pinnedMessage_r70));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \\u00C9pingl\\u00E9 \", pinnedMessage_r70.pinnedAt ? \"le \" + ctx_r69.formatMessageDate(pinnedMessage_r70.pinnedAt) : \"\", \" \");\n  }\n}\nfunction MessageChatComponent_div_29_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 129);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_29_div_10_button_1_Template, 20, 8, \"button\", 130);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r68 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r68.pinnedMessages);\n  }\n}\nfunction MessageChatComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r78 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 116)(1, \"div\", 117)(2, \"div\", 118);\n    i0.ɵɵelement(3, \"i\", 119);\n    i0.ɵɵelementStart(4, \"h3\", 120);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_29_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r78);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.togglePinnedMessages());\n    });\n    i0.ɵɵelement(7, \"i\", 102);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 122);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_29_div_9_Template, 5, 0, \"div\", 123);\n    i0.ɵɵtemplate(10, MessageChatComponent_div_29_div_10_Template, 2, 1, \"div\", 124);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Messages \\u00E9pingl\\u00E9s (\", ctx_r8.getPinnedMessagesCount(), \") \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.pinnedMessages.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.pinnedMessages.length > 0);\n  }\n}\nfunction MessageChatComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 149)(1, \"div\", 150)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"div\", 151)(4, \"div\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 153);\n    i0.ɵɵtext(6, \" Initializing communication... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 154)(1, \"div\", 155)(2, \"div\", 156);\n    i0.ɵɵelement(3, \"div\", 157)(4, \"div\", 158)(5, \"div\", 159);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 76);\n    i0.ɵɵtext(7, \" Retrieving data... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 160)(1, \"div\", 161);\n    i0.ɵɵelement(2, \"div\", 162);\n    i0.ɵɵelementStart(3, \"div\", 163);\n    i0.ɵɵtext(4, \" Communication Initialized \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 162);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"div\", 165)(2, \"div\", 166);\n    i0.ɵɵelement(3, \"i\", 167)(4, \"div\", 168);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 169);\n    i0.ɵɵtext(7, \" System Error: Communication Failure \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 170);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r13.error);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 182);\n    i0.ɵɵelement(1, \"div\", 183);\n    i0.ɵɵelementStart(2, \"div\", 184);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 183);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext().$implicit;\n    const ctx_r82 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r82.formatMessageDate(message_r80 == null ? null : message_r80.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 185);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r92);\n      const message_r80 = i0.ɵɵnextContext().$implicit;\n      const ctx_r90 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r90.toggleReactionPicker(message_r80.id));\n    });\n    i0.ɵɵelement(1, \"i\", 186);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_4_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r97 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 190);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_4_button_2_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r97);\n      const emoji_r94 = restoredCtx.$implicit;\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r95 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r95.reactToMessage(message_r80.id, emoji_r94));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r94 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", \"R\\u00E9agir avec \" + emoji_r94);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r94, \" \");\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 187)(1, \"div\", 188);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_36_div_1_div_4_button_2_Template, 2, 2, \"button\", 189);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r84 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r84.availableReactions);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵelement(1, \"img\", 192);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r109 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 204);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r109);\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r107 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r107.toggleMessageOptions(message_r80.id));\n    });\n    i0.ɵɵelement(1, \"i\", 205);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_2_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r114 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 207);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_2_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r114);\n      const message_r80 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r112 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r112.startEditMessage(message_r80));\n    });\n    i0.ɵɵelement(1, \"i\", 213);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Modifier\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_2_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r117 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 214);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_2_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r117);\n      const message_r80 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r115 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r115.showDeleteConfirmation(message_r80.id));\n    });\n    i0.ɵɵelement(1, \"i\", 215);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Supprimer\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r120 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 206)(1, \"button\", 207);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r120);\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r118 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r118.startReplyToMessage(message_r80));\n    });\n    i0.ɵɵelement(2, \"i\", 208);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"R\\u00E9pondre\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 207);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r120);\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r121 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r121.openForwardModal(message_r80));\n    });\n    i0.ɵɵelement(6, \"i\", 209);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Transf\\u00E9rer\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 207);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r120);\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r123 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r123.showPinConfirmation(message_r80.id));\n    });\n    i0.ɵɵelement(10, \"i\", 210);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, MessageChatComponent_ng_container_36_div_1_div_7_div_2_button_13_Template, 4, 0, \"button\", 211);\n    i0.ɵɵtemplate(14, MessageChatComponent_ng_container_36_div_1_div_7_div_2_button_14_Template, 4, 0, \"button\", 212);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r100 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵclassMap(ctx_r100.getPinIcon(message_r80));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r100.getPinDisplayText(message_r80));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r100.canEditMessage(message_r80));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r100.canEditMessage(message_r80));\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r128 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 216)(1, \"div\", 217);\n    i0.ɵɵtext(2, \" Supprimer ce message ? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 218)(4, \"button\", 219);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r128);\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r126 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r126.cancelDeleteMessage(message_r80.id));\n    });\n    i0.ɵɵtext(5, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 220);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r128);\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r129 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r129.confirmDeleteMessage(message_r80.id));\n    });\n    i0.ɵɵtext(7, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_4_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 224);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r134 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 221)(1, \"div\", 217);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 218)(4, \"button\", 219);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_4_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r134);\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r132 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r132.cancelPinConfirmation(message_r80.id));\n    });\n    i0.ɵɵtext(5, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 222);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_4_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r134);\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r135 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r135.togglePinMessage(message_r80));\n    });\n    i0.ɵɵtemplate(7, MessageChatComponent_ng_container_36_div_1_div_7_div_4_i_7_Template, 1, 0, \"i\", 223);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r102 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r102.isMessagePinned(message_r80) ? \"D\\u00E9s\\u00E9pingler ce message ?\" : \"\\u00C9pingler ce message ?\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r102.isPinning[message_r80.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r102.isPinning[message_r80.id]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r102.isMessagePinned(message_r80) ? \"D\\u00E9s\\u00E9pingler\" : \"\\u00C9pingler\");\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 230);\n    i0.ɵɵelement(1, \"i\", 142);\n    i0.ɵɵelementStart(2, \"span\", 231);\n    i0.ɵɵtext(3, \"\\u00C9pingl\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 232)(1, \"div\", 233);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 234);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (message_r80.replyTo.sender == null ? null : message_r80.replyTo.sender.username) || \"Utilisateur\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", message_r80.replyTo.content || \"Message\", \" \");\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_5_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(message_r80.content);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_5_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 235);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r80.content, \" \");\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_5_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 236);\n    i0.ɵɵtext(1, \" (modifi\\u00E9) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 225);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_36_div_1_div_7_div_5_div_1_Template, 4, 0, \"div\", 226);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_36_div_1_div_7_div_5_div_2_Template, 5, 2, \"div\", 227);\n    i0.ɵɵtemplate(3, MessageChatComponent_ng_container_36_div_1_div_7_div_5_span_3_Template, 2, 1, \"span\", 139);\n    i0.ɵɵtemplate(4, MessageChatComponent_ng_container_36_div_1_div_7_div_5_span_4_Template, 2, 1, \"span\", 228);\n    i0.ɵɵtemplate(5, MessageChatComponent_ng_container_36_div_1_div_7_div_5_span_5_Template, 2, 0, \"span\", 229);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r103 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r103.isMessagePinned(message_r80));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.replyTo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !message_r80.isDeleted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.isDeleted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.isEdited && !message_r80.isDeleted);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r148 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 237)(1, \"textarea\", 238);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template_textarea_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r148);\n      const ctx_r147 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r147.editingContent = $event);\n    })(\"keydown\", function MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template_textarea_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r148);\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r149 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r149.onEditKeyPress($event, message_r80.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 239)(3, \"button\", 219);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r148);\n      const ctx_r151 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r151.cancelEditMessage());\n    });\n    i0.ɵɵtext(4, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 240);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r148);\n      const message_r80 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r152 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r152.saveEditMessage(message_r80.id));\n    });\n    i0.ɵɵtext(6, \" Sauvegarder \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r104 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r104.editingContent);\n  }\n}\nconst _c5 = function (a0, a1) {\n  return {\n    \"bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] border border-[#4f5fad]/30 dark:border-[#6d78c9]/30\": a0,\n    \"bg-[#edf1f4]/50 dark:bg-[#3a3a3a]/50 text-[#6d6870] dark:text-[#a0a0a0] border border-[#edf1f4] dark:border-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10\": a1\n  };\n};\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_7_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r158 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 243);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_7_div_7_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r158);\n      const reaction_r155 = restoredCtx.$implicit;\n      const message_r80 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r156 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(message_r80.id && ctx_r156.onReactionClick(message_r80.id, reaction_r155.emoji));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 75);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reaction_r155 = ctx.$implicit;\n    const message_r80 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r154 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c5, ctx_r154.hasUserReacted(message_r80, reaction_r155.emoji), !ctx_r154.hasUserReacted(message_r80, reaction_r155.emoji)))(\"title\", reaction_r155.count + \" r\\u00E9action(s)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r155.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r155.count);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 241);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_36_div_1_div_7_div_7_button_1_Template, 5, 7, \"button\", 242);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r105 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r105.getUniqueReactions(message_r80));\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_span_11_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 247);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_span_11_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 248);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_7_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 244);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_36_div_1_div_7_span_11_i_1_Template, 1, 0, \"i\", 245);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_36_div_1_div_7_span_11_i_2_Template, 1, 0, \"i\", 246);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80 == null ? null : message_r80.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(message_r80 == null ? null : message_r80.isRead));\n  }\n}\nconst _c6 = function (a0, a1, a2, a3) {\n  return {\n    \"futuristic-message-pending\": a0,\n    \"futuristic-message-sending\": a1,\n    \"futuristic-message-error\": a2,\n    \"futuristic-message-pinned\": a3\n  };\n};\nfunction MessageChatComponent_ng_container_36_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r166 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 193);\n    i0.ɵɵlistener(\"mouseenter\", function MessageChatComponent_ng_container_36_div_1_div_7_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r166);\n      const message_r80 = i0.ɵɵnextContext().$implicit;\n      const ctx_r164 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(message_r80.id && ctx_r164.canEditMessage(message_r80) ? null : null);\n    })(\"mouseleave\", function MessageChatComponent_ng_container_36_div_1_div_7_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r166);\n      const message_r80 = i0.ɵɵnextContext().$implicit;\n      const ctx_r167 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(message_r80.id && ctx_r167.canEditMessage(message_r80) ? null : null);\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_36_div_1_div_7_button_1_Template, 2, 0, \"button\", 194);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_36_div_1_div_7_div_2_Template, 15, 5, \"div\", 195);\n    i0.ɵɵtemplate(3, MessageChatComponent_ng_container_36_div_1_div_7_div_3_Template, 8, 0, \"div\", 196);\n    i0.ɵɵtemplate(4, MessageChatComponent_ng_container_36_div_1_div_7_div_4_Template, 10, 4, \"div\", 197);\n    i0.ɵɵtemplate(5, MessageChatComponent_ng_container_36_div_1_div_7_div_5_Template, 6, 5, \"div\", 198);\n    i0.ɵɵtemplate(6, MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template, 7, 1, \"div\", 199);\n    i0.ɵɵtemplate(7, MessageChatComponent_ng_container_36_div_1_div_7_div_7_Template, 2, 1, \"div\", 200);\n    i0.ɵɵelementStart(8, \"div\", 201)(9, \"span\", 202);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, MessageChatComponent_ng_container_36_div_1_div_7_span_11_Template, 3, 2, \"span\", 203);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext().$implicit;\n    const ctx_r86 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(11, _c6, message_r80.isPending, message_r80.isPending && !message_r80.isError, message_r80.isError, ctx_r86.isMessagePinned(message_r80)))(\"id\", \"message-\" + message_r80.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.id && ctx_r86.canEditMessage(message_r80) && !message_r80.isDeleted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.id && ctx_r86.showMessageOptions[message_r80.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.id && ctx_r86.showDeleteConfirm[message_r80.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.id && ctx_r86.showPinConfirm[message_r80.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r86.editingMessageId !== message_r80.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r86.editingMessageId === message_r80.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.reactions && message_r80.reactions.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r86.formatMessageTime(message_r80 == null ? null : message_r80.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender.id) === ctx_r86.currentUserId || (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender._id) === ctx_r86.currentUserId || (message_r80 == null ? null : message_r80.senderId) === ctx_r86.currentUserId);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 256);\n  }\n  if (rf & 2) {\n    const i_r173 = ctx.$implicit;\n    const ctx_r170 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"height\", ctx_r170.getVoiceBarHeight(i_r173), \"px\");\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_8_div_8_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r178 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 243);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_8_div_8_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r178);\n      const reaction_r175 = restoredCtx.$implicit;\n      const message_r80 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r176 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(message_r80.id && ctx_r176.onReactionClick(message_r80.id, reaction_r175.emoji));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 75);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reaction_r175 = ctx.$implicit;\n    const message_r80 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r174 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c5, ctx_r174.hasUserReacted(message_r80, reaction_r175.emoji), !ctx_r174.hasUserReacted(message_r80, reaction_r175.emoji)))(\"title\", reaction_r175.count + \" r\\u00E9action(s)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r175.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r175.count);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 241);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_36_div_1_div_8_div_8_button_1_Template, 5, 7, \"button\", 242);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r171 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r171.getUniqueReactions(message_r80));\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_8_span_12_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 247);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_8_span_12_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 248);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_8_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 244);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_36_div_1_div_8_span_12_i_1_Template, 1, 0, \"i\", 245);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_36_div_1_div_8_span_12_i_2_Template, 1, 0, \"i\", 246);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80 == null ? null : message_r80.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(message_r80 == null ? null : message_r80.isRead));\n  }\n}\nconst _c7 = function (a0, a1, a2, a3) {\n  return {\n    \"voice-message-sent\": a0,\n    \"voice-message-received\": a1,\n    \"voice-message-pending\": a2,\n    \"voice-message-error\": a3\n  };\n};\nconst _c8 = function () {\n  return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19];\n};\nfunction MessageChatComponent_ng_container_36_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 249)(1, \"button\", 250);\n    i0.ɵɵelement(2, \"i\", 251);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 252);\n    i0.ɵɵtemplate(4, MessageChatComponent_ng_container_36_div_1_div_8_div_4_Template, 1, 2, \"div\", 253);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 254);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"app-voice-message-player\", 255);\n    i0.ɵɵtemplate(8, MessageChatComponent_ng_container_36_div_1_div_8_div_8_Template, 2, 1, \"div\", 200);\n    i0.ɵɵelementStart(9, \"div\", 201)(10, \"span\", 202);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, MessageChatComponent_ng_container_36_div_1_div_8_span_12_Template, 3, 2, \"span\", 203);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext().$implicit;\n    const ctx_r87 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(8, _c7, (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender.id) === ctx_r87.currentUserId || (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender._id) === ctx_r87.currentUserId || (message_r80 == null ? null : message_r80.senderId) === ctx_r87.currentUserId, !((message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender.id) === ctx_r87.currentUserId || (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender._id) === ctx_r87.currentUserId || (message_r80 == null ? null : message_r80.senderId) === ctx_r87.currentUserId), message_r80.isPending, message_r80.isError));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(13, _c8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r87.formatVoiceDuration(ctx_r87.getVoiceMessageDuration(message_r80)), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"audioUrl\", ctx_r87.getVoiceMessageUrl(message_r80))(\"duration\", ctx_r87.getVoiceMessageDuration(message_r80));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.reactions && message_r80.reactions.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r87.formatMessageTime(message_r80 == null ? null : message_r80.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender.id) === ctx_r87.currentUserId || (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender._id) === ctx_r87.currentUserId || (message_r80 == null ? null : message_r80.senderId) === ctx_r87.currentUserId);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_9_div_6_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r191 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 243);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_container_36_div_1_div_9_div_6_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r191);\n      const reaction_r188 = restoredCtx.$implicit;\n      const message_r80 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r189 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(message_r80.id && ctx_r189.onReactionClick(message_r80.id, reaction_r188.emoji));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 75);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reaction_r188 = ctx.$implicit;\n    const message_r80 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r187 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c5, ctx_r187.hasUserReacted(message_r80, reaction_r188.emoji), !ctx_r187.hasUserReacted(message_r80, reaction_r188.emoji)))(\"title\", reaction_r188.count + \" r\\u00E9action(s)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r188.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r188.count);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_9_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 241);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_36_div_1_div_9_div_6_button_1_Template, 5, 7, \"button\", 242);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r185 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r185.getUniqueReactions(message_r80));\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_9_span_10_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 247);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_9_span_10_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 248);\n  }\n}\nfunction MessageChatComponent_ng_container_36_div_1_div_9_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 244);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_36_div_1_div_9_span_10_i_1_Template, 1, 0, \"i\", 245);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_36_div_1_div_9_span_10_i_2_Template, 1, 0, \"i\", 246);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80 == null ? null : message_r80.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(message_r80 == null ? null : message_r80.isRead));\n  }\n}\nconst _c9 = function (a0, a1, a2) {\n  return {\n    \"futuristic-message-pending\": a0,\n    \"futuristic-message-sending\": a1,\n    \"futuristic-message-error\": a2\n  };\n};\nfunction MessageChatComponent_ng_container_36_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 257)(1, \"div\", 258)(2, \"a\", 259);\n    i0.ɵɵelement(3, \"img\", 260);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 261);\n    i0.ɵɵelement(5, \"i\", 262);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, MessageChatComponent_ng_container_36_div_1_div_9_div_6_Template, 2, 1, \"div\", 200);\n    i0.ɵɵelementStart(7, \"div\", 201)(8, \"span\", 202);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, MessageChatComponent_ng_container_36_div_1_div_9_span_10_Template, 3, 2, \"span\", 203);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r80 = i0.ɵɵnextContext().$implicit;\n    const ctx_r88 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c9, message_r80.isPending, message_r80.isPending && !message_r80.isError, message_r80.isError));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"href\", ctx_r88.getImageUrl(message_r80), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r88.getImageUrl(message_r80), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", message_r80.reactions && message_r80.reactions.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r88.formatMessageTime(message_r80 == null ? null : message_r80.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender.id) === ctx_r88.currentUserId || (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender._id) === ctx_r88.currentUserId || (message_r80 == null ? null : message_r80.senderId) === ctx_r88.currentUserId);\n  }\n}\nconst _c10 = function (a0, a1) {\n  return {\n    \"futuristic-message-current-user\": a0,\n    \"futuristic-message-other-user\": a1\n  };\n};\nfunction MessageChatComponent_ng_container_36_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 172);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_36_div_1_div_1_Template, 5, 1, \"div\", 173);\n    i0.ɵɵelementStart(2, \"div\", 174);\n    i0.ɵɵtemplate(3, MessageChatComponent_ng_container_36_div_1_button_3_Template, 2, 0, \"button\", 175);\n    i0.ɵɵtemplate(4, MessageChatComponent_ng_container_36_div_1_div_4_Template, 3, 1, \"div\", 176);\n    i0.ɵɵtemplate(5, MessageChatComponent_ng_container_36_div_1_div_5_Template, 2, 1, \"div\", 177);\n    i0.ɵɵelementStart(6, \"div\", 178);\n    i0.ɵɵtemplate(7, MessageChatComponent_ng_container_36_div_1_div_7_Template, 12, 16, \"div\", 179);\n    i0.ɵɵtemplate(8, MessageChatComponent_ng_container_36_div_1_div_8_Template, 13, 14, \"div\", 180);\n    i0.ɵɵtemplate(9, MessageChatComponent_ng_container_36_div_1_div_9_Template, 11, 10, \"div\", 181);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r80 = ctx.$implicit;\n    const i_r81 = ctx.index;\n    const ctx_r79 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-message-id\", message_r80.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r79.shouldShowDateHeader(i_r81));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c10, (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender.id) === ctx_r79.currentUserId || (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender._id) === ctx_r79.currentUserId || (message_r80 == null ? null : message_r80.senderId) === ctx_r79.currentUserId, !((message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender.id) === ctx_r79.currentUserId || (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender._id) === ctx_r79.currentUserId || (message_r80 == null ? null : message_r80.senderId) === ctx_r79.currentUserId)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.id && !message_r80.isDeleted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r80.id && ctx_r79.showReactionPicker[message_r80.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !((message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender.id) === ctx_r79.currentUserId || (message_r80 == null ? null : message_r80.sender == null ? null : message_r80.sender._id) === ctx_r79.currentUserId || (message_r80 == null ? null : message_r80.senderId) === ctx_r79.currentUserId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (message_r80 == null ? null : message_r80.content) && !ctx_r79.hasImage(message_r80) && !ctx_r79.isVoiceMessage(message_r80));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r79.isVoiceMessage(message_r80));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r79.hasImage(message_r80));\n  }\n}\nfunction MessageChatComponent_ng_container_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_36_div_1_Template, 10, 12, \"div\", 171);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.messages);\n  }\n}\nfunction MessageChatComponent_ng_template_37_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r200 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 264)(1, \"div\", 265);\n    i0.ɵɵelement(2, \"i\", 266);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 267);\n    i0.ɵɵtext(4, \" Aucun message dans cette conversation. \");\n    i0.ɵɵelement(5, \"br\");\n    i0.ɵɵtext(6, \"\\u00C9tablissez le premier contact pour commencer. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 268);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_template_37_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r200);\n      const ctx_r199 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView((tmp_b_0 = ctx_r199.messageForm.get(\"content\")) == null ? null : tmp_b_0.setValue(\"Bonjour!\"));\n    });\n    i0.ɵɵelement(8, \"i\", 269);\n    i0.ɵɵtext(9, \" Initialiser la communication \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MessageChatComponent_ng_template_37_div_0_Template, 10, 0, \"div\", 263);\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.loading && !ctx_r16.error);\n  }\n}\nfunction MessageChatComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 270)(1, \"div\", 191);\n    i0.ɵɵelement(2, \"img\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 271)(4, \"div\", 272);\n    i0.ɵɵelement(5, \"div\", 273)(6, \"div\", 274)(7, \"div\", 275);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r17.otherParticipant == null ? null : ctx_r17.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MessageChatComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r202 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 276);\n    i0.ɵɵelement(1, \"img\", 277);\n    i0.ɵɵelementStart(2, \"button\", 278);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_41_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r202);\n      const ctx_r201 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r201.removeAttachment());\n    });\n    i0.ɵɵelement(3, \"i\", 96);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r18.previewUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MessageChatComponent_div_42_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r206 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 290);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_42_button_15_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r206);\n      const emoji_r204 = restoredCtx.$implicit;\n      const ctx_r205 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r205.insertEmoji(emoji_r204));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r204 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r204, \" \");\n  }\n}\nfunction MessageChatComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 279)(1, \"div\", 280)(2, \"button\", 281);\n    i0.ɵɵelement(3, \"i\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 282);\n    i0.ɵɵelement(5, \"i\", 283);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 282);\n    i0.ɵɵelement(7, \"i\", 284);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 282);\n    i0.ɵɵelement(9, \"i\", 285);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 282);\n    i0.ɵɵelement(11, \"i\", 286);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 282);\n    i0.ɵɵelement(13, \"i\", 287);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 288);\n    i0.ɵɵtemplate(15, MessageChatComponent_div_42_button_15_Template, 2, 1, \"button\", 289);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.commonEmojis);\n  }\n}\nfunction MessageChatComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r208 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 291)(1, \"div\", 292)(2, \"div\", 293)(3, \"div\", 294);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 138);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 295);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_43_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r208);\n      const ctx_r207 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r207.cancelReply());\n    });\n    i0.ɵɵelement(8, \"i\", 102);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9ponse \\u00E0 \", (ctx_r20.replyingToMessage.sender == null ? null : ctx_r20.replyingToMessage.sender.username) || \"Utilisateur\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.replyingToMessage.content || \"Message\", \" \");\n  }\n}\nfunction MessageChatComponent_app_voice_recorder_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r210 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-voice-recorder\", 296);\n    i0.ɵɵlistener(\"recordingComplete\", function MessageChatComponent_app_voice_recorder_52_Template_app_voice_recorder_recordingComplete_0_listener($event) {\n      i0.ɵɵrestoreView(_r210);\n      const ctx_r209 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r209.onVoiceRecordingComplete($event));\n    })(\"recordingCancelled\", function MessageChatComponent_app_voice_recorder_52_Template_app_voice_recorder_recordingCancelled_0_listener() {\n      i0.ɵɵrestoreView(_r210);\n      const ctx_r211 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r211.onVoiceRecordingCancelled());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"maxDuration\", 60);\n  }\n}\nfunction MessageChatComponent_input_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r213 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 297);\n    i0.ɵɵlistener(\"input\", function MessageChatComponent_input_53_Template_input_input_0_listener() {\n      i0.ɵɵrestoreView(_r213);\n      const ctx_r212 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r212.onTyping());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_button_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r215 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 298);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_button_54_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r215);\n      const ctx_r214 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r214.toggleVoiceRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 299);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_button_55_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 269);\n  }\n}\nfunction MessageChatComponent_button_55_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 303);\n  }\n}\nfunction MessageChatComponent_button_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 300);\n    i0.ɵɵtemplate(1, MessageChatComponent_button_55_i_1_Template, 1, 0, \"i\", 301);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_55_i_2_Template, 1, 0, \"i\", 302);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r25.isUploading || ctx_r25.messageForm.invalid && !ctx_r25.selectedFile);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r25.isUploading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.isUploading);\n  }\n}\nfunction MessageChatComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r219 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 304)(1, \"div\", 305)(2, \"div\", 306)(3, \"div\", 307);\n    i0.ɵɵelement(4, \"img\", 308);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 309);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 310);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 311)(10, \"button\", 312);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_56_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r219);\n      const ctx_r218 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r218.rejectCall());\n    });\n    i0.ɵɵelement(11, \"i\", 313);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Rejeter\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 314);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_56_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r219);\n      const ctx_r220 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r220.acceptCall());\n    });\n    i0.ɵɵelement(15, \"i\", 315);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Accepter\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", (ctx_r26.incomingCall.caller == null ? null : ctx_r26.incomingCall.caller.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r26.incomingCall.caller == null ? null : ctx_r26.incomingCall.caller.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.incomingCall.type === \"AUDIO\" ? \"Appel audio entrant\" : \"Appel vid\\u00E9o entrant\", \" \");\n  }\n}\nfunction MessageChatComponent_div_57_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 327);\n    i0.ɵɵelement(1, \"img\", 328);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MessageChatComponent_div_57_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 321);\n    i0.ɵɵelement(1, \"video\", 322, 323)(3, \"video\", 324, 325);\n    i0.ɵɵtemplate(5, MessageChatComponent_div_57_div_1_div_5_Template, 2, 1, \"div\", 326);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r221 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"display\", ctx_r221.isVideoEnabled ? \"block\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r221.isVideoEnabled);\n  }\n}\nconst _c11 = function (a0, a1, a2, a3) {\n  return {\n    \"text-green-500\": a0,\n    \"text-yellow-500\": a1,\n    \"text-red-500\": a2,\n    \"text-gray-500\": a3\n  };\n};\nfunction MessageChatComponent_div_57_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 329)(1, \"div\", 330)(2, \"div\", 331);\n    i0.ɵɵelement(3, \"img\", 332);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 333);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 334);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 335);\n    i0.ɵɵelement(9, \"i\", 336);\n    i0.ɵɵelementStart(10, \"span\", 337);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r222 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (ctx_r222.activeCall.caller == null ? null : ctx_r222.activeCall.caller.image) || (ctx_r222.activeCall.recipient == null ? null : ctx_r222.activeCall.recipient.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r222.activeCall.caller == null ? null : ctx_r222.activeCall.caller.username) || (ctx_r222.activeCall.recipient == null ? null : ctx_r222.activeCall.recipient.username), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r222.formatCallDuration(ctx_r222.callDuration));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(5, _c11, ctx_r222.callQuality === \"excellent\", ctx_r222.callQuality === \"good\", ctx_r222.callQuality === \"poor\", ctx_r222.callQuality === \"connecting\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r222.callQuality);\n  }\n}\nfunction MessageChatComponent_div_57_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 346)(1, \"span\", 347);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 348);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r228 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r228.formatCallDuration(ctx_r228.callDuration));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r228.activeCall.caller == null ? null : ctx_r228.activeCall.caller.username) || (ctx_r228.activeCall.recipient == null ? null : ctx_r228.activeCall.recipient.username), \" \");\n  }\n}\nconst _c12 = function (a0) {\n  return {\n    active: a0\n  };\n};\nfunction MessageChatComponent_div_57_div_3_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r231 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 341);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_57_div_3_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r231);\n      const ctx_r230 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r230.toggleCallVideo());\n    });\n    i0.ɵɵelement(1, \"i\", 342);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r229 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c12, !ctx_r229.isVideoEnabled));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r229.isVideoEnabled ? \"fa-video\" : \"fa-video-slash\");\n  }\n}\nconst _c13 = function (a0) {\n  return {\n    hidden: a0\n  };\n};\nfunction MessageChatComponent_div_57_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r233 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 338);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_57_div_3_div_1_Template, 5, 2, \"div\", 339);\n    i0.ɵɵelementStart(2, \"div\", 340)(3, \"button\", 341);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_57_div_3_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r233);\n      const ctx_r232 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r232.toggleCallMute());\n    });\n    i0.ɵɵelement(4, \"i\", 342);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, MessageChatComponent_div_57_div_3_button_5_Template, 2, 4, \"button\", 343);\n    i0.ɵɵelementStart(6, \"button\", 344);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_57_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r233);\n      const ctx_r234 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r234.endCall());\n    });\n    i0.ɵɵelement(7, \"i\", 313);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 345);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_57_div_3_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r233);\n      const ctx_r235 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r235.toggleCallMinimize());\n    });\n    i0.ɵɵelement(9, \"i\", 342);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r223 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c13, !ctx_r223.showCallControls));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r223.activeCall.type === \"VIDEO\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c12, ctx_r223.isCallMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r223.isCallMuted ? \"fa-microphone-slash\" : \"fa-microphone\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r223.activeCall.type === \"VIDEO\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r223.isCallMinimized ? \"fa-expand\" : \"fa-compress\");\n  }\n}\nfunction MessageChatComponent_div_57_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r237 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 349)(1, \"div\", 350)(2, \"span\", 351);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 352);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 353)(7, \"button\", 354);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_57_div_4_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r237);\n      const ctx_r236 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r236.toggleCallMinimize());\n    });\n    i0.ɵɵelement(8, \"i\", 262);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 355);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_57_div_4_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r237);\n      const ctx_r238 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r238.endCall());\n    });\n    i0.ɵɵelement(10, \"i\", 313);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r224 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r224.formatCallDuration(ctx_r224.callDuration));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r224.activeCall.caller == null ? null : ctx_r224.activeCall.caller.username) || (ctx_r224.activeCall.recipient == null ? null : ctx_r224.activeCall.recipient.username), \" \");\n  }\n}\nconst _c14 = function (a0) {\n  return {\n    minimized: a0\n  };\n};\nfunction MessageChatComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r240 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 316);\n    i0.ɵɵlistener(\"mousemove\", function MessageChatComponent_div_57_Template_div_mousemove_0_listener() {\n      i0.ɵɵrestoreView(_r240);\n      const ctx_r239 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r239.onCallMouseMove());\n    });\n    i0.ɵɵtemplate(1, MessageChatComponent_div_57_div_1_Template, 6, 3, \"div\", 317);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_57_div_2_Template, 12, 10, \"div\", 318);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_57_div_3_Template, 10, 10, \"div\", 319);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_57_div_4_Template, 11, 2, \"div\", 320);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c14, ctx_r27.isCallMinimized));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.activeCall.type === \"VIDEO\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.activeCall.type === \"AUDIO\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r27.isCallMinimized);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.isCallMinimized);\n  }\n}\nfunction MessageChatComponent_div_58_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 375)(1, \"div\", 137);\n    i0.ɵɵelement(2, \"i\", 147);\n    i0.ɵɵtext(3, \" Image jointe \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_58_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 375)(1, \"div\", 137);\n    i0.ɵɵelement(2, \"i\", 148);\n    i0.ɵɵtext(3, \" Message vocal \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_58_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r251 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 376);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_58_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r251);\n      const ctx_r250 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r250.selectAllConversations());\n    });\n    i0.ɵɵtext(1, \" Tout s\\u00E9lectionner \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_58_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r253 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 377);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_58_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r253);\n      const ctx_r252 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r252.deselectAllConversations());\n    });\n    i0.ɵɵtext(1, \" Tout d\\u00E9s\\u00E9lectionner \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_58_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125)(1, \"div\", 378);\n    i0.ɵɵelement(2, \"div\", 100);\n    i0.ɵɵelementStart(3, \"span\", 170);\n    i0.ɵɵtext(4, \"Chargement des conversations...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_58_div_26_button_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 387);\n  }\n}\nfunction MessageChatComponent_div_58_div_26_button_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const conversation_r255 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2022 \", conversation_r255.participants.length, \" participant(s)\");\n  }\n}\nconst _c15 = function (a0, a1) {\n  return {\n    \"border-[#4f5fad] dark:border-[#6d78c9] bg-[#4f5fad] dark:bg-[#6d78c9]\": a0,\n    \"border-[#edf1f4] dark:border-[#3a3a3a] bg-transparent\": a1\n  };\n};\nfunction MessageChatComponent_div_58_div_26_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r260 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 380);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_58_div_26_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r260);\n      const conversation_r255 = restoredCtx.$implicit;\n      const ctx_r259 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(conversation_r255.id && ctx_r259.toggleConversationSelection(conversation_r255.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 133)(2, \"div\", 381);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_58_div_26_button_1_i_3_Template, 1, 0, \"i\", 382);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 133);\n    i0.ɵɵelement(5, \"img\", 383);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 384)(7, \"div\", 385);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 386);\n    i0.ɵɵtext(10);\n    i0.ɵɵtemplate(11, MessageChatComponent_div_58_div_26_button_1_span_11_Template, 2, 1, \"span\", 139);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const conversation_r255 = ctx.$implicit;\n    const ctx_r254 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c15, conversation_r255.id && ctx_r254.isConversationSelected(conversation_r255.id), !conversation_r255.id || !ctx_r254.isConversationSelected(conversation_r255.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", conversation_r255.id && ctx_r254.isConversationSelected(conversation_r255.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r254.getConversationDisplayImage(conversation_r255), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r254.getConversationDisplayName(conversation_r255));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r254.getConversationDisplayName(conversation_r255), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", conversation_r255.isGroup ? \"Groupe\" : \"Conversation priv\\u00E9e\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", conversation_r255.participants);\n  }\n}\nfunction MessageChatComponent_div_58_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, MessageChatComponent_div_58_div_26_button_1_Template, 12, 10, \"button\", 379);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r246 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r246.availableConversations);\n  }\n}\nfunction MessageChatComponent_div_58_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125)(1, \"div\", 126);\n    i0.ɵɵelement(2, \"i\", 388);\n    i0.ɵɵelementStart(3, \"div\", 128);\n    i0.ɵɵtext(4, \"Aucune autre conversation disponible\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_58_i_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 303);\n  }\n}\nfunction MessageChatComponent_div_58_i_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 389);\n  }\n}\nfunction MessageChatComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r262 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 356);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_58_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r262);\n      const ctx_r261 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r261.closeForwardModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 357);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_58_Template_div_click_1_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"div\", 358)(3, \"h3\", 359);\n    i0.ɵɵtext(4, \" Transf\\u00E9rer le message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 360);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_58_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r262);\n      const ctx_r264 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r264.closeForwardModal());\n    });\n    i0.ɵɵelement(6, \"i\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 361)(8, \"div\", 362);\n    i0.ɵɵtext(9, \" Message \\u00E0 transf\\u00E9rer : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 363)(11, \"div\", 364);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, MessageChatComponent_div_58_div_13_Template, 4, 0, \"div\", 365);\n    i0.ɵɵtemplate(14, MessageChatComponent_div_58_div_14_Template, 4, 0, \"div\", 365);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 366)(16, \"div\", 367)(17, \"span\", 120);\n    i0.ɵɵtext(18, \" S\\u00E9lectionner les conversations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 218);\n    i0.ɵɵtemplate(20, MessageChatComponent_div_58_button_20_Template, 2, 0, \"button\", 368);\n    i0.ɵɵtemplate(21, MessageChatComponent_div_58_button_21_Template, 2, 0, \"button\", 369);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 137);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 370);\n    i0.ɵɵtemplate(25, MessageChatComponent_div_58_div_25_Template, 5, 0, \"div\", 123);\n    i0.ɵɵtemplate(26, MessageChatComponent_div_58_div_26_Template, 2, 1, \"div\", 139);\n    i0.ɵɵtemplate(27, MessageChatComponent_div_58_div_27_Template, 5, 0, \"div\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 371)(29, \"button\", 372);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_58_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r262);\n      const ctx_r265 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r265.closeForwardModal());\n    });\n    i0.ɵɵtext(30, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 373);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_58_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r262);\n      const ctx_r266 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r266.forwardMessage());\n    });\n    i0.ɵɵtemplate(32, MessageChatComponent_div_58_i_32_Template, 1, 0, \"i\", 302);\n    i0.ɵɵtemplate(33, MessageChatComponent_div_58_i_33_Template, 1, 0, \"i\", 374);\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r28.forwardingMessage == null ? null : ctx_r28.forwardingMessage.content) || \"Message sans contenu\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.hasImage(ctx_r28.forwardingMessage));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.isVoiceMessage(ctx_r28.forwardingMessage));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r28.areAllConversationsSelected());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.areAllConversationsSelected());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r28.selectedConversations.length, \" conversation(s) s\\u00E9lectionn\\u00E9e(s) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.isLoadingConversations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r28.isLoadingConversations && ctx_r28.availableConversations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r28.isLoadingConversations && ctx_r28.availableConversations.length === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r28.selectedConversations.length === 0 || ctx_r28.isForwarding);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.isForwarding);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r28.isForwarding);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r28.isForwarding ? \"Transfert...\" : \"Transf\\u00E9rer\");\n  }\n}\nfunction MessageChatComponent_div_59_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 415);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r267 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r267.unreadNotificationCount, \" \");\n  }\n}\nfunction MessageChatComponent_div_59_div_23_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r278 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 423)(1, \"button\", 424);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_div_23_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r278);\n      const ctx_r277 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r277.markSelectedAsRead());\n    });\n    i0.ɵɵelement(2, \"i\", 248);\n    i0.ɵɵtext(3, \" Marquer comme lu \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 425);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_div_23_div_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r278);\n      const ctx_r279 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r279.showDeleteSelectedConfirmation());\n    });\n    i0.ɵɵelement(5, \"i\", 426);\n    i0.ɵɵtext(6, \" Supprimer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r276 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r276.isMarkingAsRead);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r276.isDeletingNotifications);\n  }\n}\nfunction MessageChatComponent_div_59_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r281 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 416)(1, \"div\", 417)(2, \"label\", 418)(3, \"input\", 419);\n    i0.ɵɵlistener(\"change\", function MessageChatComponent_div_59_div_23_Template_input_change_3_listener() {\n      i0.ɵɵrestoreView(_r281);\n      const ctx_r280 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r280.toggleSelectAllNotifications());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"span\", 420);\n    i0.ɵɵelementStart(5, \"span\", 421);\n    i0.ɵɵtext(6, \"Tout s\\u00E9lectionner\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, MessageChatComponent_div_59_div_23_div_7_Template, 7, 2, \"div\", 422);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r268 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r268.areAllNotificationsSelected());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r268.selectedNotifications.size > 0);\n  }\n}\nfunction MessageChatComponent_div_59_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r283 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 427);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r283);\n      const ctx_r282 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r282.markAllAsRead());\n    });\n    i0.ɵɵelement(1, \"i\", 247);\n    i0.ɵɵtext(2, \" Tout marquer comme lu \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r269 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r269.isMarkingAsRead);\n  }\n}\nconst _c16 = function (a0) {\n  return {\n    \"fa-spin\": a0\n  };\n};\nfunction MessageChatComponent_div_59_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r285 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 428);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r285);\n      const ctx_r284 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r284.deleteAllNotifications());\n    });\n    i0.ɵɵelement(1, \"i\", 429);\n    i0.ɵɵtext(2, \" Supprimer tout \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r270 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r270.isDeletingNotifications);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c16, ctx_r270.isDeletingNotifications));\n  }\n}\nfunction MessageChatComponent_div_59_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 430);\n    i0.ɵɵelement(1, \"i\", 303);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Chargement des notifications...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_59_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 431);\n    i0.ɵɵelement(1, \"i\", 432);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Aucune notification\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_59_div_30_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 447);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r286 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", notification_r286.senderId.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_59_div_30_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 448);\n    i0.ɵɵelement(1, \"i\", 248);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_59_div_30_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r292 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 449);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_div_30_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r292);\n      const notification_r286 = i0.ɵɵnextContext().$implicit;\n      const ctx_r291 = i0.ɵɵnextContext(2);\n      ctx_r291.markSelectedAsRead();\n      ctx_r291.selectedNotifications.clear();\n      return i0.ɵɵresetView(ctx_r291.selectedNotifications.add(notification_r286.id));\n    });\n    i0.ɵɵelement(1, \"i\", 248);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c17 = function (a0, a1) {\n  return {\n    unread: a0,\n    selected: a1\n  };\n};\nfunction MessageChatComponent_div_59_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r295 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 433)(1, \"div\", 434)(2, \"input\", 419);\n    i0.ɵɵlistener(\"change\", function MessageChatComponent_div_59_div_30_Template_input_change_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r295);\n      const notification_r286 = restoredCtx.$implicit;\n      const ctx_r294 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r294.toggleNotificationSelection(notification_r286.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 420);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 435)(5, \"div\", 436);\n    i0.ɵɵelement(6, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 437)(8, \"div\", 438);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_59_div_30_span_9_Template, 2, 1, \"span\", 439);\n    i0.ɵɵelementStart(10, \"span\", 440);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 441)(13, \"span\", 442);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, MessageChatComponent_div_59_div_30_span_15_Template, 2, 0, \"span\", 443);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 444);\n    i0.ɵɵtemplate(17, MessageChatComponent_div_59_div_30_button_17_Template, 2, 0, \"button\", 445);\n    i0.ɵɵelementStart(18, \"button\", 446);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_div_30_Template_button_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r295);\n      const notification_r286 = restoredCtx.$implicit;\n      const ctx_r296 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r296.deleteNotification(notification_r286.id));\n    });\n    i0.ɵɵelement(19, \"i\", 426);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const notification_r286 = ctx.$implicit;\n    const ctx_r273 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(10, _c17, !notification_r286.isRead, ctx_r273.selectedNotifications.has(notification_r286.id)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r273.selectedNotifications.has(notification_r286.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r273.getNotificationColor(notification_r286.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r273.getNotificationIcon(notification_r286.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", notification_r286.senderId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r286.content);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r273.formatNotificationDate(notification_r286.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r286.isRead);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !notification_r286.isRead);\n  }\n}\nfunction MessageChatComponent_div_59_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r298 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 450)(1, \"button\", 451);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r298);\n      const ctx_r297 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r297.loadMoreNotifications());\n    });\n    i0.ɵɵelement(2, \"i\", 452);\n    i0.ɵɵtext(3, \" Charger plus \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r274 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r274.isLoadingNotifications);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c16, ctx_r274.isLoadingNotifications));\n  }\n}\nfunction MessageChatComponent_div_59_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r300 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 453)(1, \"div\", 454)(2, \"h4\");\n    i0.ɵɵtext(3, \"Param\\u00E8tres de notification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 455);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_div_32_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r300);\n      const ctx_r299 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r299.toggleNotificationSettings());\n    });\n    i0.ɵɵelement(5, \"i\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 456)(7, \"div\", 457)(8, \"label\", 458)(9, \"input\", 459);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_59_div_32_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r300);\n      const ctx_r301 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r301.notificationSounds = $event);\n    })(\"change\", function MessageChatComponent_div_59_div_32_Template_input_change_9_listener() {\n      i0.ɵɵrestoreView(_r300);\n      const ctx_r302 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r302.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"span\", 460);\n    i0.ɵɵelementStart(11, \"span\", 461);\n    i0.ɵɵtext(12, \"Sons de notification\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 457)(14, \"label\", 458)(15, \"input\", 459);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_59_div_32_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r300);\n      const ctx_r303 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r303.notificationPreview = $event);\n    })(\"change\", function MessageChatComponent_div_59_div_32_Template_input_change_15_listener() {\n      i0.ɵɵrestoreView(_r300);\n      const ctx_r304 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r304.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"span\", 460);\n    i0.ɵɵelementStart(17, \"span\", 461);\n    i0.ɵɵtext(18, \"Aper\\u00E7u des notifications\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 457)(20, \"label\", 458)(21, \"input\", 459);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_59_div_32_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r300);\n      const ctx_r305 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r305.autoMarkAsRead = $event);\n    })(\"change\", function MessageChatComponent_div_59_div_32_Template_input_change_21_listener() {\n      i0.ɵɵrestoreView(_r300);\n      const ctx_r306 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r306.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"span\", 460);\n    i0.ɵɵelementStart(23, \"span\", 461);\n    i0.ɵɵtext(24, \"Marquer automatiquement comme lu\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r275 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r275.notificationSounds);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r275.notificationPreview);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r275.autoMarkAsRead);\n  }\n}\nfunction MessageChatComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r308 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 390)(1, \"div\", 391)(2, \"div\", 392)(3, \"div\", 393);\n    i0.ɵɵelement(4, \"i\", 394);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"Notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, MessageChatComponent_div_59_span_7_Template, 2, 1, \"span\", 395);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 396)(9, \"button\", 397);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r308);\n      const ctx_r307 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r307.toggleNotificationSettings());\n    });\n    i0.ɵɵelement(10, \"i\", 398);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 399);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r308);\n      const ctx_r309 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r309.loadNotifications(true));\n    });\n    i0.ɵɵelement(12, \"i\", 400);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 401);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r308);\n      const ctx_r310 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r310.toggleNotificationPanel());\n    });\n    i0.ɵɵelement(14, \"i\", 96);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 402)(16, \"div\", 403)(17, \"button\", 404);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r308);\n      const ctx_r311 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r311.setNotificationFilter(\"all\"));\n    });\n    i0.ɵɵtext(18, \" Toutes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 404);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r308);\n      const ctx_r312 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r312.setNotificationFilter(\"unread\"));\n    });\n    i0.ɵɵtext(20, \" Non lues \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 404);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r308);\n      const ctx_r313 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r313.setNotificationFilter(\"read\"));\n    });\n    i0.ɵɵtext(22, \" Lues \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, MessageChatComponent_div_59_div_23_Template, 8, 2, \"div\", 405);\n    i0.ɵɵelementStart(24, \"div\", 406);\n    i0.ɵɵtemplate(25, MessageChatComponent_div_59_button_25_Template, 3, 1, \"button\", 407);\n    i0.ɵɵtemplate(26, MessageChatComponent_div_59_button_26_Template, 3, 4, \"button\", 408);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 409);\n    i0.ɵɵtemplate(28, MessageChatComponent_div_59_div_28_Template, 4, 0, \"div\", 410);\n    i0.ɵɵtemplate(29, MessageChatComponent_div_59_div_29_Template, 4, 0, \"div\", 411);\n    i0.ɵɵtemplate(30, MessageChatComponent_div_59_div_30_Template, 20, 13, \"div\", 412);\n    i0.ɵɵtemplate(31, MessageChatComponent_div_59_div_31_Template, 4, 4, \"div\", 413);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, MessageChatComponent_div_59_div_32_Template, 25, 3, \"div\", 414);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.unreadNotificationCount > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r29.isLoadingNotifications);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c16, ctx_r29.isLoadingNotifications));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c12, ctx_r29.notificationFilter === \"all\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c12, ctx_r29.notificationFilter === \"unread\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c12, ctx_r29.notificationFilter === \"read\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.getFilteredNotifications().length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.unreadNotificationCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.notifications.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.isLoadingNotifications && ctx_r29.notifications.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r29.isLoadingNotifications && ctx_r29.getFilteredNotifications().length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r29.getFilteredNotifications())(\"ngForTrackBy\", ctx_r29.trackByNotificationId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.hasMoreNotifications);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.showNotificationSettings);\n  }\n}\nfunction MessageChatComponent_div_60_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r315 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r315.getOnlineUsersCount(), \" en ligne)\");\n  }\n}\nfunction MessageChatComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r317 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 462);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_60_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r317);\n      const panel_r314 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(panel_r314.closeAction());\n    });\n    i0.ɵɵelementStart(1, \"div\", 463);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_60_Template_div_click_1_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"div\", 464)(3, \"h3\");\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵtext(5);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_60_span_6_Template, 2, 1, \"span\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 465);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_60_Template_button_click_7_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r317);\n      const panel_r314 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(panel_r314.closeAction());\n    });\n    i0.ɵɵelement(8, \"i\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 466)(10, \"div\", 467);\n    i0.ɵɵelement(11, \"i\");\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Fonctionnalit\\u00E9 en cours de d\\u00E9veloppement\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const panel_r314 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(panel_r314.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", panel_r314.title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", panel_r314.key === \"userStatus\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMap(panel_r314.icon);\n  }\n}\nfunction MessageChatComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r321 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 468)(1, \"div\", 469)(2, \"div\", 470);\n    i0.ɵɵelement(3, \"i\", 167);\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Confirmer la suppression\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 471)(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 472);\n    i0.ɵɵtext(10, \"Cette action est irr\\u00E9versible.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 473)(12, \"button\", 474);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_61_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r321);\n      const ctx_r320 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r320.cancelDeleteNotifications());\n    });\n    i0.ɵɵtext(13, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 475);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_61_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r321);\n      const ctx_r322 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r322.deleteSelectedNotifications());\n    });\n    i0.ɵɵelement(15, \"i\", 476);\n    i0.ɵɵtext(16, \" Supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \\u00CAtes-vous s\\u00FBr de vouloir supprimer \", ctx_r31.selectedNotifications.size, \" notification(s) ? \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r31.isDeletingNotifications);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c16, ctx_r31.isDeletingNotifications));\n  }\n}\nconst _c18 = function (a0) {\n  return {\n    \"text-[#4f5fad] dark:text-[#6d78c9]\": a0\n  };\n};\nconst _c19 = function (a0) {\n  return {\n    \"with-search-bar\": a0\n  };\n};\nexport let MessageChatComponent = /*#__PURE__*/(() => {\n  class MessageChatComponent {\n    // Utiliser les emojis du service au lieu de dupliquer\n    get commonEmojis() {\n      return this.MessageService.getCommonEmojis();\n    }\n    /**\n     * Configuration consolidée des boutons d'action de l'en-tête\n     */\n    getHeaderActions() {\n      return [{\n        class: 'btn-audio-call',\n        icon: 'fas fa-phone-alt',\n        title: 'Appel audio',\n        onClick: () => this.initiateCall('AUDIO'),\n        isActive: false\n      }, {\n        class: 'btn-video-call',\n        icon: 'fas fa-video',\n        title: 'Appel vidéo',\n        onClick: () => this.initiateCall('VIDEO'),\n        isActive: false\n      }, {\n        class: 'btn-search',\n        icon: 'fas fa-search',\n        title: 'Rechercher',\n        onClick: () => this.toggleSearchBar(),\n        isActive: this.showSearchBar,\n        activeClass: {\n          'text-[#4f5fad] dark:text-[#6d78c9]': true\n        }\n      }, {\n        class: 'btn-pinned relative',\n        icon: 'fas fa-thumbtack',\n        title: `Messages épinglés (${this.getPinnedMessagesCount()})`,\n        onClick: () => this.togglePinnedMessages(),\n        isActive: this.showPinnedMessages,\n        activeClass: {\n          'text-[#4f5fad] dark:text-[#6d78c9]': true\n        },\n        badge: this.getPinnedMessagesCount() > 0 ? {\n          count: this.getPinnedMessagesCount(),\n          class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',\n          animate: false\n        } : null\n      }, {\n        class: 'btn-notifications relative',\n        icon: 'fas fa-bell',\n        title: 'Notifications',\n        onClick: () => this.toggleNotificationPanel(),\n        isActive: this.showNotificationPanel,\n        activeClass: {\n          'text-[#4f5fad] dark:text-[#6d78c9]': true\n        },\n        badge: this.unreadNotificationCount > 0 ? {\n          count: this.unreadNotificationCount,\n          class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',\n          animate: true\n        } : null\n      }, {\n        class: 'btn-history relative',\n        icon: 'fas fa-history',\n        title: 'Historique des appels',\n        onClick: () => this.toggleCallHistoryPanel(),\n        isActive: this.showCallHistoryPanel,\n        activeClass: {\n          'text-[#4f5fad] dark:text-[#6d78c9]': true\n        }\n      }, {\n        class: 'btn-stats relative',\n        icon: 'fas fa-chart-bar',\n        title: \"Statistiques d'appels\",\n        onClick: () => this.toggleCallStatsPanel(),\n        isActive: this.showCallStatsPanel,\n        activeClass: {\n          'text-[#4f5fad] dark:text-[#6d78c9]': true\n        }\n      }, {\n        class: 'btn-voice-messages relative',\n        icon: 'fas fa-microphone',\n        title: 'Messages vocaux',\n        onClick: () => this.toggleVoiceMessagesPanel(),\n        isActive: this.showVoiceMessagesPanel,\n        activeClass: {\n          'text-[#4f5fad] dark:text-[#6d78c9]': true\n        },\n        badge: this.voiceMessages.length > 0 ? {\n          count: this.voiceMessages.length,\n          class: 'bg-[#4f5fad]',\n          animate: false\n        } : null\n      }];\n    }\n    constructor(MessageService, route, authService, fb, statusService, router, toastService, logger, cdr) {\n      this.MessageService = MessageService;\n      this.route = route;\n      this.authService = authService;\n      this.fb = fb;\n      this.statusService = statusService;\n      this.router = router;\n      this.toastService = toastService;\n      this.logger = logger;\n      this.cdr = cdr;\n      this.messages = [];\n      this.conversation = null;\n      this.loading = true;\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n      this.otherParticipant = null;\n      this.selectedFile = null;\n      this.previewUrl = null;\n      this.isUploading = false;\n      this.isTyping = false;\n      this.isRecordingVoice = false;\n      this.voiceRecordingDuration = 0;\n      this.MAX_MESSAGES_PER_SIDE = 5; // Nombre maximum de messages à afficher par côté (expéditeur/destinataire)\n      this.MAX_MESSAGES_TO_LOAD = 10; // Nombre maximum de messages à charger à la fois (pagination)\n      this.MAX_TOTAL_MESSAGES = 100; // Limite totale de messages à conserver en mémoire\n      this.currentPage = 1; // Page actuelle pour la pagination\n      this.isLoadingMore = false; // Indicateur de chargement en cours (public pour le template)\n      this.hasMoreMessages = true; // Indique s'il y a plus de messages à charger (public pour le template)\n      this.subscriptions = new Subscription();\n      // Variables supprimées pour optimisation\n      // Variables pour le sélecteur de thème\n      this.selectedTheme = 'theme-default'; // Thème par défaut\n      this.showThemeSelector = false; // Affichage du sélecteur de thème\n      this.showMainMenu = false; // Affichage du menu principal\n      // Variables pour le sélecteur d'émojis\n      this.showEmojiPicker = false;\n      // Variables pour les appels (simplifiées)\n      this.incomingCall = null;\n      this.activeCall = null;\n      // Variables pour les notifications (simplifiées)\n      this.notifications = [];\n      this.showNotificationPanel = false;\n      this.unreadNotificationCount = 0;\n      this.selectedNotifications = new Set();\n      this.showDeleteConfirmModal = false;\n      this.isDeletingNotifications = false;\n      // Variables pour le statut utilisateur (simplifiées)\n      this.onlineUsers = new Map();\n      this.currentUserStatus = 'online';\n      this.lastActivityTime = new Date();\n      this.autoAwayTimeout = null;\n      this.autoAwayDelay = 300000; // 5 minutes\n      // Variables pour l'historique d'appels (simplifiées)\n      this.callHistory = [];\n      this.voiceMessages = [];\n      // Variables consolidées pour l'interface\n      this.showCallModal = false;\n      this.showActiveCallModal = false;\n      this.localVideoElement = null;\n      this.remoteVideoElement = null;\n      this.isCallMuted = false;\n      this.isVideoEnabled = true;\n      this.isCallMinimized = false;\n      this.callQuality = 'connecting';\n      this.callDuration = 0;\n      this.callTimer = null;\n      // Variables pour les messages\n      this.showMessageOptions = {};\n      this.editingMessageId = null;\n      this.editingContent = '';\n      this.replyingToMessage = null;\n      // Variables pour les notifications\n      this.isLoadingNotifications = false;\n      this.notificationFilter = 'all';\n      this.isMarkingAsRead = false;\n      // Variables pour la recherche\n      this.showSearchBar = false;\n      this.searchQuery = '';\n      this.isSearching = false;\n      this.searchMode = false;\n      this.searchResults = [];\n      // Variables pour les panneaux et fonctionnalités\n      this.showPinnedMessages = false;\n      this.pinnedMessages = [];\n      this.showStatusSelector = false;\n      this.isUpdatingStatus = false;\n      this.showCallHistoryPanel = false;\n      this.showCallStatsPanel = false;\n      this.showVoiceMessagesPanel = false;\n      this.showReactionPicker = {};\n      this.availableReactions = ['👍', '❤️', '😂', '😮', '😢', '😡'];\n      this.showDeleteConfirm = {};\n      this.showPinConfirm = {};\n      this.isPinning = {};\n      this.showCallControls = false;\n      this.showForwardModal = false;\n      this.forwardingMessage = null;\n      this.selectedConversations = [];\n      this.isLoadingConversations = false;\n      this.availableConversations = [];\n      this.isForwarding = false;\n      this.hasMoreNotifications = false;\n      this.showNotificationSettings = false;\n      this.notificationSounds = true;\n      this.notificationPreview = true;\n      this.autoMarkAsRead = true;\n      this.showUserStatusPanel = false;\n      this.statusFilterType = 'all';\n      this.userStatusMap = new Map();\n      this.showDetailedStatus = false;\n      this.statusHistory = [];\n      this.isLoadingCallHistory = false;\n      this.hasMoreCallHistory = false;\n      this.callHistoryFilters = {\n        status: []\n      };\n      this.isLoadingCallStats = false;\n      this.callStats = null;\n      this.isLoadingVoiceMessages = false;\n      this.isCurrentlyTyping = false;\n      this.TYPING_DELAY = 500; // Délai en ms avant d'envoyer l'événement de frappe\n      this.TYPING_TIMEOUT = 3000; // Délai en ms avant d'arrêter l'indicateur de frappe\n      this.toggleConversationInfo = () => {\n        this.showMainMenu = false;\n        this.toastService.showInfo('Fonctionnalité en cours de développement');\n      };\n      this.toggleConversationSettings = () => {\n        this.showMainMenu = false;\n        this.toastService.showInfo('Fonctionnalité en cours de développement');\n      };\n      // Méthodes déléguées au service (consolidées)\n      this.formatMessageTime = timestamp => this.MessageService.formatMessageTime(timestamp);\n      this.formatLastActive = lastActive => this.MessageService.formatLastActive(lastActive);\n      this.formatMessageDate = timestamp => this.MessageService.formatMessageDate(timestamp);\n      this.shouldShowDateHeader = index => this.MessageService.shouldShowDateHeader(this.messages, index);\n      this.getMessageType = message => this.MessageService.getMessageType(message);\n      this.hasImage = message => this.MessageService.hasImage(message);\n      this.isVoiceMessage = message => this.MessageService.isVoiceMessage(message);\n      this.getVoiceMessageUrl = message => this.MessageService.getVoiceMessageUrl(message);\n      this.getVoiceMessageDuration = message => this.MessageService.getVoiceMessageDuration(message);\n      this.getVoiceBarHeight = index => this.MessageService.getVoiceBarHeight(index);\n      this.formatVoiceDuration = seconds => this.MessageService.formatVoiceDuration(seconds);\n      this.getImageUrl = message => this.MessageService.getImageUrl(message);\n      this.getMessageTypeClass = message => this.MessageService.getMessageTypeClass(message, this.currentUserId);\n      // Méthodes utilitaires ultra-consolidées pour les notifications\n      this.notificationConfig = {\n        NEW_MESSAGE: {\n          icon: 'fas fa-comment',\n          color: 'text-blue-500'\n        },\n        FRIEND_REQUEST: {\n          icon: 'fas fa-user-plus',\n          color: 'text-green-500'\n        },\n        GROUP_INVITATION: {\n          icon: 'fas fa-users',\n          color: 'text-purple-500'\n        },\n        CALL_MISSED: {\n          icon: 'fas fa-phone-slash',\n          color: 'text-red-500'\n        },\n        CALL_INCOMING: {\n          icon: 'fas fa-phone',\n          color: 'text-yellow-500'\n        },\n        SYSTEM: {\n          icon: 'fas fa-cog',\n          color: 'text-gray-500'\n        }\n      };\n      this.formatNotificationDate = timestamp => this.MessageService.formatLastActive(timestamp);\n      this.getNotificationIcon = type => this.notificationConfig[type]?.icon || 'fas fa-bell';\n      this.getNotificationColor = type => this.notificationConfig[type]?.color || 'text-cyan-500';\n      this.trackByNotificationId = (index, notification) => notification.id;\n      // Configuration ultra-consolidée des statuts\n      this.statusConfig = {\n        online: {\n          text: 'En ligne',\n          color: 'text-green-500',\n          icon: 'fas fa-circle',\n          label: 'En ligne',\n          description: 'Disponible pour discuter'\n        },\n        offline: {\n          text: 'Hors ligne',\n          color: 'text-gray-500',\n          icon: 'far fa-circle',\n          label: 'Hors ligne',\n          description: 'Invisible pour tous'\n        },\n        away: {\n          text: 'Absent',\n          color: 'text-yellow-500',\n          icon: 'fas fa-clock',\n          label: 'Absent',\n          description: 'Absent temporairement'\n        },\n        busy: {\n          text: 'Occupé',\n          color: 'text-red-500',\n          icon: 'fas fa-minus-circle',\n          label: 'Occupé',\n          description: 'Ne pas déranger'\n        }\n      };\n      // Configuration des thèmes consolidée\n      this.themeConfig = [{\n        key: 'theme-default',\n        label: 'Par défaut',\n        color: '[#4f5fad]',\n        hoverColor: '[#4f5fad]'\n      }, {\n        key: 'theme-feminine',\n        label: 'Rose',\n        color: '[#ff6b9d]',\n        hoverColor: '[#ff6b9d]'\n      }, {\n        key: 'theme-masculine',\n        label: 'Bleu',\n        color: '[#3d85c6]',\n        hoverColor: '[#3d85c6]'\n      }, {\n        key: 'theme-neutral',\n        label: 'Vert',\n        color: '[#6aa84f]',\n        hoverColor: '[#6aa84f]'\n      }];\n      // Configuration des panneaux consolidée\n      this.panelConfig = [{\n        key: 'userStatus',\n        show: () => this.showUserStatusPanel,\n        title: 'Utilisateurs',\n        icon: 'fas fa-users',\n        closeAction: () => this.showUserStatusPanel = false\n      }, {\n        key: 'callHistory',\n        show: () => this.showCallHistoryPanel,\n        title: 'Historique des appels',\n        icon: 'fas fa-history',\n        closeAction: () => this.showCallHistoryPanel = false\n      }, {\n        key: 'callStats',\n        show: () => this.showCallStatsPanel,\n        title: \"Statistiques d'appels\",\n        icon: 'fas fa-chart-bar',\n        closeAction: () => this.showCallStatsPanel = false\n      }, {\n        key: 'voiceMessages',\n        show: () => this.showVoiceMessagesPanel,\n        title: 'Messages vocaux',\n        icon: 'fas fa-microphone',\n        closeAction: () => this.showVoiceMessagesPanel = false\n      }];\n      this.getStatusText = status => this.statusConfig[status]?.text || 'Inconnu';\n      this.getStatusColor = status => this.statusConfig[status]?.color || 'text-gray-400';\n      this.getStatusIcon = status => this.statusConfig[status]?.icon || 'fas fa-question-circle';\n      // Méthodes vides ultra-consolidées\n      this.emptyMethods = {\n        loadCallHistory: () => {},\n        setCallHistoryFilters: filters => {},\n        loadMoreCallHistory: () => {},\n        loadCallStats: () => {},\n        loadVoiceMessages: () => {}\n      };\n      this.loadCallHistory = this.emptyMethods.loadCallHistory;\n      this.setCallHistoryFilters = this.emptyMethods.setCallHistoryFilters;\n      this.loadMoreCallHistory = this.emptyMethods.loadMoreCallHistory;\n      this.loadCallStats = this.emptyMethods.loadCallStats;\n      this.loadVoiceMessages = this.emptyMethods.loadVoiceMessages;\n      // Méthodes d'épinglage consolidées\n      this.getPinIcon = message => this.isMessagePinned(message) ? 'fas fa-thumbtack' : 'far fa-thumbtack';\n      this.getPinDisplayText = message => this.isMessagePinned(message) ? 'Désépingler' : 'Épingler';\n      this.canEditMessage = message => message.sender?.id === this.currentUserId;\n      this.isMessagePinned = message => message.isPinned || false;\n      this.saveEditMessage = messageId => this.cancelEditMessage();\n      // Méthodes utilitaires consolidées pour les appels\n      this.getCallStatusColor = status => {\n        const colors = {\n          COMPLETED: 'text-green-500',\n          MISSED: 'text-red-500',\n          REJECTED: 'text-orange-500'\n        };\n        return colors[status] || 'text-gray-500';\n      };\n      this.getCallTypeIcon = type => type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone';\n      this.formatCallDuration = duration => {\n        if (!duration) return '00:00';\n        const minutes = Math.floor(duration / 60);\n        const seconds = duration % 60;\n        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n      };\n      this.formatCallDate = timestamp => this.MessageService.formatMessageDate(timestamp);\n      this.trackByCallId = (index, call) => call.id || index.toString();\n      // Méthodes utilitaires ultra-consolidées avec configuration\n      this.utilityMethods = {\n        getUniqueReactions: message => message.reactions || [],\n        onReactionClick: (messageId, emoji) => {},\n        hasUserReacted: (message, emoji) => false,\n        onCallMouseMove: () => this.showCallControls = true,\n        areAllConversationsSelected: () => this.selectedConversations.length === this.availableConversations.length,\n        selectAllConversations: () => this.selectedConversations = this.availableConversations.map(c => c.id),\n        deselectAllConversations: () => this.selectedConversations = []\n      };\n      this.getUniqueReactions = this.utilityMethods.getUniqueReactions;\n      this.onReactionClick = this.utilityMethods.onReactionClick;\n      this.hasUserReacted = this.utilityMethods.hasUserReacted;\n      this.onCallMouseMove = this.utilityMethods.onCallMouseMove;\n      this.areAllConversationsSelected = this.utilityMethods.areAllConversationsSelected;\n      this.selectAllConversations = this.utilityMethods.selectAllConversations;\n      this.deselectAllConversations = this.utilityMethods.deselectAllConversations;\n      this.isConversationSelected = conversationId => this.selectedConversations.includes(conversationId);\n      this.getConversationDisplayImage = conversation => conversation.image || 'assets/images/default-avatar.png';\n      this.getConversationDisplayName = conversation => conversation.name || 'Conversation';\n      // Méthodes de toggle ultra-consolidées avec configuration\n      this.toggleConfig = {\n        notificationSettings: () => this.showNotificationSettings = !this.showNotificationSettings,\n        userStatusPanel: () => this.showUserStatusPanel = !this.showUserStatusPanel,\n        pinnedMessages: () => this.showPinnedMessages = !this.showPinnedMessages,\n        callHistoryPanel: () => this.showCallHistoryPanel = !this.showCallHistoryPanel,\n        callStatsPanel: () => this.showCallStatsPanel = !this.showCallStatsPanel,\n        voiceMessagesPanel: () => this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel,\n        searchBar: () => {\n          this.togglePanel('search');\n          if (!this.showSearchBar) this.clearSearch();\n        },\n        statusSelector: () => this.togglePanel('status')\n      };\n      // Méthodes de toggle simplifiées\n      this.toggleNotificationSettings = () => this.toggleConfig.notificationSettings();\n      this.toggleUserStatusPanel = () => this.toggleConfig.userStatusPanel();\n      this.togglePinnedMessages = () => this.toggleConfig.pinnedMessages();\n      this.toggleCallHistoryPanel = () => this.toggleConfig.callHistoryPanel();\n      this.toggleCallStatsPanel = () => this.toggleConfig.callStatsPanel();\n      this.toggleVoiceMessagesPanel = () => this.toggleConfig.voiceMessagesPanel();\n      this.toggleSearchBar = () => this.toggleConfig.searchBar();\n      this.toggleStatusSelector = () => this.toggleConfig.statusSelector();\n      this.saveNotificationSettings = () => {};\n      // Méthodes finales ultra-consolidées\n      this.navigateToMessage = messageId => {};\n      this.scrollToPinnedMessage = messageId => {};\n      this.getPinnedMessagesCount = () => this.pinnedMessages.length;\n      this.highlightSearchTerms = (content, query) => {\n        if (!query) return content;\n        const regex = new RegExp(`(${query})`, 'gi');\n        return content.replace(regex, '<mark>$1</mark>');\n      };\n      this.messageForm = this.fb.group({\n        content: ['', [Validators.maxLength(1000)]]\n      });\n    }\n    ngOnInit() {\n      this.currentUserId = this.authService.getCurrentUserId();\n      const savedTheme = localStorage.getItem('chat-theme');\n      if (savedTheme) {\n        this.selectedTheme = savedTheme;\n      }\n      // Chargement des messages vocaux simplifié\n      this.subscribeToNotifications();\n      this.subscribeToUserStatus();\n      this.initializeUserStatus();\n      this.startActivityTracking();\n      document.addEventListener('click', this.onDocumentClick.bind(this));\n      const routeSub = this.route.params.pipe(filter(params => params['id']), distinctUntilChanged(), switchMap(params => {\n        this.loading = true;\n        this.messages = [];\n        this.currentPage = 1;\n        this.hasMoreMessages = true;\n        return this.MessageService.getConversation(params['id'], this.MAX_MESSAGES_TO_LOAD, this.currentPage);\n      })).subscribe({\n        next: conversation => {\n          this.handleConversationLoaded(conversation);\n        },\n        error: error => {\n          this.handleError('Failed to load conversation', error);\n        }\n      });\n      this.subscriptions.add(routeSub);\n    }\n    /**\n     * Gère les erreurs de manière centralisée\n     */\n    handleError(message, error, resetLoading = true) {\n      this.logger.error('MessageChat', message, error);\n      if (resetLoading) {\n        this.loading = false;\n        this.isUploading = false;\n        this.isLoadingMore = false;\n      }\n      this.error = error;\n      this.toastService.showError(message);\n    }\n    /**\n     * Gère les succès de manière centralisée\n     */\n    handleSuccess(message, callback) {\n      if (message) {\n        this.toastService.showSuccess(message);\n      }\n      if (callback) {\n        callback();\n      }\n    }\n    // Utiliser le service FileService au lieu de dupliquer la logique\n    getFileIcon(mimeType) {\n      return this.MessageService.getFileIcon(mimeType);\n    }\n    getFileType(mimeType) {\n      return this.MessageService.getFileType(mimeType);\n    }\n    handleConversationLoaded(conversation) {\n      this.conversation = conversation;\n      if (!conversation?.messages || conversation.messages.length === 0) {\n        this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n        this.messages = [];\n      } else {\n        const conversationMessages = [...(conversation?.messages || [])];\n        conversationMessages.sort((a, b) => {\n          const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n          const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n          return timeA - timeB;\n        });\n        this.messages = conversationMessages;\n      }\n      this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n      this.loading = false;\n      setTimeout(() => this.scrollToBottom(), 100);\n      // Messages épinglés supprimés\n      this.markMessagesAsRead();\n      if (this.conversation?.id) {\n        this.subscribeToConversationUpdates(this.conversation.id);\n        this.subscribeToNewMessages(this.conversation.id);\n        this.subscribeToTypingIndicators(this.conversation.id);\n      }\n    }\n    subscribeToConversationUpdates(conversationId) {\n      const sub = this.MessageService.subscribeToConversationUpdates(conversationId).subscribe({\n        next: updatedConversation => {\n          this.conversation = updatedConversation;\n          this.messages = updatedConversation.messages ? [...updatedConversation.messages] : [];\n          this.scrollToBottom();\n        },\n        error: error => {\n          this.toastService.showError('Connection to conversation updates lost');\n        }\n      });\n      this.subscriptions.add(sub);\n    }\n    subscribeToNewMessages(conversationId) {\n      const sub = this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n        next: newMessage => {\n          if (newMessage?.conversationId === this.conversation?.id) {\n            this.messages = [...this.messages, newMessage].sort((a, b) => {\n              const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n              const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n              return timeA - timeB;\n            });\n            setTimeout(() => this.scrollToBottom(), 100);\n            if (newMessage.sender?.id !== this.currentUserId && newMessage.sender?._id !== this.currentUserId) {\n              if (newMessage.id) {\n                this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n              }\n            }\n          }\n        },\n        error: error => {\n          this.toastService.showError('Connection to new messages lost');\n        }\n      });\n      this.subscriptions.add(sub);\n    }\n    subscribeToTypingIndicators(conversationId) {\n      const sub = this.MessageService.subscribeToTypingIndicator(conversationId).subscribe({\n        next: event => {\n          if (event.userId !== this.currentUserId) {\n            this.isTyping = event.isTyping;\n            if (this.isTyping) {\n              clearTimeout(this.typingTimeout);\n              this.typingTimeout = setTimeout(() => {\n                this.isTyping = false;\n              }, 2000);\n            }\n          }\n        }\n      });\n      this.subscriptions.add(sub);\n    }\n    markMessagesAsRead() {\n      const unreadMessages = this.messages.filter(msg => !msg.isRead && (msg.receiver?.id === this.currentUserId || msg.receiver?._id === this.currentUserId));\n      unreadMessages.forEach(msg => {\n        if (msg.id) {\n          const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n            error: error => {\n              // Error handled silently\n            }\n          });\n          this.subscriptions.add(sub);\n        }\n      });\n    }\n    onFileSelected(event) {\n      const file = event.target.files[0];\n      if (!file) return;\n      // Validate file size (e.g., 5MB max)\n      if (file.size > 5 * 1024 * 1024) {\n        this.toastService.showError('File size should be less than 5MB');\n        return;\n      }\n      // Validate file type\n      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n      if (!validTypes.includes(file.type)) {\n        this.toastService.showError('Invalid file type. Only images, PDFs and Word docs are allowed');\n        return;\n      }\n      this.selectedFile = file;\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.previewUrl = reader.result;\n      };\n      reader.readAsDataURL(file);\n    }\n    removeAttachment() {\n      this.selectedFile = null;\n      this.previewUrl = null;\n      if (this.fileInput?.nativeElement) {\n        this.fileInput.nativeElement.value = '';\n      }\n    }\n    /**\n     * Gère l'événement de frappe de l'utilisateur\n     * Envoie un indicateur de frappe avec un délai pour éviter trop de requêtes\n     */\n    onTyping() {\n      if (!this.conversation?.id || !this.currentUserId) {\n        return;\n      }\n      const conversationId = this.conversation.id;\n      clearTimeout(this.typingTimer);\n      if (!this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = true;\n        this.MessageService.startTyping(conversationId).subscribe({\n          next: () => {\n            // Success handled silently\n          },\n          error: error => {\n            // Error handled silently\n          }\n        });\n      }\n      this.typingTimer = setTimeout(() => {\n        if (this.isCurrentlyTyping) {\n          this.isCurrentlyTyping = false;\n          this.MessageService.stopTyping(conversationId).subscribe({\n            next: () => {\n              // Success handled silently\n            },\n            error: error => {\n              // Error handled silently\n            }\n          });\n        }\n      }, this.TYPING_TIMEOUT);\n    }\n    /**\n     * Méthode générique pour basculer les panneaux\n     */\n    togglePanel(panelName, closeOthers = true) {\n      const panels = {\n        theme: 'showThemeSelector',\n        menu: 'showMainMenu',\n        emoji: 'showEmojiPicker',\n        notification: 'showNotificationPanel',\n        search: 'showSearchBar',\n        status: 'showStatusSelector'\n      };\n      const currentPanel = panels[panelName];\n      if (currentPanel) {\n        this[currentPanel] = !this[currentPanel];\n        if (closeOthers && this[currentPanel]) {\n          Object.values(panels).forEach(panel => {\n            if (panel !== currentPanel) {\n              this[panel] = false;\n            }\n          });\n        }\n      }\n    }\n    toggleThemeSelector() {\n      this.togglePanel('theme');\n    }\n    /**\n     * Change le thème de la conversation\n     * @param theme Nom du thème à appliquer\n     */\n    changeTheme(theme) {\n      this.selectedTheme = theme;\n      this.showThemeSelector = false;\n      localStorage.setItem('chat-theme', theme);\n    }\n    toggleMainMenu() {\n      this.togglePanel('menu');\n    }\n    // Méthodes de conversation ultra-consolidées\n    clearConversation() {\n      if (!this.conversation?.id || this.messages.length === 0) {\n        this.toastService.showWarning('Aucune conversation à vider');\n        return;\n      }\n      if (confirm('Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.')) {\n        this.messages = [];\n        this.showMainMenu = false;\n        this.toastService.showSuccess('Conversation vidée avec succès');\n      }\n    }\n    exportConversation() {\n      if (!this.conversation?.id || this.messages.length === 0) {\n        this.toastService.showWarning('Aucune conversation à exporter');\n        return;\n      }\n      const conversationName = this.conversation.isGroup ? this.conversation.groupName || 'Groupe sans nom' : this.otherParticipant?.username || 'Conversation privée';\n      const exportData = {\n        conversation: {\n          id: this.conversation.id,\n          name: conversationName,\n          isGroup: this.conversation.isGroup,\n          participants: this.conversation.participants,\n          createdAt: this.conversation.createdAt\n        },\n        messages: this.messages.map(msg => ({\n          id: msg.id,\n          content: msg.content,\n          sender: msg.sender,\n          timestamp: msg.timestamp,\n          type: msg.type\n        })),\n        exportedAt: new Date().toISOString(),\n        exportedBy: this.currentUserId\n      };\n      const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n        type: 'application/json'\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      const safeFileName = conversationName.replace(/[^a-z0-9]/gi, '_').toLowerCase();\n      const dateStr = new Date().toISOString().split('T')[0];\n      link.download = `conversation-${safeFileName}-${dateStr}.json`;\n      link.click();\n      window.URL.revokeObjectURL(url);\n      this.showMainMenu = false;\n      this.toastService.showSuccess('Conversation exportée avec succès');\n    }\n    sendMessage() {\n      if (this.messageForm.invalid && !this.selectedFile || !this.currentUserId || !this.otherParticipant?.id) {\n        return;\n      }\n      this.stopTypingIndicator();\n      const content = this.messageForm.get('content')?.value;\n      // Créer un message temporaire pour l'affichage immédiat (comme dans Facebook Messenger)\n      const tempMessage = {\n        id: 'temp-' + new Date().getTime(),\n        content: content || '',\n        sender: {\n          id: this.currentUserId || '',\n          username: this.currentUsername\n        },\n        receiver: {\n          id: this.otherParticipant.id,\n          username: this.otherParticipant.username || 'Recipient'\n        },\n        timestamp: new Date(),\n        isRead: false,\n        isPending: true // Marquer comme en attente\n      };\n      // Si un fichier est sélectionné, ajouter l'aperçu au message temporaire\n      if (this.selectedFile) {\n        // Déterminer le type de fichier\n        let fileType = 'file';\n        if (this.selectedFile.type.startsWith('image/')) {\n          fileType = 'image';\n          // Pour les images, ajouter un aperçu immédiat\n          if (this.previewUrl) {\n            tempMessage.attachments = [{\n              id: 'temp-attachment',\n              url: this.previewUrl ? this.previewUrl.toString() : '',\n              type: MessageType.IMAGE,\n              name: this.selectedFile.name,\n              size: this.selectedFile.size\n            }];\n          }\n        }\n        // Définir le type de message en fonction du type de fichier\n        if (fileType === 'image') {\n          tempMessage.type = MessageType.IMAGE;\n        } else if (fileType === 'file') {\n          tempMessage.type = MessageType.FILE;\n        }\n      }\n      // Ajouter immédiatement le message temporaire à la liste\n      this.messages = [...this.messages, tempMessage];\n      // Réinitialiser le formulaire immédiatement pour une meilleure expérience utilisateur\n      const fileToSend = this.selectedFile; // Sauvegarder une référence\n      this.messageForm.reset();\n      this.removeAttachment();\n      // Forcer le défilement vers le bas immédiatement\n      setTimeout(() => this.scrollToBottom(true), 50);\n      // Maintenant, envoyer le message au serveur\n      this.isUploading = true;\n      const sendSub = this.MessageService.sendMessage(this.otherParticipant.id, content, fileToSend || undefined, MessageType.TEXT, this.conversation?.id).subscribe({\n        next: message => {\n          this.messages = this.messages.map(msg => msg.id === tempMessage.id ? message : msg);\n          this.isUploading = false;\n        },\n        error: error => {\n          this.messages = this.messages.map(msg => {\n            if (msg.id === tempMessage.id) {\n              return {\n                ...msg,\n                isPending: false,\n                isError: true\n              };\n            }\n            return msg;\n          });\n          this.isUploading = false;\n          this.toastService.showError('Failed to send message');\n        }\n      });\n      this.subscriptions.add(sendSub);\n    }\n    // La méthode ngAfterViewChecked est implémentée plus bas dans le fichier\n    // Méthode pour détecter le défilement vers le haut et charger plus de messages\n    onScroll(event) {\n      const container = event.target;\n      const scrollTop = container.scrollTop;\n      // Si on est proche du haut de la liste et qu'on n'est pas déjà en train de charger\n      if (scrollTop < 50 && !this.isLoadingMore && this.conversation?.id && this.hasMoreMessages) {\n        // Afficher un indicateur de chargement en haut de la liste\n        this.showLoadingIndicator();\n        // Sauvegarder la hauteur actuelle et la position des messages\n        const oldScrollHeight = container.scrollHeight;\n        const firstVisibleMessage = this.getFirstVisibleMessage();\n        // Marquer comme chargement en cours\n        this.isLoadingMore = true;\n        // Charger plus de messages avec un délai réduit\n        this.loadMoreMessages();\n        // Maintenir la position de défilement pour que l'utilisateur reste au même endroit\n        // en utilisant le premier message visible comme ancre\n        requestAnimationFrame(() => {\n          const preserveScrollPosition = () => {\n            if (firstVisibleMessage) {\n              const messageElement = this.findMessageElement(firstVisibleMessage.id);\n              if (messageElement) {\n                // Faire défiler jusqu'à l'élément qui était visible avant\n                messageElement.scrollIntoView({\n                  block: 'center'\n                });\n              } else {\n                // Fallback: utiliser la différence de hauteur\n                const newScrollHeight = container.scrollHeight;\n                const scrollDiff = newScrollHeight - oldScrollHeight;\n                container.scrollTop = scrollTop + scrollDiff;\n              }\n            }\n            // Masquer l'indicateur de chargement\n            this.hideLoadingIndicator();\n          };\n          // Attendre que le DOM soit mis à jour\n          setTimeout(preserveScrollPosition, 100);\n        });\n      }\n    }\n    // Méthode pour trouver le premier message visible dans la vue\n    getFirstVisibleMessage() {\n      if (!this.messagesContainer?.nativeElement || !this.messages.length) return null;\n      const container = this.messagesContainer.nativeElement;\n      const messageElements = container.querySelectorAll('.message-item');\n      for (let i = 0; i < messageElements.length; i++) {\n        const element = messageElements[i];\n        const rect = element.getBoundingClientRect();\n        // Si l'élément est visible dans la vue\n        if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n          const messageId = element.getAttribute('data-message-id');\n          return this.messages.find(m => m.id === messageId) || null;\n        }\n      }\n      return null;\n    }\n    // Méthode pour trouver un élément de message par ID\n    findMessageElement(messageId) {\n      if (!this.messagesContainer?.nativeElement || !messageId) return null;\n      return this.messagesContainer.nativeElement.querySelector(`[data-message-id=\"${messageId}\"]`);\n    }\n    // Afficher un indicateur de chargement en haut de la liste\n    showLoadingIndicator() {\n      // Créer l'indicateur s'il n'existe pas déjà\n      if (!document.getElementById('message-loading-indicator')) {\n        const indicator = document.createElement('div');\n        indicator.id = 'message-loading-indicator';\n        indicator.className = 'text-center py-2 text-gray-500 text-sm';\n        indicator.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n        if (this.messagesContainer?.nativeElement) {\n          this.messagesContainer.nativeElement.prepend(indicator);\n        }\n      }\n    }\n    // Masquer l'indicateur de chargement\n    hideLoadingIndicator() {\n      const indicator = document.getElementById('message-loading-indicator');\n      if (indicator && indicator.parentNode) {\n        indicator.parentNode.removeChild(indicator);\n      }\n    }\n    // Méthode pour charger plus de messages (style Facebook Messenger)\n    loadMoreMessages() {\n      if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages) return;\n      // Marquer comme chargement en cours\n      this.isLoadingMore = true;\n      // Augmenter la page pour charger les messages plus anciens\n      this.currentPage++;\n      // Charger plus de messages depuis le serveur avec pagination\n      this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n        next: conversation => {\n          if (conversation && conversation.messages && conversation.messages.length > 0) {\n            // Sauvegarder les messages actuels\n            const oldMessages = [...this.messages];\n            // Créer un Set des IDs existants pour une recherche de doublons plus rapide\n            const existingIds = new Set(oldMessages.map(msg => msg.id));\n            // Filtrer et trier les nouveaux messages plus efficacement\n            const newMessages = conversation.messages.filter(msg => !existingIds.has(msg.id)).sort((a, b) => {\n              const timeA = new Date(a.timestamp).getTime();\n              const timeB = new Date(b.timestamp).getTime();\n              return timeA - timeB;\n            });\n            if (newMessages.length > 0) {\n              // Ajouter les nouveaux messages au début de la liste\n              this.messages = [...newMessages, ...oldMessages];\n              // Limiter le nombre total de messages pour éviter les problèmes de performance\n              if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n                this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n              }\n              // Vérifier s'il y a plus de messages à charger\n              this.hasMoreMessages = newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n            } else {\n              // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n              this.hasMoreMessages = false;\n            }\n          } else {\n            this.hasMoreMessages = false;\n          }\n          // Désactiver le flag de chargement après un court délai\n          // pour permettre au DOM de se mettre à jour\n          setTimeout(() => {\n            this.isLoadingMore = false;\n          }, 200);\n        },\n        error: error => {\n          this.logger.error('MessageChat', 'Error loading more messages:', error);\n          this.isLoadingMore = false;\n          this.hideLoadingIndicator();\n          this.toastService.showError('Failed to load more messages');\n        }\n      });\n    }\n    // Méthode utilitaire pour comparer les timestamps\n    isSameTimestamp(timestamp1, timestamp2) {\n      if (!timestamp1 || !timestamp2) return false;\n      try {\n        const time1 = timestamp1 instanceof Date ? timestamp1.getTime() : new Date(timestamp1).getTime();\n        const time2 = timestamp2 instanceof Date ? timestamp2.getTime() : new Date(timestamp2).getTime();\n        return Math.abs(time1 - time2) < 1000; // Tolérance d'une seconde\n      } catch (error) {\n        return false;\n      }\n    }\n    scrollToBottom(force = false) {\n      try {\n        if (!this.messagesContainer?.nativeElement) return;\n        // Utiliser requestAnimationFrame pour s'assurer que le DOM est prêt\n        requestAnimationFrame(() => {\n          const container = this.messagesContainer.nativeElement;\n          const isScrolledToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 150;\n          // Faire défiler vers le bas si:\n          // - force est true (pour les nouveaux messages envoyés par l'utilisateur)\n          // - ou si l'utilisateur est déjà proche du bas\n          if (force || isScrolledToBottom) {\n            // Utiliser une animation fluide pour le défilement (comme dans Messenger)\n            container.scrollTo({\n              top: container.scrollHeight,\n              behavior: 'smooth'\n            });\n          }\n        });\n      } catch (err) {\n        this.logger.error('MessageChat', 'Error scrolling to bottom:', err);\n      }\n    }\n    // Méthode pour ouvrir l'image en plein écran (style Messenger)\n    /**\n     * Active/désactive l'enregistrement vocal\n     */\n    toggleVoiceRecording() {\n      this.isRecordingVoice = !this.isRecordingVoice;\n      if (!this.isRecordingVoice) {\n        // Si on désactive l'enregistrement, réinitialiser la durée\n        this.voiceRecordingDuration = 0;\n      }\n    }\n    /**\n     * Gère la fin de l'enregistrement vocal\n     * @param audioBlob Blob audio enregistré\n     */\n    onVoiceRecordingComplete(audioBlob) {\n      if (!this.conversation?.id && !this.otherParticipant?.id) {\n        this.toastService.showError('No conversation or recipient selected');\n        this.isRecordingVoice = false;\n        return;\n      }\n      const receiverId = this.otherParticipant?.id || '';\n      this.MessageService.sendVoiceMessage(receiverId, audioBlob, this.conversation?.id, this.voiceRecordingDuration).subscribe({\n        next: message => {\n          this.isRecordingVoice = false;\n          this.voiceRecordingDuration = 0;\n          this.scrollToBottom(true);\n        },\n        error: error => {\n          this.toastService.showError('Failed to send voice message');\n          this.isRecordingVoice = false;\n        }\n      });\n    }\n    /**\n     * Gère l'annulation de l'enregistrement vocal\n     */\n    onVoiceRecordingCancelled() {\n      this.isRecordingVoice = false;\n      this.voiceRecordingDuration = 0;\n    }\n    /**\n     * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n     * @param imageUrl URL de l'image à afficher\n     */\n    openImageFullscreen(imageUrl) {\n      window.open(imageUrl, '_blank');\n    }\n    /**\n     * Détecte les changements après chaque vérification de la vue\n     * Cela permet de s'assurer que les messages vocaux sont correctement affichés\n     * et que le défilement est maintenu\n     */\n    ngAfterViewChecked() {\n      // Faire défiler vers le bas si nécessaire\n      this.scrollToBottom();\n      // Forcer la détection des changements pour les messages vocaux\n      // Cela garantit que les messages vocaux sont correctement affichés même après avoir quitté la conversation\n      if (this.messages.some(msg => msg.type === MessageType.VOICE_MESSAGE)) {\n        // Utiliser setTimeout pour éviter l'erreur ExpressionChangedAfterItHasBeenCheckedError\n        setTimeout(() => {\n          this.cdr.detectChanges();\n        }, 0);\n      }\n    }\n    /**\n     * Arrête l'indicateur de frappe\n     */\n    stopTypingIndicator() {\n      if (this.isCurrentlyTyping && this.conversation?.id) {\n        this.isCurrentlyTyping = false;\n        clearTimeout(this.typingTimer);\n        const conversationId = this.conversation?.id;\n        if (conversationId) {\n          this.MessageService.stopTyping(conversationId).subscribe({\n            next: () => {\n              // Success handled silently\n            },\n            error: error => {\n              // Error handled silently\n            }\n          });\n        }\n      }\n    }\n    /**\n     * Navigue vers la liste des conversations\n     */\n    goBackToConversations() {\n      this.router.navigate(['/messages/conversations']);\n    }\n    toggleEmojiPicker() {\n      this.togglePanel('emoji');\n    }\n    /**\n     * Insère un emoji dans le champ de message\n     * @param emoji Emoji à insérer\n     */\n    insertEmoji(emoji) {\n      const control = this.messageForm.get('content');\n      if (control) {\n        const currentValue = control.value || '';\n        control.setValue(currentValue + emoji);\n        control.markAsDirty();\n        // Garder le focus sur le champ de saisie\n        setTimeout(() => {\n          const inputElement = document.querySelector('.whatsapp-input-field');\n          if (inputElement) {\n            inputElement.focus();\n          }\n        }, 0);\n      }\n    }\n    /**\n     * S'abonne aux notifications en temps réel\n     */\n    subscribeToNotifications() {\n      const notificationSub = this.MessageService.subscribeToNewNotifications().subscribe({\n        next: notification => {\n          this.notifications.unshift(notification);\n          this.updateNotificationCount();\n          this.MessageService.play('notification');\n          if (notification.type === 'NEW_MESSAGE' && notification.conversationId === this.conversation?.id) {\n            if (notification.id) {\n              this.MessageService.markAsRead([notification.id]).subscribe();\n            }\n          }\n        },\n        error: error => {\n          // Error handled silently\n        }\n      });\n      this.subscriptions.add(notificationSub);\n      const notificationsListSub = this.MessageService.notifications$.subscribe({\n        next: notifications => {\n          this.notifications = notifications;\n          this.updateNotificationCount();\n        },\n        error: error => {\n          // Error handled silently\n        }\n      });\n      this.subscriptions.add(notificationsListSub);\n      const notificationCountSub = this.MessageService.notificationCount$.subscribe({\n        next: count => {\n          this.unreadNotificationCount = count;\n        }\n      });\n      this.subscriptions.add(notificationCountSub);\n      const callSub = this.MessageService.incomingCall$.subscribe({\n        next: call => {\n          if (call) {\n            this.incomingCall = call;\n            this.showCallModal = true;\n            this.MessageService.play('ringtone');\n          } else {\n            this.showCallModal = false;\n            this.incomingCall = null;\n          }\n        }\n      });\n      this.subscriptions.add(callSub);\n      const activeCallSub = this.MessageService.activeCall$.subscribe({\n        next: call => {\n          this.activeCall = call;\n          if (call) {\n            this.showActiveCallModal = true;\n            this.startCallTimerMethod();\n          } else {\n            this.showActiveCallModal = false;\n            this.stopCallTimerMethod();\n            this.resetCallStateMethod();\n          }\n        }\n      });\n      this.subscriptions.add(activeCallSub);\n      // S'abonner aux flux vidéo locaux\n      const localStreamSub = this.MessageService.localStream$.subscribe({\n        next: stream => {\n          if (stream && this.localVideoElement) {\n            this.localVideoElement.srcObject = stream;\n          }\n        }\n      });\n      this.subscriptions.add(localStreamSub);\n      // S'abonner aux flux vidéo distants\n      const remoteStreamSub = this.MessageService.remoteStream$.subscribe({\n        next: stream => {\n          if (stream && this.remoteVideoElement) {\n            this.remoteVideoElement.srcObject = stream;\n          }\n        }\n      });\n      this.subscriptions.add(remoteStreamSub);\n    }\n    /**\n     * Initie un appel audio ou vidéo avec l'autre participant\n     * @param type Type d'appel (AUDIO ou VIDEO)\n     */\n    initiateCall(type) {\n      if (!this.otherParticipant || !this.otherParticipant.id) {\n        return;\n      }\n      this.MessageService.initiateCall(this.otherParticipant.id, type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO, this.conversation?.id).subscribe({\n        next: call => {\n          // Call initiated successfully\n        },\n        error: error => {\n          this.toastService.showError(\"Impossible d'initier l'appel. Veuillez réessayer.\");\n        }\n      });\n    }\n    /**\n     * Accepte un appel entrant\n     */\n    acceptCall() {\n      if (!this.incomingCall) {\n        return;\n      }\n      this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n        next: call => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n          this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';\n          this.callQuality = 'connecting';\n          this.toastService.showSuccess('Appel connecté');\n        },\n        error: error => {\n          this.toastService.showError(\"Impossible d'accepter l'appel. Veuillez réessayer.\");\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      });\n    }\n    /**\n     * Rejette un appel entrant\n     */\n    rejectCall() {\n      if (!this.incomingCall) {\n        return;\n      }\n      this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n        next: call => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n        error: error => {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      });\n    }\n    /**\n     * Termine un appel en cours\n     */\n    endCall() {\n      let activeCall = null;\n      const sub = this.MessageService.activeCall$.subscribe(call => {\n        activeCall = call;\n        if (!activeCall) {\n          return;\n        }\n        this.MessageService.endCall(activeCall.id).subscribe({\n          next: call => {\n            // Call ended successfully\n          },\n          error: error => {\n            // Error handled silently\n          }\n        });\n      });\n      sub.unsubscribe();\n    }\n    // Méthodes d'appel ultra-consolidées\n    toggleCallMute() {\n      this.isCallMuted = !this.isCallMuted;\n      this.updateCallMedia();\n      this.toastService.showInfo(this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé');\n    }\n    toggleCallVideo() {\n      this.isVideoEnabled = !this.isVideoEnabled;\n      this.updateCallMedia();\n      this.toastService.showInfo(this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée');\n    }\n    toggleCallMinimize() {\n      this.isCallMinimized = !this.isCallMinimized;\n    }\n    updateCallMedia() {\n      this.MessageService.toggleMedia(this.activeCall?.id, !this.isCallMuted, this.isVideoEnabled).subscribe({\n        next: () => {},\n        error: () => {}\n      });\n    }\n    // Méthodes de timer ultra-consolidées\n    startCallTimerMethod() {\n      this.callDuration = 0;\n      this.callTimer = setInterval(() => {\n        this.callDuration++;\n        if (this.callDuration === 3 && this.callQuality === 'connecting') {\n          this.callQuality = 'excellent';\n        }\n      }, 1000);\n    }\n    stopCallTimerMethod() {\n      if (this.callTimer) {\n        clearInterval(this.callTimer);\n        this.callTimer = null;\n      }\n    }\n    resetCallStateMethod() {\n      this.callDuration = 0;\n    }\n    // Méthode de contrôles d'appel supprimée - Propriétés inexistantes\n    // Méthode de configuration vidéo supprimée - Propriétés inexistantes\n    // --------------------------------------------------------------------------\n    // Section: Gestion Avancée des Notifications\n    // --------------------------------------------------------------------------\n    toggleNotificationPanel() {\n      this.togglePanel('notification');\n      if (this.showNotificationPanel) {\n        this.loadNotifications();\n      }\n    }\n    /**\n     * Charge les notifications\n     */\n    loadNotifications(refresh = false) {\n      const loadSub = this.MessageService.getNotifications(refresh, 1, 20).subscribe({\n        next: notifications => {\n          if (refresh) {\n            this.notifications = notifications;\n          } else {\n            this.notifications = [...this.notifications, ...notifications];\n          }\n          this.updateNotificationCount();\n        },\n        error: error => {\n          this.toastService.showError('Erreur lors du chargement des notifications');\n        }\n      });\n      this.subscriptions.add(loadSub);\n    }\n    /**\n     * Charge plus de notifications (pagination)\n     */\n    loadMoreNotifications() {\n      this.loadNotifications();\n    }\n    /**\n     * Met à jour le compteur de notifications non lues\n     */\n    updateNotificationCount() {\n      this.unreadNotificationCount = this.notifications.filter(n => !n.isRead).length;\n    }\n    /**\n     * Filtre les notifications selon le type sélectionné\n     */\n    getFilteredNotifications() {\n      return this.notifications; // Filtre simplifié\n    }\n    /**\n     * Change le filtre des notifications\n     */\n    setNotificationFilter(filter) {\n      // Filtre simplifié\n    }\n    /**\n     * Sélectionne/désélectionne une notification\n     */\n    toggleNotificationSelection(notificationId) {\n      if (this.selectedNotifications.has(notificationId)) {\n        this.selectedNotifications.delete(notificationId);\n      } else {\n        this.selectedNotifications.add(notificationId);\n      }\n    }\n    /**\n     * Sélectionne/désélectionne toutes les notifications visibles\n     */\n    toggleSelectAllNotifications() {\n      const filteredNotifications = this.getFilteredNotifications();\n      const allSelected = filteredNotifications.every(n => this.selectedNotifications.has(n.id));\n      if (allSelected) {\n        filteredNotifications.forEach(n => this.selectedNotifications.delete(n.id));\n      } else {\n        filteredNotifications.forEach(n => this.selectedNotifications.add(n.id));\n      }\n    }\n    /**\n     * Vérifie si toutes les notifications visibles sont sélectionnées\n     */\n    areAllNotificationsSelected() {\n      const filteredNotifications = this.getFilteredNotifications();\n      return filteredNotifications.length > 0 && filteredNotifications.every(n => this.selectedNotifications.has(n.id));\n    }\n    /**\n     * Marque les notifications sélectionnées comme lues\n     */\n    markSelectedAsRead() {\n      const selectedIds = Array.from(this.selectedNotifications);\n      if (selectedIds.length === 0) {\n        this.toastService.showWarning('Aucune notification sélectionnée');\n        return;\n      }\n      // Marquage en cours\n      const markSub = this.MessageService.markAsRead(selectedIds).subscribe({\n        next: result => {\n          this.notifications = this.notifications.map(n => selectedIds.includes(n.id) ? {\n            ...n,\n            isRead: true,\n            readAt: new Date()\n          } : n);\n          this.selectedNotifications.clear();\n          this.updateNotificationCount();\n          // Marquage terminé\n          this.toastService.showSuccess(`${result.readCount} notification(s) marquée(s) comme lue(s)`);\n        },\n        error: error => {\n          // Erreur lors du marquage\n          this.toastService.showError('Erreur lors du marquage des notifications');\n        }\n      });\n      this.subscriptions.add(markSub);\n    }\n    /**\n     * Marque toutes les notifications comme lues\n     */\n    markAllAsRead() {\n      const unreadNotifications = this.notifications.filter(n => !n.isRead);\n      if (unreadNotifications.length === 0) {\n        this.toastService.showInfo('Aucune notification non lue');\n        return;\n      }\n      const unreadIds = unreadNotifications.map(n => n.id);\n      // Marquage en cours\n      const markSub = this.MessageService.markAsRead(unreadIds).subscribe({\n        next: result => {\n          this.notifications = this.notifications.map(n => unreadIds.includes(n.id) ? {\n            ...n,\n            isRead: true,\n            readAt: new Date()\n          } : n);\n          this.updateNotificationCount();\n          // Marquage terminé\n          this.toastService.showSuccess('Toutes les notifications ont été marquées comme lues');\n        },\n        error: error => {\n          // Erreur lors du marquage\n          this.toastService.showError('Erreur lors du marquage des notifications');\n        }\n      });\n      this.subscriptions.add(markSub);\n    }\n    /**\n     * Affiche la confirmation de suppression des notifications sélectionnées\n     */\n    showDeleteSelectedConfirmation() {\n      if (this.selectedNotifications.size === 0) {\n        this.toastService.showWarning('Aucune notification sélectionnée');\n        return;\n      }\n      this.showDeleteConfirmModal = true;\n    }\n    /**\n     * Supprime les notifications sélectionnées\n     */\n    deleteSelectedNotifications() {\n      const selectedIds = Array.from(this.selectedNotifications);\n      if (selectedIds.length === 0) return;\n      this.isDeletingNotifications = true;\n      this.showDeleteConfirmModal = false;\n      const deleteSub = this.MessageService.deleteMultipleNotifications(selectedIds).subscribe({\n        next: result => {\n          this.notifications = this.notifications.filter(n => !selectedIds.includes(n.id));\n          this.selectedNotifications.clear();\n          this.updateNotificationCount();\n          this.isDeletingNotifications = false;\n          this.toastService.showSuccess(`${result.count} notification(s) supprimée(s)`);\n        },\n        error: error => {\n          this.isDeletingNotifications = false;\n          this.toastService.showError('Erreur lors de la suppression des notifications');\n        }\n      });\n      this.subscriptions.add(deleteSub);\n    }\n    /**\n     * Supprime une notification individuelle\n     */\n    deleteNotification(notificationId) {\n      const deleteSub = this.MessageService.deleteNotification(notificationId).subscribe({\n        next: result => {\n          this.notifications = this.notifications.filter(n => n.id !== notificationId);\n          this.selectedNotifications.delete(notificationId);\n          this.updateNotificationCount();\n          this.toastService.showSuccess('Notification supprimée');\n        },\n        error: error => {\n          this.toastService.showError('Erreur lors de la suppression de la notification');\n        }\n      });\n      this.subscriptions.add(deleteSub);\n    }\n    /**\n     * Supprime toutes les notifications\n     */\n    deleteAllNotifications() {\n      if (this.notifications.length === 0) return;\n      // Demander confirmation\n      if (!confirm('Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.')) {\n        return;\n      }\n      this.isDeletingNotifications = true;\n      const deleteAllSub = this.MessageService.deleteAllNotifications().subscribe({\n        next: result => {\n          this.notifications = [];\n          this.selectedNotifications.clear();\n          this.updateNotificationCount();\n          this.isDeletingNotifications = false;\n          this.toastService.showSuccess(`${result.count} notifications supprimées avec succès`);\n        },\n        error: error => {\n          this.isDeletingNotifications = false;\n          this.toastService.showError('Erreur lors de la suppression de toutes les notifications');\n        }\n      });\n      this.subscriptions.add(deleteAllSub);\n    }\n    /**\n     * Annule la suppression des notifications\n     */\n    cancelDeleteNotifications() {\n      this.showDeleteConfirmModal = false;\n    }\n    // --------------------------------------------------------------------------\n    // Section: Statut Utilisateur en Temps Réel\n    // --------------------------------------------------------------------------\n    /**\n     * S'abonne au statut utilisateur en temps réel\n     */\n    subscribeToUserStatus() {\n      const statusSub = this.MessageService.subscribeToUserStatus().subscribe({\n        next: user => {\n          this.handleUserStatusUpdate(user);\n        },\n        error: error => {\n          // Error handled silently\n        }\n      });\n      this.subscriptions.add(statusSub);\n    }\n    /**\n     * Gère la mise à jour du statut d'un utilisateur\n     */\n    handleUserStatusUpdate(user) {\n      if (!user.id) return;\n      if (user.isOnline) {\n        this.onlineUsers.set(user.id, user);\n      } else {\n        this.onlineUsers.delete(user.id);\n      }\n      if (this.otherParticipant && this.otherParticipant.id === user.id) {\n        this.otherParticipant = {\n          ...this.otherParticipant,\n          ...user\n        };\n      }\n    }\n    /**\n     * Initialise le statut de l'utilisateur actuel\n     */\n    initializeUserStatus() {\n      if (!this.currentUserId) return;\n      // Définir l'utilisateur comme en ligne\n      const setOnlineSub = this.MessageService.setUserOnline(this.currentUserId).subscribe({\n        next: user => {\n          this.currentUserStatus = 'online';\n          this.lastActivityTime = new Date();\n        },\n        error: error => {\n          this.logger.error('MessageChat', \"Erreur lors de l'initialisation du statut\", error);\n        }\n      });\n      this.subscriptions.add(setOnlineSub);\n    }\n    /**\n     * Démarre le suivi d'activité automatique\n     */\n    startActivityTracking() {\n      // Écouter les événements d'activité\n      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];\n      events.forEach(event => {\n        document.addEventListener(event, this.onUserActivity.bind(this), true);\n      });\n    }\n    /**\n     * Gère l'activité de l'utilisateur\n     */\n    onUserActivity() {\n      this.lastActivityTime = new Date();\n      // Réinitialiser le timer d'absence automatique\n      if (this.autoAwayTimeout) {\n        clearTimeout(this.autoAwayTimeout);\n      }\n      // Si l'utilisateur était absent, le remettre en ligne\n      if (this.currentUserStatus === 'away' || this.currentUserStatus === 'offline') {\n        this.updateUserStatus('online');\n      }\n      // Programmer la mise en absence automatique\n      this.autoAwayTimeout = setTimeout(() => {\n        if (this.currentUserStatus === 'online') {\n          this.updateUserStatus('away');\n        }\n      }, this.autoAwayDelay);\n    }\n    checkAndUpdateStatus() {\n      const now = new Date();\n      const timeSinceLastActivity = now.getTime() - this.lastActivityTime.getTime();\n      if (timeSinceLastActivity > this.autoAwayDelay && this.currentUserStatus === 'online') {\n        this.updateUserStatus('away');\n      } else if (timeSinceLastActivity > 1800000 && this.currentUserStatus === 'away') {\n        this.updateUserStatus('offline');\n      }\n    }\n    /**\n     * Met à jour le statut de l'utilisateur\n     */\n    updateUserStatus(status) {\n      if (!this.currentUserId) return;\n      const previousStatus = this.currentUserStatus;\n      let updateObservable;\n      if (status === 'online') {\n        updateObservable = this.MessageService.setUserOnline(this.currentUserId);\n      } else {\n        updateObservable = this.MessageService.setUserOffline(this.currentUserId);\n      }\n      const updateSub = updateObservable.subscribe({\n        next: user => {\n          this.currentUserStatus = status;\n          if (status !== previousStatus) {\n            const statusText = this.getStatusText(status);\n            this.toastService.showInfo(`Statut : ${statusText}`);\n          }\n        },\n        error: error => {\n          this.logger.error('MessageChat', 'Erreur lors de la mise à jour du statut', error);\n        }\n      });\n      this.subscriptions.add(updateSub);\n    }\n    /**\n     * Charge la liste des utilisateurs en ligne\n     */\n    loadOnlineUsers() {\n      const usersSub = this.MessageService.getAllUsers(false, undefined, 1, 50, 'username', 'asc', true).subscribe({\n        next: users => {\n          users.forEach(user => {\n            if (user.isOnline && user.id) {\n              this.onlineUsers.set(user.id, user);\n            }\n          });\n        },\n        error: error => {\n          this.logger.error('MessageChat', 'Erreur lors du chargement des utilisateurs en ligne', error);\n        }\n      });\n      this.subscriptions.add(usersSub);\n    }\n    // Méthode pour obtenir les options de statut pour le template\n    getStatusOptions() {\n      return Object.entries(this.statusConfig).map(([key, config]) => ({\n        key,\n        ...config\n      }));\n    }\n    // Méthode pour obtenir les options de thème pour le template\n    getThemeOptions() {\n      return this.themeConfig;\n    }\n    // Méthode pour obtenir les panneaux actifs\n    getActivePanels() {\n      return this.panelConfig.filter(panel => panel.show());\n    }\n    formatLastSeen(lastActive) {\n      if (!lastActive) return 'Jamais vu';\n      return this.MessageService.formatLastActive(lastActive);\n    }\n    /**\n     * Obtient le nombre d'utilisateurs en ligne\n     */\n    getOnlineUsersCount() {\n      return Array.from(this.onlineUsers.values()).filter(user => user.isOnline).length;\n    }\n    /**\n     * TrackBy function pour optimiser le rendu de la liste des utilisateurs\n     */\n    trackByUserId(index, user) {\n      return user.id || index.toString();\n    }\n    // Méthodes consolidées pour les templates\n    setStatusFilter(filter) {\n      this.statusFilterType = filter;\n    }\n    getFilteredUsers() {\n      return Array.from(this.onlineUsers.values());\n    }\n    // Gestion des réponses et transferts\n    startReplyToMessage(message) {\n      this.replyingToMessage = message;\n    }\n    cancelReply() {\n      this.replyingToMessage = null;\n    }\n    openForwardModal(message) {\n      this.forwardingMessage = message;\n      this.showForwardModal = true;\n    }\n    closeForwardModal() {\n      this.showForwardModal = false;\n      this.forwardingMessage = null;\n      this.selectedConversations = [];\n    }\n    startEditMessage(message) {\n      this.editingMessageId = message.id;\n      this.editingContent = message.content;\n    }\n    cancelEditMessage() {\n      this.editingMessageId = null;\n      this.editingContent = '';\n    }\n    onEditKeyPress(event, messageId) {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.saveEditMessage(messageId);\n      } else if (event.key === 'Escape') {\n        this.cancelEditMessage();\n      }\n    }\n    togglePinMessage(message) {\n      this.isPinning[message.id] = true;\n      setTimeout(() => {\n        this.isPinning[message.id] = false;\n        this.showPinConfirm[message.id] = false;\n      }, 1000);\n    }\n    onDocumentClick(event) {\n      const target = event.target;\n      if (!target.closest('.theme-selector-menu') && !target.closest('.btn-theme')) {\n        this.showThemeSelector = false;\n      }\n      if (!target.closest('.emoji-picker') && !target.closest('.btn-emoji')) {\n        this.showEmojiPicker = false;\n      }\n    }\n    toggleConversationSelection(conversationId) {\n      const index = this.selectedConversations.indexOf(conversationId);\n      index > -1 ? this.selectedConversations.splice(index, 1) : this.selectedConversations.push(conversationId);\n    }\n    forwardMessage() {\n      this.isForwarding = true;\n      setTimeout(() => {\n        this.isForwarding = false;\n        this.closeForwardModal();\n      }, 1000);\n    }\n    // Méthodes de recherche ultra-consolidées\n    onSearchInput(event) {\n      this.searchQuery = event.target.value;\n      this.searchQuery.length >= 2 ? this.performSearch() : this.clearSearch();\n    }\n    onSearchKeyPress(event) {\n      event.key === 'Enter' ? this.performSearch() : event.key === 'Escape' ? this.clearSearch() : null;\n    }\n    performSearch() {\n      this.isSearching = true;\n      this.searchMode = true;\n      setTimeout(() => {\n        this.searchResults = this.messages.filter(m => m.content?.toLowerCase().includes(this.searchQuery.toLowerCase()));\n        this.isSearching = false;\n      }, 500);\n    }\n    clearSearch() {\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.searchMode = false;\n      this.isSearching = false;\n    }\n    // Gestion des confirmations consolidée\n    showPinConfirmation(messageId) {\n      this.showPinConfirm[messageId] = true;\n    }\n    cancelPinConfirmation(messageId) {\n      this.showPinConfirm[messageId] = false;\n    }\n    showDeleteConfirmation(messageId) {\n      this.showDeleteConfirm[messageId] = true;\n    }\n    cancelDeleteMessage(messageId) {\n      this.showDeleteConfirm[messageId] = false;\n    }\n    confirmDeleteMessage(messageId) {\n      this.showDeleteConfirm[messageId] = false;\n    }\n    // Gestion des réactions et options consolidée\n    toggleReactionPicker(messageId) {\n      this.showReactionPicker[messageId] = !this.showReactionPicker[messageId];\n    }\n    reactToMessage(messageId, emoji) {\n      this.showReactionPicker[messageId] = false;\n    }\n    toggleMessageOptions(messageId) {\n      this.showMessageOptions[messageId] = !this.showMessageOptions[messageId];\n    }\n    ngOnDestroy() {\n      this.stopTypingIndicator();\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n      }\n      if (this.autoAwayTimeout) {\n        clearTimeout(this.autoAwayTimeout);\n      }\n      if (this.currentUserId) {\n        this.MessageService.setUserOffline(this.currentUserId).subscribe();\n      }\n      this.subscriptions.unsubscribe();\n    }\n    static {\n      this.ɵfac = function MessageChatComponent_Factory(t) {\n        return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.UserStatusService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.LoggerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MessageChatComponent,\n        selectors: [[\"app-message-chat\"]],\n        viewQuery: function MessageChatComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        decls: 62,\n        vars: 44,\n        consts: [[1, \"flex\", \"flex-col\", \"h-full\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"relative\", \"overflow-hidden\", \"futuristic-chat-container\", \"dark\", 3, \"ngClass\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\", \"bg-grid-pattern\"], [1, \"absolute\", \"inset-0\", \"opacity-0\", \"dark:opacity-100\", \"overflow-hidden\"], [1, \"h-px\", \"w-full\", \"bg-[#00f7ff]/20\", \"absolute\", \"animate-scan\"], [1, \"whatsapp-chat-header\"], [1, \"whatsapp-action-button\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"whatsapp-user-info\"], [1, \"whatsapp-avatar\"], [\"alt\", \"User avatar\", 3, \"src\"], [\"class\", \"whatsapp-online-indicator\", 4, \"ngIf\"], [\"class\", \"whatsapp-user-details\", 4, \"ngIf\"], [1, \"whatsapp-actions\"], [4, \"ngFor\", \"ngForOf\"], [1, \"relative\"], [\"title\", \"Statut utilisateur\", 1, \"whatsapp-action-button\", \"relative\", 3, \"ngClass\", \"click\"], [3, \"ngClass\"], [\"class\", \"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\", 4, \"ngIf\"], [\"class\", \"absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\", 4, \"ngIf\"], [1, \"whatsapp-action-button\", \"btn-theme\", 3, \"click\"], [1, \"fas\", \"fa-palette\"], [\"class\", \"theme-selector-menu absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\", 4, \"ngIf\"], [\"title\", \"Menu principal\", 1, \"whatsapp-action-button\", \"btn-menu\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\"], [\"class\", \"search-bar bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] px-4 py-3 relative z-10\", 4, \"ngIf\"], [\"class\", \"pinned-messages-panel bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] relative z-10\", 4, \"ngIf\"], [1, \"futuristic-messages-container\", 3, \"ngClass\", \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"flex justify-center items-center h-full\", 4, \"ngIf\"], [\"class\", \"flex justify-center py-2 sticky top-0 z-10\", 4, \"ngIf\"], [\"class\", \"flex justify-center py-2 mb-2\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-md my-4 backdrop-blur-sm\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMessages\", \"\"], [\"class\", \"futuristic-typing-indicator\", 4, \"ngIf\"], [1, \"whatsapp-input-container\"], [\"class\", \"whatsapp-file-preview\", 4, \"ngIf\"], [\"class\", \"whatsapp-emoji-picker\", 4, \"ngIf\"], [\"class\", \"reply-preview bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 border-l-4 border-[#4f5fad] dark:border-[#6d78c9] p-3 mx-4 mb-2 rounded-r-lg\", 4, \"ngIf\"], [1, \"whatsapp-input-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"whatsapp-input-tools\"], [\"type\", \"button\", 1, \"whatsapp-tool-button\", 3, \"ngClass\", \"click\"], [1, \"far\", \"fa-smile\"], [\"type\", \"button\", 1, \"whatsapp-tool-button\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [\"type\", \"file\", \"accept\", \"image/*\", 1, \"hidden\", 3, \"change\"], [\"fileInput\", \"\"], [3, \"maxDuration\", \"recordingComplete\", \"recordingCancelled\", 4, \"ngIf\"], [\"formControlName\", \"content\", \"type\", \"text\", \"placeholder\", \"Message\", \"class\", \"whatsapp-input-field\", 3, \"input\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"whatsapp-voice-button\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", \"class\", \"whatsapp-send-button\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"whatsapp-call-modal\", 4, \"ngIf\"], [\"class\", \"active-call-modal\", 3, \"ngClass\", \"mousemove\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 bg-black/50 dark:bg-black/70 flex items-center justify-center z-50 p-4\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"notification-panel-overlay\", 4, \"ngIf\"], [\"class\", \"user-status-panel-overlay\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"delete-confirm-modal\", 4, \"ngIf\"], [1, \"whatsapp-online-indicator\"], [1, \"whatsapp-user-details\"], [1, \"whatsapp-username\"], [1, \"whatsapp-status\"], [3, \"ngClass\", \"title\", \"click\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [1, \"absolute\", \"-top-1\", \"-right-1\", \"w-3\", \"h-3\", \"bg-blue-500\", \"rounded-full\", \"animate-pulse\"], [1, \"absolute\", \"right-0\", \"mt-2\", \"w-56\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"shadow-lg\", \"z-50\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"p-3\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"bg-gradient-to-r\", \"from-[#4f5fad]/10\", \"to-[#6d78c9]/10\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"font-medium\", 3, \"ngClass\"], [1, \"p-1\"], [\"class\", \"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\", 3, \"disabled\", \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"p-1\"], [1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"text-xs\"], [1, \"font-medium\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-colors\", 3, \"disabled\", \"ngClass\", \"click\"], [1, \"theme-selector-menu\", \"absolute\", \"right-0\", \"mt-2\", \"w-48\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"shadow-lg\", \"z-50\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"p-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [\"href\", \"javascript:void(0)\", \"class\", \"block w-full text-left px-3 py-2 text-sm rounded-md transition-colors\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"transition-colors\", 3, \"ngClass\", \"click\"], [1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-red-500/10\", \"dark:hover:bg-red-500/10\", \"transition-colors\", \"text-red-600\", \"dark:text-red-400\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"mr-3\", \"text-xs\"], [1, \"fas\", \"fa-download\", \"mr-3\", \"text-xs\"], [1, \"fas\", \"fa-info-circle\", \"mr-3\", \"text-xs\"], [1, \"fas\", \"fa-cog\", \"mr-3\", \"text-xs\"], [1, \"search-bar\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"px-4\", \"py-3\", \"relative\", \"z-10\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"fas\", \"fa-search\", \"text-sm\"], [1, \"flex-1\", \"relative\"], [\"type\", \"text\", \"placeholder\", \"Rechercher dans cette conversation...\", \"autofocus\", \"\", 1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"bg-[#edf1f4]/50\", \"dark:bg-[#2a2a2a]/50\", \"border\", \"border-[#edf1f4]\", \"dark:border-[#3a3a3a]\", \"rounded-lg\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]/50\", \"transition-colors\", 3, \"ngModel\", \"ngModelChange\", \"input\", \"keydown\"], [\"class\", \"absolute right-3 top-1/2 transform -translate-y-1/2\", 4, \"ngIf\"], [\"class\", \"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"class\", \"mt-3 max-h-40 overflow-y-auto border border-[#edf1f4]/50 dark:border-[#3a3a3a] rounded-lg bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30\", 4, \"ngIf\"], [\"class\", \"mt-3 p-3 text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30 rounded-lg\", 4, \"ngIf\"], [1, \"absolute\", \"right-3\", \"top-1/2\", \"transform\", \"-translate-y-1/2\"], [1, \"w-4\", \"h-4\", \"border-2\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"right-3\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"w-4\", \"h-4\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-xs\"], [1, \"mt-3\", \"max-h-40\", \"overflow-y-auto\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#3a3a3a]\", \"rounded-lg\", \"bg-[#edf1f4]/30\", \"dark:bg-[#2a2a2a]/30\"], [1, \"p-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#3a3a3a]\"], [1, \"max-h-32\", \"overflow-y-auto\"], [\"class\", \"w-full text-left px-3 py-2 hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors border-b border-[#edf1f4]/30 dark:border-[#3a3a3a]/30 last:border-b-0\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-colors\", \"border-b\", \"border-[#edf1f4]/30\", \"dark:border-[#3a3a3a]/30\", \"last:border-b-0\", 3, \"click\"], [1, \"flex\", \"items-start\", \"space-x-2\"], [1, \"w-6\", \"h-6\", \"rounded-full\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"flex\", \"items-center\", \"justify-center\", \"flex-shrink-0\", \"mt-0.5\"], [1, \"fas\", \"fa-comment\", \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"truncate\", 3, \"innerHTML\"], [1, \"mt-3\", \"p-3\", \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"bg-[#edf1f4]/30\", \"dark:bg-[#2a2a2a]/30\", \"rounded-lg\"], [1, \"fas\", \"fa-search\", \"text-lg\", \"mb-2\", \"block\", \"text-[#6d6870]/50\", \"dark:text-[#a0a0a0]/50\"], [1, \"pinned-messages-panel\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"z-10\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-4\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"fas\", \"fa-thumbtack\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"w-6\", \"h-6\", \"flex\", \"items-center\", \"justify-center\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"rounded-full\", \"transition-colors\", 3, \"click\"], [1, \"max-h-48\", \"overflow-y-auto\"], [\"class\", \"p-4 text-center\", 4, \"ngIf\"], [\"class\", \"divide-y divide-[#edf1f4]/30 dark:divide-[#3a3a3a]/30\", 4, \"ngIf\"], [1, \"p-4\", \"text-center\"], [1, \"text-[#6d6870]/70\", \"dark:text-[#a0a0a0]/70\"], [1, \"fas\", \"fa-thumbtack\", \"text-2xl\", \"mb-2\", \"block\", \"opacity-50\"], [1, \"text-sm\"], [1, \"divide-y\", \"divide-[#edf1f4]/30\", \"dark:divide-[#3a3a3a]/30\"], [\"class\", \"w-full text-left p-3 hover:bg-[#4f5fad]/5 dark:hover:bg-[#6d78c9]/5 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"text-left\", \"p-3\", \"hover:bg-[#4f5fad]/5\", \"dark:hover:bg-[#6d78c9]/5\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"space-x-3\"], [1, \"flex-shrink-0\"], [\"onerror\", \"this.src='assets/images/default-avatar.png'\", 1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-1\"], [1, \"text-xs\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-xs\", \"text-[#6d6870]/70\", \"dark:text-[#a0a0a0]/70\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"line-clamp-2\"], [4, \"ngIf\"], [\"class\", \"italic flex items-center\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"mt-1\"], [1, \"fas\", \"fa-thumbtack\", \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-1\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"flex-shrink-0\", \"text-[#6d6870]/50\", \"dark:text-[#a0a0a0]/50\"], [1, \"fas\", \"fa-chevron-right\", \"text-xs\"], [1, \"italic\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-image\", \"mr-1\"], [1, \"fas\", \"fa-microphone\", \"mr-1\"], [1, \"flex\", \"justify-center\", \"items-center\", \"h-full\"], [1, \"flex\", \"flex-col\", \"items-center\"], [1, \"w-12\", \"h-12\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\", \"mb-3\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-3\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"font-medium\"], [1, \"flex\", \"justify-center\", \"py-2\", \"sticky\", \"top-0\", \"z-10\"], [1, \"flex\", \"flex-col\", \"items-center\", \"backdrop-blur-sm\", \"bg-white/50\", \"dark:bg-[#1e1e1e]/50\", \"px-4\", \"py-2\", \"rounded-full\", \"shadow-sm\"], [1, \"flex\", \"space-x-2\", \"mb-1\"], [1, \"w-2\", \"h-2\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-pulse\", \"shadow-[0_0_5px_rgba(79,95,173,0.5)]\", \"dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\"], [1, \"w-2\", \"h-2\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-pulse\", \"shadow-[0_0_5px_rgba(79,95,173,0.5)]\", \"dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\", 2, \"animation-delay\", \"0.2s\"], [1, \"w-2\", \"h-2\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-pulse\", \"shadow-[0_0_5px_rgba(79,95,173,0.5)]\", \"dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\", 2, \"animation-delay\", \"0.4s\"], [1, \"flex\", \"justify-center\", \"py-2\", \"mb-2\"], [1, \"flex\", \"items-center\", \"w-full\", \"max-w-xs\"], [1, \"flex-1\", \"h-px\", \"bg-gradient-to-r\", \"from-transparent\", \"via-[#4f5fad]/20\", \"dark:via-[#6d78c9]/20\", \"to-transparent\"], [1, \"px-3\", \"text-xs\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"font-medium\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-md\", \"my-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"futuristic-message-wrapper\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-message-wrapper\"], [\"class\", \"futuristic-date-separator\", 4, \"ngIf\"], [1, \"futuristic-message\", \"group\", \"relative\", 3, \"ngClass\"], [\"data-reaction-trigger\", \"\", \"class\", \"absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-white dark:bg-[#2a2a2a] border border-[#edf1f4] dark:border-[#3a3a3a] hover:border-[#4f5fad] dark:hover:border-[#6d78c9] text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] rounded-full flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-all duration-200 z-10 shadow-lg\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"reaction-picker absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#edf1f4]/50 dark:border-[#3a3a3a] z-30 p-2\", 4, \"ngIf\"], [\"class\", \"futuristic-avatar\", 4, \"ngIf\"], [1, \"futuristic-message-content\"], [\"class\", \"futuristic-message-bubble relative\", 3, \"ngClass\", \"id\", \"mouseenter\", \"mouseleave\", 4, \"ngIf\"], [\"class\", \"voice-message-modern\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"futuristic-message-image-container\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"futuristic-date-separator\"], [1, \"futuristic-date-line\"], [1, \"futuristic-date-text\"], [\"data-reaction-trigger\", \"\", 1, \"absolute\", \"-bottom-2\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"w-8\", \"h-8\", \"bg-white\", \"dark:bg-[#2a2a2a]\", \"border\", \"border-[#edf1f4]\", \"dark:border-[#3a3a3a]\", \"hover:border-[#4f5fad]\", \"dark:hover:border-[#6d78c9]\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-sm\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-all\", \"duration-200\", \"z-10\", \"shadow-lg\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [1, \"reaction-picker\", \"absolute\", \"-bottom-12\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-white\", \"dark:bg-[#2a2a2a]\", \"rounded-lg\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#3a3a3a]\", \"z-30\", \"p-2\"], [1, \"flex\", \"space-x-1\"], [\"class\", \"w-8 h-8 flex items-center justify-center text-lg hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded transition-colors\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"text-lg\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"rounded\", \"transition-colors\", 3, \"title\", \"click\"], [1, \"futuristic-avatar\"], [\"alt\", \"User avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 3, \"src\"], [1, \"futuristic-message-bubble\", \"relative\", 3, \"ngClass\", \"id\", \"mouseenter\", \"mouseleave\"], [\"class\", \"absolute -top-2 -right-2 w-6 h-6 bg-[#4f5fad]/80 dark:bg-[#6d78c9]/80 hover:bg-[#4f5fad] dark:hover:bg-[#6d78c9] text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute top-6 right-0 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#edf1f4]/50 dark:border-[#3a3a3a] z-20 min-w-[140px] overflow-hidden\", 4, \"ngIf\"], [\"class\", \"absolute top-6 right-0 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#ff6b69]/50 z-20 min-w-[200px] p-3\", 4, \"ngIf\"], [\"class\", \"absolute top-6 right-0 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#4f5fad]/50 dark:border-[#6d78c9]/50 z-20 min-w-[200px] p-3\", 4, \"ngIf\"], [\"class\", \"futuristic-message-text\", 4, \"ngIf\"], [\"class\", \"futuristic-message-edit\", 4, \"ngIf\"], [\"class\", \"flex flex-wrap gap-1 mt-2\", 4, \"ngIf\"], [1, \"futuristic-message-info\"], [1, \"futuristic-message-time\"], [\"class\", \"futuristic-message-status\", 4, \"ngIf\"], [1, \"absolute\", \"-top-2\", \"-right-2\", \"w-6\", \"h-6\", \"bg-[#4f5fad]/80\", \"dark:bg-[#6d78c9]/80\", \"hover:bg-[#4f5fad]\", \"dark:hover:bg-[#6d78c9]\", \"text-white\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-xs\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-all\", \"duration-200\", \"z-10\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-v\", \"text-xs\"], [1, \"absolute\", \"top-6\", \"right-0\", \"bg-white\", \"dark:bg-[#2a2a2a]\", \"rounded-lg\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#3a3a3a]\", \"z-20\", \"min-w-[140px]\", \"overflow-hidden\"], [1, \"w-full\", \"px-3\", \"py-2\", \"text-left\", \"text-sm\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"flex\", \"items-center\", \"space-x-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-reply\", \"text-xs\"], [1, \"fas\", \"fa-share\", \"text-xs\"], [1, \"text-xs\"], [\"class\", \"w-full px-3 py-2 text-left text-sm hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"w-full px-3 py-2 text-left text-sm hover:bg-[#ff6b69]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"fas\", \"fa-edit\", \"text-xs\"], [1, \"w-full\", \"px-3\", \"py-2\", \"text-left\", \"text-sm\", \"hover:bg-[#ff6b69]/10\", \"flex\", \"items-center\", \"space-x-2\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#ff6b69]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"text-xs\"], [1, \"absolute\", \"top-6\", \"right-0\", \"bg-white\", \"dark:bg-[#2a2a2a]\", \"rounded-lg\", \"shadow-lg\", \"border\", \"border-[#ff6b69]/50\", \"z-20\", \"min-w-[200px]\", \"p-3\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-3\"], [1, \"flex\", \"space-x-2\"], [1, \"px-3\", \"py-1\", \"text-xs\", \"bg-[#edf1f4]\", \"dark:bg-[#3a3a3a]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"rounded\", \"transition-colors\", 3, \"click\"], [1, \"px-3\", \"py-1\", \"text-xs\", \"bg-[#ff6b69]\", \"hover:bg-[#ff6b69]/80\", \"text-white\", \"rounded\", \"transition-colors\", 3, \"click\"], [1, \"absolute\", \"top-6\", \"right-0\", \"bg-white\", \"dark:bg-[#2a2a2a]\", \"rounded-lg\", \"shadow-lg\", \"border\", \"border-[#4f5fad]/50\", \"dark:border-[#6d78c9]/50\", \"z-20\", \"min-w-[200px]\", \"p-3\"], [1, \"px-3\", \"py-1\", \"text-xs\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"hover:bg-[#4f5fad]/80\", \"dark:hover:bg-[#6d78c9]/80\", \"text-white\", \"rounded\", \"transition-colors\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"flex\", \"items-center\", \"space-x-1\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-spinner fa-spin text-xs\", 4, \"ngIf\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"text-xs\"], [1, \"futuristic-message-text\"], [\"class\", \"flex items-center mb-1\", 4, \"ngIf\"], [\"class\", \"reply-to-message bg-[#edf1f4]/30 dark:bg-[#3a3a3a]/30 border-l-2 border-[#4f5fad]/50 dark:border-[#6d78c9]/50 p-2 mb-2 rounded-r text-xs\", 4, \"ngIf\"], [\"class\", \"italic text-[#6d6870] dark:text-[#a0a0a0]\", 4, \"ngIf\"], [\"class\", \"text-xs text-[#6d6870] dark:text-[#a0a0a0] ml-2\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"mb-1\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"reply-to-message\", \"bg-[#edf1f4]/30\", \"dark:bg-[#3a3a3a]/30\", \"border-l-2\", \"border-[#4f5fad]/50\", \"dark:border-[#6d78c9]/50\", \"p-2\", \"mb-2\", \"rounded-r\", \"text-xs\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\", \"mb-1\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"line-clamp-2\"], [1, \"italic\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"ml-2\"], [1, \"futuristic-message-edit\"], [\"rows\", \"2\", \"placeholder\", \"Modifier le message...\", \"autofocus\", \"\", 1, \"w-full\", \"p-2\", \"text-sm\", \"bg-transparent\", \"border\", \"border-[#4f5fad]/30\", \"dark:border-[#6d78c9]/30\", \"rounded\", \"resize-none\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [1, \"flex\", \"justify-end\", \"space-x-2\", \"mt-2\"], [1, \"px-3\", \"py-1\", \"text-xs\", \"bg-[#4f5fad]\", \"hover:bg-[#4f5fad]/80\", \"dark:bg-[#6d78c9]\", \"dark:hover:bg-[#6d78c9]/80\", \"text-white\", \"rounded\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"flex-wrap\", \"gap-1\", \"mt-2\"], [\"class\", \"flex items-center space-x-1 px-2 py-1 rounded-full text-xs transition-all duration-200\", 3, \"ngClass\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"space-x-1\", \"px-2\", \"py-1\", \"rounded-full\", \"text-xs\", \"transition-all\", \"duration-200\", 3, \"ngClass\", \"title\", \"click\"], [1, \"futuristic-message-status\"], [\"class\", \"fas fa-check-double\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", 4, \"ngIf\"], [1, \"fas\", \"fa-check-double\"], [1, \"fas\", \"fa-check\"], [1, \"voice-message-modern\", 3, \"ngClass\"], [1, \"voice-play-btn-modern\"], [1, \"fas\", \"fa-play\"], [1, \"voice-waveform-modern\"], [\"class\", \"voice-bar-modern\", 3, \"height\", 4, \"ngFor\", \"ngForOf\"], [1, \"voice-duration-modern\"], [1, \"hidden\", 3, \"audioUrl\", \"duration\"], [1, \"voice-bar-modern\"], [1, \"futuristic-message-image-container\", 3, \"ngClass\"], [1, \"futuristic-image-wrapper\"], [\"target\", \"_blank\", 1, \"futuristic-message-image-link\", 3, \"href\"], [\"alt\", \"Image\", 1, \"futuristic-message-image\", 3, \"src\"], [1, \"futuristic-image-overlay\"], [1, \"fas\", \"fa-expand\"], [\"class\", \"futuristic-no-messages\", 4, \"ngIf\"], [1, \"futuristic-no-messages\"], [1, \"futuristic-no-messages-icon\"], [1, \"fas\", \"fa-satellite-dish\"], [1, \"futuristic-no-messages-text\"], [1, \"futuristic-start-button\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"futuristic-typing-indicator\"], [1, \"futuristic-typing-bubble\"], [1, \"futuristic-typing-dots\"], [1, \"futuristic-typing-dot\"], [1, \"futuristic-typing-dot\", 2, \"animation-delay\", \"0.2s\"], [1, \"futuristic-typing-dot\", 2, \"animation-delay\", \"0.4s\"], [1, \"whatsapp-file-preview\"], [1, \"whatsapp-preview-image\", 3, \"src\"], [1, \"whatsapp-remove-button\", 3, \"click\"], [1, \"whatsapp-emoji-picker\"], [1, \"whatsapp-emoji-categories\"], [1, \"whatsapp-emoji-category\", \"active\"], [1, \"whatsapp-emoji-category\"], [1, \"fas\", \"fa-cat\"], [1, \"fas\", \"fa-hamburger\"], [1, \"fas\", \"fa-futbol\"], [1, \"fas\", \"fa-car\"], [1, \"fas\", \"fa-lightbulb\"], [1, \"whatsapp-emoji-list\"], [\"class\", \"whatsapp-emoji-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"whatsapp-emoji-item\", 3, \"click\"], [1, \"reply-preview\", \"bg-[#edf1f4]/50\", \"dark:bg-[#2a2a2a]/50\", \"border-l-4\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"p-3\", \"mx-4\", \"mb-2\", \"rounded-r-lg\"], [1, \"flex\", \"items-start\", \"justify-between\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\", \"mb-1\"], [1, \"ml-2\", \"w-6\", \"h-6\", \"flex\", \"items-center\", \"justify-center\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", 3, \"click\"], [3, \"maxDuration\", \"recordingComplete\", \"recordingCancelled\"], [\"formControlName\", \"content\", \"type\", \"text\", \"placeholder\", \"Message\", 1, \"whatsapp-input-field\", 3, \"input\"], [\"type\", \"button\", 1, \"whatsapp-voice-button\", 3, \"click\"], [1, \"fas\", \"fa-microphone\"], [\"type\", \"submit\", 1, \"whatsapp-send-button\", 3, \"disabled\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"whatsapp-call-modal\"], [1, \"whatsapp-call-modal-content\"], [1, \"whatsapp-call-header\"], [1, \"whatsapp-call-avatar\"], [\"alt\", \"Caller avatar\", 3, \"src\"], [1, \"whatsapp-call-name\"], [1, \"whatsapp-call-status\"], [1, \"whatsapp-call-actions\"], [1, \"whatsapp-call-reject\", 3, \"click\"], [1, \"fas\", \"fa-phone-slash\"], [1, \"whatsapp-call-accept\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [1, \"active-call-modal\", 3, \"ngClass\", \"mousemove\"], [\"class\", \"video-call-interface\", 4, \"ngIf\"], [\"class\", \"audio-call-interface\", 4, \"ngIf\"], [\"class\", \"call-controls\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"minimized-call-interface\", 4, \"ngIf\"], [1, \"video-call-interface\"], [\"autoplay\", \"\", \"playsinline\", \"\", 1, \"remote-video\"], [\"remoteVideo\", \"\"], [\"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 1, \"local-video\"], [\"localVideo\", \"\"], [\"class\", \"local-avatar\", 4, \"ngIf\"], [1, \"local-avatar\"], [\"alt\", \"Vous\", 3, \"src\"], [1, \"audio-call-interface\"], [1, \"audio-call-content\"], [1, \"call-participant-avatar\"], [\"alt\", \"Participant\", 3, \"src\"], [1, \"call-participant-name\"], [1, \"call-status\"], [1, \"call-quality-indicator\"], [1, \"fas\", \"fa-signal\", 3, \"ngClass\"], [1, \"quality-text\"], [1, \"call-controls\", 3, \"ngClass\"], [\"class\", \"call-info\", 4, \"ngIf\"], [1, \"control-buttons\"], [1, \"control-btn\", 3, \"ngClass\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"control-btn\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [1, \"control-btn\", \"end-call\", 3, \"click\"], [1, \"control-btn\", 3, \"click\"], [1, \"call-info\"], [1, \"call-duration\"], [1, \"call-participant\"], [1, \"minimized-call-interface\"], [1, \"minimized-info\"], [1, \"minimized-duration\"], [1, \"minimized-participant\"], [1, \"minimized-controls\"], [1, \"minimized-btn\", 3, \"click\"], [1, \"minimized-btn\", \"end-call\", 3, \"click\"], [1, \"fixed\", \"inset-0\", \"bg-black/50\", \"dark:bg-black/70\", \"flex\", \"items-center\", \"justify-center\", \"z-50\", \"p-4\", 3, \"click\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"shadow-xl\", \"max-w-md\", \"w-full\", \"max-h-[80vh]\", \"overflow-hidden\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-4\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#3a3a3a]\"], [1, \"text-lg\", \"font-semibold\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"rounded-full\", \"transition-colors\", 3, \"click\"], [1, \"p-4\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#3a3a3a]\", \"bg-[#edf1f4]/30\", \"dark:bg-[#2a2a2a]/30\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [1, \"bg-white\", \"dark:bg-[#2a2a2a]\", \"rounded-lg\", \"p-3\", \"border\", \"border-[#edf1f4]\", \"dark:border-[#3a3a3a]\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"line-clamp-3\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [1, \"p-4\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#3a3a3a]\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-3\"], [\"class\", \"text-xs px-2 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad]/20 dark:hover:bg-[#6d78c9]/20 rounded transition-colors\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"text-xs px-2 py-1 bg-[#ff6b69]/10 text-[#ff6b69] hover:bg-[#ff6b69]/20 rounded transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"max-h-60\", \"overflow-y-auto\"], [1, \"p-4\", \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#3a3a3a]\", \"flex\", \"space-x-3\"], [1, \"flex-1\", \"px-4\", \"py-2\", \"text-sm\", \"bg-[#edf1f4]\", \"dark:bg-[#3a3a3a]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"rounded-lg\", \"transition-colors\", 3, \"click\"], [1, \"flex-1\", \"px-4\", \"py-2\", \"text-sm\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"hover:bg-[#4f5fad]/80\", \"dark:hover:bg-[#6d78c9]/80\", \"text-white\", \"rounded-lg\", \"transition-colors\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-share\", 4, \"ngIf\"], [1, \"mt-2\"], [1, \"text-xs\", \"px-2\", \"py-1\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/20\", \"dark:hover:bg-[#6d78c9]/20\", \"rounded\", \"transition-colors\", 3, \"click\"], [1, \"text-xs\", \"px-2\", \"py-1\", \"bg-[#ff6b69]/10\", \"text-[#ff6b69]\", \"hover:bg-[#ff6b69]/20\", \"rounded\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"class\", \"w-full p-3 flex items-center space-x-3 hover:bg-[#4f5fad]/5 dark:hover:bg-[#6d78c9]/5 transition-colors border-b border-[#edf1f4]/30 dark:border-[#3a3a3a]/30 last:border-b-0\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"p-3\", \"flex\", \"items-center\", \"space-x-3\", \"hover:bg-[#4f5fad]/5\", \"dark:hover:bg-[#6d78c9]/5\", \"transition-colors\", \"border-b\", \"border-[#edf1f4]/30\", \"dark:border-[#3a3a3a]/30\", \"last:border-b-0\", 3, \"click\"], [1, \"w-5\", \"h-5\", \"rounded\", \"border-2\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"ngClass\"], [\"class\", \"fas fa-check text-xs text-white\", 4, \"ngIf\"], [\"onerror\", \"this.src='assets/images/default-avatar.png'\", 1, \"w-10\", \"h-10\", \"rounded-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"flex-1\", \"min-w-0\", \"text-left\"], [1, \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"truncate\"], [1, \"text-xs\", \"text-[#6d6870]/70\", \"dark:text-[#a0a0a0]/70\", \"truncate\"], [1, \"fas\", \"fa-check\", \"text-xs\", \"text-white\"], [1, \"fas\", \"fa-comments\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"fas\", \"fa-share\"], [1, \"notification-panel-overlay\"], [1, \"notification-panel\"], [1, \"notification-header\"], [1, \"notification-title\"], [1, \"fas\", \"fa-bell\"], [\"class\", \"notification-badge\", 4, \"ngIf\"], [1, \"notification-actions\"], [\"title\", \"Param\\u00E8tres\", 1, \"notification-btn\", 3, \"click\"], [1, \"fas\", \"fa-cog\"], [\"title\", \"Actualiser\", 1, \"notification-btn\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\", 3, \"ngClass\"], [\"title\", \"Fermer\", 1, \"notification-btn\", \"close-btn\", 3, \"click\"], [1, \"notification-filters\"], [1, \"filter-tabs\"], [1, \"filter-tab\", 3, \"ngClass\", \"click\"], [\"class\", \"selection-actions\", 4, \"ngIf\"], [1, \"global-actions\"], [\"class\", \"action-btn mark-all-read-btn\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"action-btn delete-all-btn\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"notification-list\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"notification-item\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"load-more-container\", 4, \"ngIf\"], [\"class\", \"notification-settings\", 4, \"ngIf\"], [1, \"notification-badge\"], [1, \"selection-actions\"], [1, \"selection-controls\"], [1, \"select-all-checkbox\"], [\"type\", \"checkbox\", 3, \"checked\", \"change\"], [1, \"checkmark\"], [1, \"select-text\"], [\"class\", \"bulk-actions\", 4, \"ngIf\"], [1, \"bulk-actions\"], [1, \"action-btn\", \"mark-read-btn\", 3, \"disabled\", \"click\"], [1, \"action-btn\", \"delete-btn\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"action-btn\", \"mark-all-read-btn\", 3, \"disabled\", \"click\"], [1, \"action-btn\", \"delete-all-btn\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-trash-alt\", 3, \"ngClass\"], [1, \"loading-state\"], [1, \"empty-state\"], [1, \"fas\", \"fa-bell-slash\"], [1, \"notification-item\", 3, \"ngClass\"], [1, \"notification-checkbox\"], [1, \"notification-content\"], [1, \"notification-icon\", 3, \"ngClass\"], [1, \"notification-body\"], [1, \"notification-text\"], [\"class\", \"notification-sender\", 4, \"ngIf\"], [1, \"notification-message\"], [1, \"notification-meta\"], [1, \"notification-time\"], [\"class\", \"read-indicator\", 4, \"ngIf\"], [1, \"notification-item-actions\"], [\"class\", \"item-action-btn\", \"title\", \"Marquer comme lu\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Supprimer\", 1, \"item-action-btn\", \"delete\", 3, \"click\"], [1, \"notification-sender\"], [1, \"read-indicator\"], [\"title\", \"Marquer comme lu\", 1, \"item-action-btn\", 3, \"click\"], [1, \"load-more-container\"], [1, \"load-more-btn\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-chevron-down\", 3, \"ngClass\"], [1, \"notification-settings\"], [1, \"settings-header\"], [1, \"close-settings-btn\", 3, \"click\"], [1, \"settings-content\"], [1, \"setting-item\"], [1, \"setting-label\"], [\"type\", \"checkbox\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"setting-checkmark\"], [1, \"setting-text\"], [1, \"user-status-panel-overlay\", 3, \"click\"], [1, \"user-status-panel\", 3, \"click\"], [1, \"status-panel-header\"], [1, \"status-panel-btn\", \"close-btn\", 3, \"click\"], [1, \"status-user-list\"], [1, \"empty-status-state\"], [1, \"delete-confirm-modal\"], [1, \"delete-confirm-content\"], [1, \"delete-confirm-header\"], [1, \"delete-confirm-body\"], [1, \"warning-text\"], [1, \"delete-confirm-actions\"], [1, \"cancel-btn\", 3, \"click\"], [1, \"confirm-delete-btn\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-trash\", 3, \"ngClass\"]],\n        template: function MessageChatComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r323 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2);\n            i0.ɵɵelementStart(3, \"div\", 3);\n            i0.ɵɵelement(4, \"div\", 4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_6_listener() {\n              return ctx.goBackToConversations();\n            });\n            i0.ɵɵelement(7, \"i\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9);\n            i0.ɵɵelement(10, \"img\", 10);\n            i0.ɵɵtemplate(11, MessageChatComponent_span_11_Template, 1, 0, \"span\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(12, MessageChatComponent_div_12_Template, 5, 2, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"div\", 13);\n            i0.ɵɵtemplate(14, MessageChatComponent_ng_container_14_Template, 4, 8, \"ng-container\", 14);\n            i0.ɵɵelementStart(15, \"div\", 15)(16, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_16_listener() {\n              return ctx.toggleStatusSelector();\n            });\n            i0.ɵɵelement(17, \"i\", 17);\n            i0.ɵɵtemplate(18, MessageChatComponent_span_18_Template, 1, 0, \"span\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(19, MessageChatComponent_div_19_Template, 18, 4, \"div\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 15)(21, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_21_listener() {\n              return ctx.toggleThemeSelector();\n            });\n            i0.ɵɵelement(22, \"i\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(23, MessageChatComponent_div_23_Template, 5, 1, \"div\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"div\", 15)(25, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_25_listener() {\n              return ctx.toggleMainMenu();\n            });\n            i0.ɵɵelement(26, \"i\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(27, MessageChatComponent_div_27_Template, 24, 0, \"div\", 19);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(28, MessageChatComponent_div_28_Template, 12, 5, \"div\", 25);\n            i0.ɵɵtemplate(29, MessageChatComponent_div_29_Template, 11, 3, \"div\", 26);\n            i0.ɵɵelementStart(30, \"div\", 27, 28);\n            i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_div_scroll_30_listener($event) {\n              return ctx.onScroll($event);\n            });\n            i0.ɵɵtemplate(32, MessageChatComponent_div_32_Template, 7, 0, \"div\", 29);\n            i0.ɵɵtemplate(33, MessageChatComponent_div_33_Template, 8, 0, \"div\", 30);\n            i0.ɵɵtemplate(34, MessageChatComponent_div_34_Template, 6, 0, \"div\", 31);\n            i0.ɵɵtemplate(35, MessageChatComponent_div_35_Template, 10, 1, \"div\", 32);\n            i0.ɵɵtemplate(36, MessageChatComponent_ng_container_36_Template, 2, 1, \"ng-container\", 33);\n            i0.ɵɵtemplate(37, MessageChatComponent_ng_template_37_Template, 1, 1, \"ng-template\", null, 34, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵtemplate(39, MessageChatComponent_div_39_Template, 8, 1, \"div\", 35);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"div\", 36);\n            i0.ɵɵtemplate(41, MessageChatComponent_div_41_Template, 4, 1, \"div\", 37);\n            i0.ɵɵtemplate(42, MessageChatComponent_div_42_Template, 16, 1, \"div\", 38);\n            i0.ɵɵtemplate(43, MessageChatComponent_div_43_Template, 9, 2, \"div\", 39);\n            i0.ɵɵelementStart(44, \"form\", 40);\n            i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_44_listener() {\n              return ctx.sendMessage();\n            });\n            i0.ɵɵelementStart(45, \"div\", 41)(46, \"button\", 42);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_46_listener() {\n              return ctx.toggleEmojiPicker();\n            });\n            i0.ɵɵelement(47, \"i\", 43);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"button\", 44);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_48_listener() {\n              i0.ɵɵrestoreView(_r323);\n              const _r21 = i0.ɵɵreference(51);\n              return i0.ɵɵresetView(_r21.click());\n            });\n            i0.ɵɵelement(49, \"i\", 45);\n            i0.ɵɵelementStart(50, \"input\", 46, 47);\n            i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_50_listener($event) {\n              return ctx.onFileSelected($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(52, MessageChatComponent_app_voice_recorder_52_Template, 1, 1, \"app-voice-recorder\", 48);\n            i0.ɵɵtemplate(53, MessageChatComponent_input_53_Template, 1, 0, \"input\", 49);\n            i0.ɵɵtemplate(54, MessageChatComponent_button_54_Template, 2, 0, \"button\", 50);\n            i0.ɵɵtemplate(55, MessageChatComponent_button_55_Template, 3, 3, \"button\", 51);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(56, MessageChatComponent_div_56_Template, 18, 3, \"div\", 52);\n            i0.ɵɵtemplate(57, MessageChatComponent_div_57_Template, 5, 7, \"div\", 53);\n            i0.ɵɵtemplate(58, MessageChatComponent_div_58_Template, 36, 13, \"div\", 54);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(59, MessageChatComponent_div_59_Template, 33, 23, \"div\", 55);\n            i0.ɵɵtemplate(60, MessageChatComponent_div_60_Template, 14, 6, \"div\", 56);\n            i0.ɵɵtemplate(61, MessageChatComponent_div_61_Template, 17, 5, \"div\", 57);\n          }\n          if (rf & 2) {\n            const _r15 = i0.ɵɵreference(38);\n            let tmp_29_0;\n            let tmp_30_0;\n            i0.ɵɵproperty(\"ngClass\", ctx.selectedTheme);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getHeaderActions());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(38, _c18, ctx.showStatusSelector));\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassMap(ctx.getStatusIcon(ctx.currentUserStatus));\n            i0.ɵɵproperty(\"ngClass\", ctx.getStatusColor(ctx.currentUserStatus));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isUpdatingStatus);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showStatusSelector);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.showThemeSelector);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.showMainMenu);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showSearchBar);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showPinnedMessages);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(40, _c19, ctx.showSearchBar));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoadingMore);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.hasMoreMessages && ctx.messages.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.messages && ctx.messages.length > 0)(\"ngIfElse\", _r15);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isTyping);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.previewUrl);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.replyingToMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(42, _c12, ctx.showEmojiPicker));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isRecordingVoice);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isRecordingVoice && ((tmp_29_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_29_0.value) === \"\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isRecordingVoice && ((tmp_30_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_30_0.value) !== \"\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showCallModal && ctx.incomingCall);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showActiveCallModal && ctx.activeCall);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showForwardModal);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showNotificationPanel);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getActivePanels());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showDeleteConfirmModal);\n          }\n        },\n        dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.CheckboxControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.NgModel, i4.FormGroupDirective, i4.FormControlName, i9.VoiceRecorderComponent, i10.VoiceMessagePlayerComponent],\n        styles: [\"[_ngcontent-%COMP%]:root{--primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);--accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);--message-gradient: linear-gradient( 135deg, rgba(0, 247, 255, .9), rgba(131, 56, 236, .9) );--dark-primary: #1a1a2e;--dark-secondary: #16213e;--dark-accent: #0f3460;--dark-surface: rgba(30, 30, 40, .7);--dark-text: #e0e0e0;--modern-white: #ffffff;--modern-gray-200: #e5e7eb;--modern-gray-800: #1f2937;--glass-effect: rgba(255, 255, 255, .1);--glass-border: rgba(255, 255, 255, .2);--shadow-modern: 0 8px 32px rgba(0, 0, 0, .12);--blur-effect: blur(20px);--glow-effect: 0 0 20px rgba(0, 247, 255, .4);--neon-cyan: #00ffff;--neon-pink: #ff00ff;--neon-green: #00ff00;--neon-orange: #ff6600;--transition-fast: all .3s cubic-bezier(.4, 0, .2, 1);--transition-smooth: all .3s ease;--border-radius-sm: 8px;--border-radius-md: 12px;--border-radius-lg: 18px;--text-primary: #333;--text-secondary: #666;--text-dim: #aaa;--accent-color: #00f7ff}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]{background:var(--message-gradient);color:#fff;border:1px solid var(--glass-border);box-shadow:var(--glow-effect);-webkit-backdrop-filter:var(--blur-effect);backdrop-filter:var(--blur-effect);transition:var(--transition-fast);border-radius:var(--border-radius-lg) var(--border-radius-lg) 4px var(--border-radius-lg)}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 25px #00f7ff80}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]{background:var(--dark-surface);border:1px solid rgba(0,247,255,.15);color:var(--dark-text);transition:var(--transition-fast);border-radius:var(--border-radius-lg) var(--border-radius-lg) var(--border-radius-lg) 4px}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover{border-color:#00f7ff40;box-shadow:0 2px 15px #00f7ff33}.whatsapp-chat-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:8px 16px;background:var(--primary-gradient);background-size:200% 200%;animation:_ngcontent-%COMP%_gradientShift 8s ease infinite;border-bottom:1px solid var(--glass-border);height:60px;-webkit-backdrop-filter:var(--blur-effect);backdrop-filter:var(--blur-effect);box-shadow:var(--shadow-modern);position:relative;overflow:hidden}.dark[_nghost-%COMP%]   .whatsapp-chat-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-chat-header[_ngcontent-%COMP%]{background:var(--dark-secondary);background-image:var(--accent-gradient);background-size:200% 200%;border-bottom:1px solid var(--dark-accent)}.whatsapp-user-info[_ngcontent-%COMP%]{display:flex;align-items:center;flex:1;margin-left:12px}.whatsapp-avatar[_ngcontent-%COMP%]{position:relative;width:36px;height:36px;border-radius:50%;overflow:hidden;margin-right:8px}.whatsapp-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.whatsapp-online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:8px;height:8px;border-radius:50%;background:var(--neon-green);border:2px solid #f0f2f5}.dark[_nghost-%COMP%]   .whatsapp-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-online-indicator[_ngcontent-%COMP%]{border-color:var(--dark-primary)}.whatsapp-user-details[_ngcontent-%COMP%]{margin-left:8px;display:flex;flex-direction:column}.whatsapp-username[_ngcontent-%COMP%]{font-size:.9375rem;font-weight:600;color:var(--text-primary)}.dark[_nghost-%COMP%]   .whatsapp-username[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-username[_ngcontent-%COMP%]{color:var(--dark-text)}.whatsapp-status[_ngcontent-%COMP%]{font-size:.75rem;color:var(--text-secondary)}.dark[_nghost-%COMP%]   .whatsapp-status[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-status[_ngcontent-%COMP%]{color:var(--text-dim)}.whatsapp-actions[_ngcontent-%COMP%]{display:flex;gap:16px}.whatsapp-action-button[_ngcontent-%COMP%]{background:transparent;border:none;color:var(--accent-color);width:42px;height:42px;border-radius:0;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:var(--transition-smooth);position:relative;font-size:1.1rem}.whatsapp-action-button.btn-audio-call[_ngcontent-%COMP%], .whatsapp-action-button.btn-video-call[_ngcontent-%COMP%]{border-radius:50%!important;background:rgba(0,247,255,.1)!important;border:1px solid rgba(0,247,255,.3)!important}.whatsapp-action-button[_ngcontent-%COMP%]:hover{transform:scale(1.1) translateY(-2px);color:#fff;text-shadow:0 0 10px rgba(0,247,255,.8)}.dark[_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%]{color:var(--neon-cyan);text-shadow:0 0 10px var(--neon-cyan)}.dark[_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%]:hover{color:#fff;text-shadow:none}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:scale(.9)}to{opacity:1;transform:scale(1)}}@keyframes _ngcontent-%COMP%_slideInLeft{0%{opacity:0;transform:translate(-20px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(20px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_gradientShift{0%{background-position:0% 50%}50%{background-position:100% 50%}to{background-position:0% 50%}}.dark[_nghost-%COMP%]   .futuristic-messages-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-messages-container[_ngcontent-%COMP%]{background:var(--dark-primary)}.futuristic-messages-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.futuristic-messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:var(--accent-color);border-radius:10px}.futuristic-message-wrapper[_ngcontent-%COMP%]{margin-bottom:4px;position:relative;z-index:1}.futuristic-message[_ngcontent-%COMP%]{display:flex;align-items:flex-end;margin-bottom:1px;position:relative;width:100%}.futuristic-message-bubble[_ngcontent-%COMP%]{margin-bottom:.25rem;max-width:70%;padding:.6rem .8rem;font-size:.9rem;line-height:1.4;word-wrap:break-word;border-radius:12px;transition:var(--transition-fast);animation:_ngcontent-%COMP%_fadeIn .3s ease-out;box-shadow:var(--shadow-modern)}.futuristic-message-content[_ngcontent-%COMP%]{max-width:80%}.futuristic-message-text[_ngcontent-%COMP%]{word-break:break-word;line-height:1.5}.futuristic-date-separator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;margin:1.5rem 0;color:var(--text-dim);font-size:.75rem;text-transform:uppercase;letter-spacing:1px}.futuristic-date-line[_ngcontent-%COMP%]{flex:1;height:1px;background:linear-gradient(to right,transparent,var(--accent-color),transparent);opacity:.3}.futuristic-date-text[_ngcontent-%COMP%]{margin:0 10px;padding:2px 8px;background-color:#00f7ff0d;border-radius:var(--border-radius-sm)}.futuristic-message-time[_ngcontent-%COMP%]{font-size:.7rem;margin-top:.2rem;opacity:.7;color:var(--text-dim)}.futuristic-message-info[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:6px;margin-top:4px;font-size:.75rem;letter-spacing:.02em;font-weight:300}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-info[_ngcontent-%COMP%]{color:#fffc}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-info[_ngcontent-%COMP%]{color:#00f7ffb3}.futuristic-message-status[_ngcontent-%COMP%]{color:#00f7ffe6}.futuristic-message-current-user[_ngcontent-%COMP%]{justify-content:flex-end;display:flex;animation:_ngcontent-%COMP%_slideInRight .3s ease-out}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;max-width:80%}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]{background:var(--primary-gradient);color:#fff;border-radius:18px 18px 4px}.futuristic-message-other-user[_ngcontent-%COMP%]{justify-content:flex-start;display:flex}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;max-width:80%}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]{background:var(--modern-white);color:var(--modern-gray-800);border:1px solid var(--modern-gray-200)}.dark[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]{background:var(--dark-surface);color:var(--dark-text);border:1px solid var(--dark-accent)}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover{transform:translateY(-2px);background:rgba(230,235,245,.9);box-shadow:0 6px 20px #4f5fad33}.dark[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover{background:rgba(40,40,50,.9);box-shadow:0 6px 20px #00f7ff4d}.futuristic-input-container[_ngcontent-%COMP%]{padding:6px 10px;background-color:var(--glass-effect);min-height:50px;position:relative;z-index:10;border-top:1px solid var(--glass-border)}.futuristic-input-form[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;position:relative;z-index:2}.futuristic-input-tools[_ngcontent-%COMP%]{display:flex;gap:8px}.futuristic-tool-button[_ngcontent-%COMP%]{background:transparent;color:var(--accent-color);border:none;border-radius:0;width:32px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:var(--transition-fast)}.futuristic-tool-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:translateY(-2px)}.futuristic-tool-button.active[_ngcontent-%COMP%]{background-color:var(--accent-color);color:#fff}.futuristic-input-field[_ngcontent-%COMP%]{flex:1;font-size:.9rem;padding:10px 16px;height:44px;border-radius:22px;border:1px solid rgba(0,247,255,.2);background-color:#00f7ff0d;color:var(--dark-text);transition:var(--transition-smooth)}.futuristic-send-button[_ngcontent-%COMP%], .whatsapp-send-button[_ngcontent-%COMP%]{background:var(--accent-gradient);color:#fff;border:none;border-radius:50%;width:44px;height:44px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:var(--transition-fast)}.futuristic-send-button[_ngcontent-%COMP%]:hover, .whatsapp-send-button[_ngcontent-%COMP%]:hover:not(:disabled){transform:scale(1.15)}.futuristic-send-button[_ngcontent-%COMP%]:disabled, .whatsapp-send-button[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed;transform:none}.futuristic-chat-container[_ngcontent-%COMP%]{width:100%;margin:0 auto;border-radius:20px;overflow:hidden;box-shadow:var(--shadow-modern);height:100%;display:flex;flex-direction:column;position:relative}:not(.dark)[_nghost-%COMP%]   .futuristic-chat-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-chat-container[_ngcontent-%COMP%]{background:var(--modern-white);border:1px solid var(--modern-gray-200)}.dark[_nghost-%COMP%]   .futuristic-chat-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-chat-container[_ngcontent-%COMP%]{background:var(--dark-primary);border:1px solid var(--dark-accent)}.message-date-divider[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d;margin:.5rem 0;text-align:center}.futuristic-message-image-container[_ngcontent-%COMP%]{margin:2px 0;max-width:220px;transition:var(--transition-fast)}.futuristic-image-wrapper[_ngcontent-%COMP%]{position:relative;overflow:hidden;border-radius:12px;box-shadow:var(--shadow-modern);transition:var(--transition-fast)}.futuristic-message-image[_ngcontent-%COMP%]{width:100%;display:block;transition:var(--transition-fast);cursor:pointer}.futuristic-image-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:rgba(0,0,0,.5);display:flex;align-items:center;justify-content:center;opacity:0;transition:var(--transition-fast);color:#fff;font-size:1.5rem}.futuristic-image-wrapper[_ngcontent-%COMP%]:hover   .futuristic-image-overlay[_ngcontent-%COMP%]{opacity:1}.futuristic-image-wrapper[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.fullscreen-image-container[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#000000f2;z-index:9999;display:flex;align-items:center;justify-content:center}.fullscreen-image-wrapper[_ngcontent-%COMP%]{position:relative;max-width:90%;max-height:90%}.fullscreen-image-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;max-height:90vh;object-fit:contain;border-radius:8px;box-shadow:0 0 30px #00f7ff4d}.dark[_nghost-%COMP%]   .whatsapp-input-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-input-container[_ngcontent-%COMP%]{background:var(--dark-secondary);background-image:linear-gradient(135deg,rgba(79,172,254,.08) 0%,rgba(245,87,108,.08) 100%),radial-gradient(circle at 30% 70%,rgba(102,126,234,.1) 0%,transparent 50%);border-top:1px solid var(--dark-accent);box-shadow:0 -4px 20px #0003}.whatsapp-input-form[_ngcontent-%COMP%]{display:flex;align-items:center;position:relative;z-index:2}.whatsapp-input-tools[_ngcontent-%COMP%]{display:flex;gap:8px;margin-right:8px}.whatsapp-tool-button[_ngcontent-%COMP%]{width:38px;height:38px;background:transparent;border:none;color:var(--accent-color);display:flex;align-items:center;justify-content:center;cursor:pointer;transition:var(--transition-fast);font-size:1rem}.whatsapp-tool-button[_ngcontent-%COMP%]:hover, .whatsapp-tool-button.active[_ngcontent-%COMP%], .whatsapp-voice-button[_ngcontent-%COMP%]:hover{transform:scale(1.1);color:#fff;background:var(--accent-color)}.whatsapp-input-field[_ngcontent-%COMP%], .futuristic-input-field[_ngcontent-%COMP%]{flex:1;background-color:#fff;border:none;border-radius:20px;padding:8px 16px;color:#333;font-size:.9375rem;transition:var(--transition-fast);outline:none;box-shadow:0 1px 2px #0000001a}.dark[_nghost-%COMP%]   .whatsapp-input-field[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-input-field[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]{background-color:#3a3a3a;color:#e0e0e0;box-shadow:0 1px 2px #0000004d}.whatsapp-input-field[_ngcontent-%COMP%]:focus, .futuristic-input-field[_ngcontent-%COMP%]:focus{box-shadow:0 1px 3px #0003}.whatsapp-voice-button[_ngcontent-%COMP%]{width:46px;height:46px;border-radius:50%;background:var(--neon-orange);color:#fff;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-left:12px;transition:var(--transition-fast);border:none;font-size:1.2rem}.whatsapp-voice-button[_ngcontent-%COMP%]:hover{transform:scale(1.15)}.whatsapp-file-preview[_ngcontent-%COMP%], .futuristic-file-preview[_ngcontent-%COMP%]{position:relative;margin-bottom:8px;border-radius:8px;overflow:hidden;box-shadow:var(--shadow-modern);max-width:200px}.whatsapp-preview-image[_ngcontent-%COMP%], .futuristic-preview-image[_ngcontent-%COMP%]{width:100%;display:block}.whatsapp-remove-button[_ngcontent-%COMP%], .futuristic-remove-button[_ngcontent-%COMP%]{position:absolute;top:5px;right:5px;width:24px;height:24px;border-radius:50%;background:rgba(0,0,0,.6);border:none;color:#fff;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:var(--transition-fast)}.whatsapp-remove-button[_ngcontent-%COMP%]:hover, .futuristic-remove-button[_ngcontent-%COMP%]:hover{background:rgba(255,0,0,.7)}.whatsapp-emoji-picker[_ngcontent-%COMP%]{position:absolute;bottom:60px;left:0;right:0;background-color:#fff;border-radius:8px 8px 0 0;box-shadow:var(--shadow-modern);z-index:100;height:250px;overflow:hidden}.dark[_nghost-%COMP%]   .whatsapp-emoji-picker[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-emoji-picker[_ngcontent-%COMP%]{background-color:var(--dark-secondary)}.whatsapp-emoji-categories[_ngcontent-%COMP%]{display:flex;padding:8px;border-bottom:1px solid #e0e0e0}.whatsapp-emoji-category[_ngcontent-%COMP%], .whatsapp-emoji-item[_ngcontent-%COMP%]{width:36px;height:36px;background-color:transparent;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:var(--transition-fast)}.whatsapp-emoji-list[_ngcontent-%COMP%]{flex:1;padding:8px;display:grid;grid-template-columns:repeat(8,1fr);gap:4px;overflow-y:auto}.whatsapp-emoji-item[_ngcontent-%COMP%]:hover{background-color:var(--accent-color)}.whatsapp-call-modal[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#000c;display:flex;align-items:center;justify-content:center;z-index:9999}.whatsapp-call-modal-content[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;width:300px;max-width:90%;overflow:hidden;box-shadow:var(--shadow-modern)}.dark[_nghost-%COMP%]   .whatsapp-call-modal-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-call-modal-content[_ngcontent-%COMP%]{background-color:var(--dark-secondary)}.whatsapp-call-header[_ngcontent-%COMP%]{padding:20px;text-align:center}.whatsapp-call-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;overflow:hidden;margin:0 auto 15px}.whatsapp-call-actions[_ngcontent-%COMP%]{display:flex}.whatsapp-call-reject[_ngcontent-%COMP%], .whatsapp-call-accept[_ngcontent-%COMP%]{flex:1;padding:15px;border:none;cursor:pointer;transition:var(--transition-fast);color:#fff}.whatsapp-call-reject[_ngcontent-%COMP%]{background-color:#f44336}.whatsapp-call-accept[_ngcontent-%COMP%]{background-color:var(--accent-color)}.close-fullscreen-btn[_ngcontent-%COMP%], .image-fullscreen-dialog[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{position:absolute;background:rgba(0,0,0,.7);color:#fff;border:none;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:var(--transition-fast)}.close-fullscreen-btn[_ngcontent-%COMP%]{top:-40px;right:-40px;width:40px;height:40px;font-size:24px}.image-fullscreen-dialog[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{top:20px;right:20px;width:50px;height:50px;font-size:30px;z-index:10000000}.close-fullscreen-btn[_ngcontent-%COMP%]:hover, .image-fullscreen-dialog[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]:hover{background:var(--accent-color);transform:scale(1.1)}.image-fullscreen-container[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;justify-content:center;align-items:center;position:relative}.image-fullscreen-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:85%;max-height:85%;object-fit:contain;border-radius:12px;box-shadow:var(--shadow-modern)}.futuristic-loading[_ngcontent-%COMP%], .futuristic-loading-more[_ngcontent-%COMP%], .futuristic-conversation-start[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;padding:20px}.futuristic-loading-more[_ngcontent-%COMP%]{padding:10px;background-color:var(--glass-effect);border-radius:var(--border-radius-md)}.futuristic-loading-circle[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:50%;border:3px solid var(--glass-border);border-top-color:var(--accent-color);animation:spin 1.5s linear infinite;margin-bottom:15px}.futuristic-loading-text[_ngcontent-%COMP%], .futuristic-conversation-start-text[_ngcontent-%COMP%]{color:var(--accent-color);font-size:.875rem}.futuristic-loading-dots[_ngcontent-%COMP%], .futuristic-typing-dots[_ngcontent-%COMP%]{display:flex;gap:5px}.futuristic-loading-dot[_ngcontent-%COMP%], .futuristic-typing-dot[_ngcontent-%COMP%]{width:8px;height:8px;background-color:var(--accent-color);border-radius:50%;animation:pulse 1.5s infinite ease-in-out}.futuristic-conversation-start[_ngcontent-%COMP%]{width:100%;margin:20px 0}.futuristic-conversation-start-line[_ngcontent-%COMP%]{flex:1;height:1px;background:linear-gradient(to right,transparent,var(--accent-color),transparent);opacity:.3}.futuristic-conversation-start-text[_ngcontent-%COMP%]{margin:0 10px;padding:4px 12px;background-color:var(--glass-effect);border-radius:var(--border-radius-md);font-size:.75rem;text-transform:uppercase}.futuristic-error-container[_ngcontent-%COMP%], .futuristic-voice-message-container[_ngcontent-%COMP%]{margin:15px;padding:15px;border-radius:14px;display:flex;align-items:flex-start;transition:var(--transition-fast)}.futuristic-error-container[_ngcontent-%COMP%]{background:rgba(255,0,0,.1);border-left:3px solid #ff3b30}.futuristic-voice-message-container[_ngcontent-%COMP%]{min-width:200px;-webkit-backdrop-filter:blur(12px);backdrop-filter:blur(12px);border:1px solid rgba(255,255,255,.15);box-shadow:var(--shadow-modern);padding:8px;margin:0}.futuristic-error-icon[_ngcontent-%COMP%], .futuristic-voice-play-button[_ngcontent-%COMP%]{color:#ff3b30;font-size:1.25rem;margin-right:15px}.futuristic-voice-play-button[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;background-color:var(--accent-color);color:#fff;margin-right:12px}.futuristic-error-title[_ngcontent-%COMP%]{color:#ff3b30;font-size:.875rem;font-weight:600;margin-bottom:5px}.futuristic-error-message[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.8125rem}.futuristic-message-pending[_ngcontent-%COMP%], .futuristic-message-sending[_ngcontent-%COMP%]{opacity:.7}.futuristic-message-error[_ngcontent-%COMP%]{border:1px solid rgba(239,68,68,.5)}.futuristic-voice-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.futuristic-voice-waveform[_ngcontent-%COMP%]{display:flex;align-items:center;gap:3px;height:36px}.futuristic-voice-bar[_ngcontent-%COMP%]{width:3px;background-color:currentColor;border-radius:4px;transition:var(--transition-fast)}.image-modal[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{animation:scaleIn .3s ease-in-out;transition:transform .2s ease}.futuristic-typing-indicator[_ngcontent-%COMP%], .futuristic-no-messages[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;margin:10px 0}.futuristic-no-messages[_ngcontent-%COMP%]{flex-direction:column;height:100%;padding:20px;text-align:center}.futuristic-typing-bubble[_ngcontent-%COMP%]{background-color:#ffffff1a;border-radius:16px;padding:10px 15px;margin-left:10px;box-shadow:0 2px 5px #0000001a;border:1px solid rgba(255,255,255,.1)}.futuristic-no-messages-icon[_ngcontent-%COMP%]{font-size:3rem;color:var(--accent-color);margin-bottom:20px;opacity:.7}.futuristic-no-messages-text[_ngcontent-%COMP%]{color:var(--text-dim);font-size:1rem;margin-bottom:30px;line-height:1.5}.futuristic-file-preview[_ngcontent-%COMP%]{position:relative;margin-bottom:10px;border-radius:var(--border-radius-md);overflow:hidden;max-width:200px;border:2px solid rgba(0,247,255,.3);box-shadow:var(--glow-effect)}.futuristic-preview-image[_ngcontent-%COMP%]{width:100%;display:block}.futuristic-remove-button[_ngcontent-%COMP%]{position:absolute;top:5px;right:5px;background:rgba(0,0,0,.6);border:none;border-radius:50%;width:24px;height:24px;display:flex;align-items:center;justify-content:center;color:#fff;font-size:12px;cursor:pointer;transition:all var(--transition-fast)}.futuristic-remove-button[_ngcontent-%COMP%]:hover{background:rgba(255,0,0,.7);transform:scale(1.1)}.active-call-modal[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#000;z-index:9998}.video-call-interface[_ngcontent-%COMP%], .audio-call-interface[_ngcontent-%COMP%], .minimized-call-interface[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center}.video-call-interface[_ngcontent-%COMP%]{background:#000}.audio-call-interface[_ngcontent-%COMP%], .minimized-call-interface[_ngcontent-%COMP%]{background:var(--primary-gradient)}.minimized-call-interface[_ngcontent-%COMP%]{justify-content:space-between;padding:1rem;color:#fff}.remote-video[_ngcontent-%COMP%], .local-video[_ngcontent-%COMP%]{border-radius:12px;object-fit:cover}.remote-video[_ngcontent-%COMP%]{width:100%;height:100%}.local-video[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;width:200px;height:150px}.call-controls[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background:rgba(0,0,0,.8);padding:2rem}.control-buttons[_ngcontent-%COMP%], .minimized-controls[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:1.5rem}.minimized-controls[_ngcontent-%COMP%]{gap:.5rem}.control-btn[_ngcontent-%COMP%], .minimized-btn[_ngcontent-%COMP%]{border:none;background:transparent;color:#fff;cursor:pointer;transition:var(--transition-fast)}.control-btn[_ngcontent-%COMP%]{width:60px;height:60px;font-size:1.5rem}.minimized-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:rgba(255,255,255,.2)}.control-btn.end-call[_ngcontent-%COMP%], .minimized-btn.end-call[_ngcontent-%COMP%]{border-radius:50%;background:#ff6b6b}.control-btn.end-call[_ngcontent-%COMP%]:hover, .minimized-btn[_ngcontent-%COMP%]:hover{transform:scale(1.15)}.minimized-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}@media (max-width: 768px){.local-video[_ngcontent-%COMP%], .local-avatar[_ngcontent-%COMP%]{width:120px;height:90px;top:10px;right:10px}.control-btn[_ngcontent-%COMP%]{width:50px;height:50px;font-size:1.2rem}.call-controls[_ngcontent-%COMP%]{padding:1.5rem}}@media (max-width: 480px){.control-btn[_ngcontent-%COMP%]{width:45px;height:45px;font-size:1.1rem}}.notification-panel-overlay[_ngcontent-%COMP%], .status-history[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.5);z-index:9999;display:flex;align-items:center;justify-content:center}.notification-panel[_ngcontent-%COMP%]{width:90%;max-width:600px;height:80%;background:var(--dark-primary);border-radius:20px;box-shadow:var(--shadow-modern);border:2px solid var(--glass-border);display:flex;flex-direction:column}.notification-header[_ngcontent-%COMP%], .status-history-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:1.5rem;background:var(--primary-gradient)}.notification-title[_ngcontent-%COMP%]{color:#fff;font-size:1.25rem;font-weight:600}.notification-btn[_ngcontent-%COMP%], .close-history-btn[_ngcontent-%COMP%], .action-btn[_ngcontent-%COMP%]{border:none;cursor:pointer;transition:var(--transition-fast);color:#fff}.notification-btn[_ngcontent-%COMP%], .close-history-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:rgba(255,255,255,.2)}.action-btn[_ngcontent-%COMP%]{padding:.5rem 1rem;border-radius:8px;background:var(--accent-gradient)}.notification-list[_ngcontent-%COMP%], .status-history-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1rem}.notification-item[_ngcontent-%COMP%], .status-history-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;padding:1rem;margin-bottom:.5rem;background:var(--glass-effect);border-radius:var(--border-radius-md);border:1px solid var(--glass-border);transition:var(--transition-fast)}.notification-item[_ngcontent-%COMP%]:hover, .status-history-item[_ngcontent-%COMP%]:hover{background:rgba(255,255,255,.1);transform:translateY(-2px)}.notification-content[_ngcontent-%COMP%]{flex:1}.status-action-btn[_ngcontent-%COMP%], .item-action-btn[_ngcontent-%COMP%], .futuristic-message-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .futuristic-message-text[_ngcontent-%COMP%]   i.fa-thumbtack[_ngcontent-%COMP%], .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .futuristic-image-overlay[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .whatsapp-action-button[_ngcontent-%COMP%]   .absolute.-top-1.-right-1[_ngcontent-%COMP%], .notification-badge[_ngcontent-%COMP%], .online-count-badge[_ngcontent-%COMP%]{transition:var(--transition-fast)}.status-action-btn[_ngcontent-%COMP%], .item-action-btn[_ngcontent-%COMP%]{width:36px;height:36px;border:none;border-radius:50%;background:var(--glass-effect);color:var(--text-dim);cursor:pointer;display:flex;align-items:center;justify-content:center}.status-action-btn[_ngcontent-%COMP%]:hover, .item-action-btn[_ngcontent-%COMP%]:hover, .futuristic-message-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover, .futuristic-message-text[_ngcontent-%COMP%]   i.fa-thumbtack[_ngcontent-%COMP%]:hover{background:var(--accent-color);color:#fff;transform:scale(1.1)}.futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.futuristic-image-overlay[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.2)}.whatsapp-action-button[_ngcontent-%COMP%]   .absolute.-top-1.-right-1[_ngcontent-%COMP%], .notification-badge[_ngcontent-%COMP%], .online-count-badge[_ngcontent-%COMP%]{background:var(--accent-gradient);border-radius:6px;padding:0 4px;font-size:10px;font-weight:700;color:#fff;min-width:18px;height:18px;z-index:10}.notification-icon[_ngcontent-%COMP%], .notification-btn[_ngcontent-%COMP%], .whatsapp-action-button[_ngcontent-%COMP%], .btn-search[_ngcontent-%COMP%], .btn-status[_ngcontent-%COMP%], .btn-stats[_ngcontent-%COMP%], .btn-voice-messages[_ngcontent-%COMP%], .btn-menu[_ngcontent-%COMP%], .btn-theme[_ngcontent-%COMP%], .btn-notifications[_ngcontent-%COMP%], .btn-history[_ngcontent-%COMP%], .voice-play-btn-modern[_ngcontent-%COMP%], .play-button[_ngcontent-%COMP%]{border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:var(--transition-fast)}.notification-icon[_ngcontent-%COMP%], .notification-btn[_ngcontent-%COMP%], .whatsapp-action-button[_ngcontent-%COMP%], .btn-search[_ngcontent-%COMP%], .btn-status[_ngcontent-%COMP%], .btn-stats[_ngcontent-%COMP%], .btn-voice-messages[_ngcontent-%COMP%], .btn-menu[_ngcontent-%COMP%], .btn-theme[_ngcontent-%COMP%], .btn-notifications[_ngcontent-%COMP%], .btn-history[_ngcontent-%COMP%]{width:44px;height:44px;background:transparent;font-size:1.1rem}.notification-icon[_ngcontent-%COMP%], .notification-btn[_ngcontent-%COMP%]{background:var(--glass-effect);border-radius:var(--border-radius-sm)}.notification-btn[_ngcontent-%COMP%]:hover, .btn-search[_ngcontent-%COMP%]:hover{background:var(--accent-color);color:#fff;transform:scale(1.1)}.voice-play-btn-modern[_ngcontent-%COMP%], .play-button[_ngcontent-%COMP%]{background:var(--accent-color);color:#fff;border-radius:50%;width:32px;height:32px}.voice-play-btn-modern[_ngcontent-%COMP%]:hover, .play-button[_ngcontent-%COMP%]:hover{transform:scale(1.2)}.theme-selector-menu[_ngcontent-%COMP%]{display:block;visibility:visible;opacity:1;z-index:100;background:var(--glass-effect);-webkit-backdrop-filter:var(--blur-effect);backdrop-filter:var(--blur-effect);border:2px solid var(--glass-border);box-shadow:var(--shadow-modern)}.theme-selector-menu[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{transition:var(--transition-fast);border-radius:8px;margin:2px}.theme-selector-menu[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{transform:translate(5px);background:var(--accent-color)}*[_ngcontent-%COMP%]{box-sizing:border-box}@media (pointer: coarse){.whatsapp-action-button[_ngcontent-%COMP%], .whatsapp-send-button[_ngcontent-%COMP%], .whatsapp-voice-button[_ngcontent-%COMP%]{min-width:44px;min-height:44px}}@media (prefers-reduced-motion: reduce){*[_ngcontent-%COMP%]{animation-duration:.01ms;transition-duration:.01ms}}\"]\n      });\n    }\n  }\n  return MessageChatComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}