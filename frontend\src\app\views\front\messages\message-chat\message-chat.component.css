/* ========================================
   VARIABLES CSS CONSOLIDÉES ET OPTIMISÉES
   ======================================== */

:root {
  /* Couleurs principales */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --message-gradient: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.9),
    rgba(131, 56, 236, 0.9)
  );

  /* Mode sombre */
  --dark-primary: #1a1a2e;
  --dark-secondary: #16213e;
  --dark-surface: rgba(30, 30, 40, 0.7);
  --dark-text: #e0e0e0;

  /* Mode clair */
  --modern-white: #ffffff;
  --modern-gray-200: #e5e7eb;
  --modern-gray-800: #1f2937;

  /* Effets unifiés */
  --glass-effect: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-modern: 0 8px 32px rgba(0, 0, 0, 0.12);
  --glow-effect: 0 0 20px rgba(0, 247, 255, 0.4);

  /* Couleurs essentielles */
  --neon-green: #00ff00;
  --neon-orange: #ff6600;

  /* Transitions unifiées */
  --transition-fast: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* Bordures et rayons */
  --border-radius-md: 12px;
  --border-radius-lg: 18px;

  /* Couleurs de texte */
  --text-primary: #333;
  --text-secondary: #666;
  --text-dim: #aaa;
  --accent-color: #00f7ff;
}

/* ========================================
   STYLES DE MESSAGES UNIFIÉS
   ======================================== */

/* Messages de l'utilisateur actuel */
.futuristic-message-current-user .futuristic-message-bubble {
  background: var(--message-gradient);
  color: white;
  border: 1px solid var(--glass-border);
  box-shadow: var(--glow-effect);
  backdrop-filter: var(--blur-effect);
  transition: var(--transition-fast);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 4px
    var(--border-radius-lg);
}

.futuristic-message-current-user .futuristic-message-bubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 247, 255, 0.5);
}

/* Messages des autres utilisateurs */
.futuristic-message-other-user .futuristic-message-bubble {
  background: var(--dark-surface);
  border: 1px solid rgba(0, 247, 255, 0.15);
  color: var(--dark-text);
  transition: var(--transition-fast);
  border-radius: var(--border-radius-lg) var(--border-radius-lg)
    var(--border-radius-lg) 4px;
}

.futuristic-message-other-user .futuristic-message-bubble:hover {
  border-color: rgba(0, 247, 255, 0.25);
  box-shadow: 0 2px 15px rgba(0, 247, 255, 0.2);
}

/* ========================================
   EN-TÊTE UNIFIÉ
   ======================================== */

.whatsapp-chat-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: var(--primary-gradient);
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
  border-bottom: 1px solid var(--glass-border);
  height: 60px;
  backdrop-filter: var(--blur-effect);
  box-shadow: var(--shadow-modern);
  position: relative;
  overflow: hidden;
}

:host-context(.dark) .whatsapp-chat-header {
  background: var(--dark-secondary);
  background-image: var(--accent-gradient);
  background-size: 200% 200%;
  border-bottom: 1px solid var(--dark-accent);
}

/* ========================================
   INFORMATIONS UTILISATEUR UNIFIÉES
   ======================================== */

.whatsapp-user-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 12px;
}

.whatsapp-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}

.whatsapp-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--neon-green);
  border: 2px solid #f0f2f5;
}

:host-context(.dark) .whatsapp-online-indicator {
  border-color: var(--dark-primary);
}

.whatsapp-user-details {
  margin-left: 8px;
  display: flex;
  flex-direction: column;
}

.whatsapp-username {
  font-size: 0.9375rem;
  font-weight: 600;
  color: var(--text-primary);
}

:host-context(.dark) .whatsapp-username {
  color: var(--dark-text);
}

.whatsapp-status {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

:host-context(.dark) .whatsapp-status {
  color: var(--text-dim);
}

/* ========================================
   BOUTONS D'ACTION UNIFIÉS
   ======================================== */

.whatsapp-actions {
  display: flex;
  gap: 16px;
}

.whatsapp-action-button {
  background: transparent;
  border: none;
  color: var(--accent-color);
  width: 42px;
  height: 42px;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-smooth);
  position: relative;
  font-size: 1.1rem;
}

/* Boutons d'appel avec forme circulaire */
.whatsapp-action-button.btn-audio-call,
.whatsapp-action-button.btn-video-call {
  border-radius: 50% !important;
  background: rgba(0, 247, 255, 0.1) !important;
  border: 1px solid rgba(0, 247, 255, 0.3) !important;
}

.whatsapp-action-button:hover {
  transform: scale(1.1) translateY(-2px);
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 247, 255, 0.8);
}

:host-context(.dark) .whatsapp-action-button {
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
}

:host-context(.dark) .whatsapp-action-button:hover {
  color: white;
  text-shadow: none;
}

/* ========================================
   ANIMATIONS UNIFIÉES
   ======================================== */

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Mode sombre simplifié */
:host-context(.dark) .futuristic-messages-container {
  background: var(--dark-primary);
}

/* Scrollbars simplifiées */
.futuristic-messages-container::-webkit-scrollbar {
  width: 5px;
}

.futuristic-messages-container::-webkit-scrollbar-thumb {
  background-color: var(--accent-color);
  border-radius: 10px;
}

/* Wrapper de message futuriste */
.futuristic-message-wrapper {
  margin-bottom: 4px;
  position: relative;
  z-index: 1;
}

/* Message futuriste style WhatsApp */
.futuristic-message {
  display: flex;
  align-items: flex-end;
  margin-bottom: 1px;
  position: relative;
  width: 100%;
}

/* Bulle de message simplifiée */
.futuristic-message-bubble {
  margin-bottom: 0.25rem;
  max-width: 70%;
  padding: 0.6rem 0.8rem;
  font-size: 0.9rem;
  line-height: 1.4;
  word-wrap: break-word;
  border-radius: 12px;
  transition: var(--transition-fast);
  animation: fadeIn 0.3s ease-out;
  box-shadow: var(--shadow-modern);
}

.futuristic-message-content {
  max-width: 80%;
}

.futuristic-message-text {
  word-break: break-word;
  line-height: 1.5;
}

/* Séparateur de date futuriste */
.futuristic-date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.5rem 0;
  color: var(--text-dim);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.futuristic-date-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    var(--accent-color),
    transparent
  );
  opacity: 0.3;
}

.futuristic-date-text {
  margin: 0 10px;
  padding: 2px 8px;
  background-color: rgba(0, 247, 255, 0.05);
  border-radius: var(--border-radius-sm);
}

/* Heure du message futuriste */
.futuristic-message-time {
  font-size: 0.7rem;
  margin-top: 0.2rem;
  opacity: 0.7;
  color: var(--text-dim);
}

/* Informations du message futuriste */
.futuristic-message-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
  margin-top: 4px;
  font-size: 0.75rem;
  letter-spacing: 0.02em;
  font-weight: 300;
}

.futuristic-message-current-user .futuristic-message-info {
  color: rgba(255, 255, 255, 0.8);
}

.futuristic-message-other-user .futuristic-message-info {
  color: rgba(0, 247, 255, 0.7);
}

.futuristic-message-status {
  color: rgba(0, 247, 255, 0.9);
}

/* Messages utilisateur simplifiés */
.futuristic-message-current-user {
  justify-content: flex-end;
  display: flex;
  animation: slideInRight 0.3s ease-out;
}

.futuristic-message-current-user .futuristic-message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  max-width: 80%;
}

.futuristic-message-current-user .futuristic-message-bubble {
  background: var(--primary-gradient);
  color: white;
  border-radius: 18px 18px 4px 18px;
}

/* Messages autres utilisateurs ultra-simplifiés */
.futuristic-message-other-user {
  justify-content: flex-start;
  display: flex;
}

.futuristic-message-other-user .futuristic-message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 80%;
}

.futuristic-message-other-user .futuristic-message-bubble {
  background: var(--modern-white);
  color: var(--modern-gray-800);
  border: 1px solid var(--modern-gray-200);
}

:host-context(.dark) .futuristic-message-other-user .futuristic-message-bubble {
  background: var(--dark-surface);
  color: var(--dark-text);
  border: 1px solid var(--dark-accent);
}

.futuristic-message-other-user .futuristic-message-bubble:hover {
  transform: translateY(-2px);
  background: rgba(230, 235, 245, 0.9);
  box-shadow: 0 6px 20px rgba(79, 95, 173, 0.2);
}

:host-context(.dark)
  .futuristic-message-other-user
  .futuristic-message-bubble:hover {
  background: rgba(40, 40, 50, 0.9);
  box-shadow: 0 6px 20px rgba(0, 247, 255, 0.3);
}

/* ========================================
   ZONE DE SAISIE SIMPLIFIÉE
   ======================================== */

.futuristic-input-container {
  padding: 6px 10px;
  background-color: var(--glass-effect);
  min-height: 50px;
  position: relative;
  z-index: 10;
  border-top: 1px solid var(--glass-border);
}

.futuristic-input-form {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  z-index: 2;
}

.futuristic-input-tools {
  display: flex;
  gap: 8px;
}

/* Style .futuristic-tool-button supprimé - Déjà défini dans la section consolidée */

.futuristic-input-field {
  flex: 1;
  font-size: 0.9rem;
  padding: 10px 16px;
  height: 44px;
  border-radius: 22px;
  border: 1px solid rgba(0, 247, 255, 0.2);
  background-color: rgba(0, 247, 255, 0.05);
  color: var(--dark-text);
  transition: var(--transition-smooth);
}

/* Boutons d'envoi ultra-consolidés */
.futuristic-send-button,
.whatsapp-send-button,
.whatsapp-voice-button {
  background: var(--accent-gradient);
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
}

.whatsapp-voice-button {
  background: var(--neon-orange);
  width: 46px;
  height: 46px;
  font-size: 1.2rem;
  margin-left: 12px;
}

.futuristic-send-button:hover,
.whatsapp-send-button:hover:not(:disabled),
.whatsapp-voice-button:hover {
  transform: scale(1.15);
}

.futuristic-send-button:disabled,
.whatsapp-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* ========================================
   CONTENEUR PRINCIPAL SIMPLIFIÉ
   ======================================== */

.futuristic-chat-container {
  width: 100%;
  margin: 0 auto;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: var(--shadow-modern);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

:host-context(:not(.dark)) .futuristic-chat-container {
  background: var(--modern-white);
  border: 1px solid var(--modern-gray-200);
}

:host-context(.dark) .futuristic-chat-container {
  background: var(--dark-primary);
  border: 1px solid var(--dark-accent);
}

.message-date-divider {
  font-size: 0.75rem;
  color: #6c757d;
  margin: 0.5rem 0;
  text-align: center;
}

/* Images simplifiées */
.futuristic-message-image-container {
  margin: 2px 0;
  max-width: 220px;
  transition: var(--transition-fast);
}

.futuristic-image-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: var(--shadow-modern);
  transition: var(--transition-fast);
}

.futuristic-message-image {
  width: 100%;
  display: block;
  transition: var(--transition-fast);
  cursor: pointer;
}

.futuristic-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-fast);
  color: white;
  font-size: 1.5rem;
}

.futuristic-image-wrapper:hover .futuristic-image-overlay {
  opacity: 1;
}

.futuristic-image-wrapper:hover {
  transform: translateY(-2px);
}

/* Styles pour le conteneur d'image en plein écran - Optimisé */
.fullscreen-image-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-image-wrapper {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.fullscreen-image-wrapper img {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.3);
}

:host-context(.dark) .whatsapp-input-container {
  background: var(--dark-secondary);
  background-image: linear-gradient(
      135deg,
      rgba(79, 172, 254, 0.08) 0%,
      rgba(245, 87, 108, 0.08) 100%
    ),
    radial-gradient(
      circle at 30% 70%,
      rgba(102, 126, 234, 0.1) 0%,
      transparent 50%
    );
  border-top: 1px solid var(--dark-accent);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
}

/* Formulaire d'entrée style WhatsApp */
.whatsapp-input-form {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

/* Outils d'entrée style WhatsApp */
.whatsapp-input-tools {
  display: flex;
  gap: 8px;
  margin-right: 8px;
}

/* Boutons d'outils consolidés avec .futuristic-tool-button */
.whatsapp-tool-button,
.futuristic-tool-button {
  width: 38px;
  height: 38px;
  background: transparent;
  border: none;
  color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: 1rem;
}

/* États hover consolidés */
.whatsapp-tool-button:hover,
.whatsapp-tool-button.active,
.whatsapp-voice-button:hover,
.futuristic-tool-button:hover,
.futuristic-tool-button.active {
  transform: scale(1.1);
  color: white;
  background: var(--accent-color);
}

.whatsapp-input-field,
.futuristic-input-field {
  flex: 1;
  background-color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  color: #333;
  font-size: 0.9375rem;
  transition: var(--transition-fast);
  outline: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

:host-context(.dark) .whatsapp-input-field,
:host-context(.dark) .futuristic-input-field {
  background-color: #3a3a3a;
  color: #e0e0e0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.whatsapp-input-field:focus,
.futuristic-input-field:focus {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Style .whatsapp-voice-button supprimé - Déjà défini dans la section consolidée */

/* Aperçu de fichier simplifié */
.whatsapp-file-preview,
.futuristic-file-preview {
  position: relative;
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-modern);
  max-width: 200px;
}

.whatsapp-preview-image,
.futuristic-preview-image {
  width: 100%;
  display: block;
}

.whatsapp-remove-button,
.futuristic-remove-button {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
}

.whatsapp-remove-button:hover,
.futuristic-remove-button:hover {
  background: rgba(255, 0, 0, 0.7);
}

/* Sélecteur d'émojis simplifié */
.whatsapp-emoji-picker {
  position: absolute;
  bottom: 60px;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 8px 8px 0 0;
  box-shadow: var(--shadow-modern);
  z-index: 100;
  height: 250px;
  overflow: hidden;
}

:host-context(.dark) .whatsapp-emoji-picker {
  background-color: var(--dark-secondary);
}

.whatsapp-emoji-categories {
  display: flex;
  padding: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.whatsapp-emoji-category,
.whatsapp-emoji-item {
  width: 36px;
  height: 36px;
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
}

.whatsapp-emoji-list {
  flex: 1;
  padding: 8px;
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  overflow-y: auto;
}

.whatsapp-emoji-item:hover {
  background-color: var(--accent-color);
}

/* Modal d'appel simplifié */
.whatsapp-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.whatsapp-call-modal-content {
  background-color: white;
  border-radius: 12px;
  width: 300px;
  max-width: 90%;
  overflow: hidden;
  box-shadow: var(--shadow-modern);
}

:host-context(.dark) .whatsapp-call-modal-content {
  background-color: var(--dark-secondary);
}

.whatsapp-call-header {
  padding: 20px;
  text-align: center;
}

.whatsapp-call-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 15px;
}

.whatsapp-call-actions {
  display: flex;
}

.whatsapp-call-reject,
.whatsapp-call-accept {
  flex: 1;
  padding: 15px;
  border: none;
  cursor: pointer;
  transition: var(--transition-fast);
  color: white;
}

.whatsapp-call-reject {
  background-color: #f44336;
}

.whatsapp-call-accept {
  background-color: var(--accent-color);
}

/* Boutons de fermeture consolidés */
.close-fullscreen-btn,
.image-fullscreen-dialog .close-button {
  position: absolute;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
}

.close-fullscreen-btn {
  top: -40px;
  right: -40px;
  width: 40px;
  height: 40px;
  font-size: 24px;
}

.image-fullscreen-dialog .close-button {
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  font-size: 30px;
  z-index: 10000000;
}

.close-fullscreen-btn:hover,
.image-fullscreen-dialog .close-button:hover {
  background: var(--accent-color);
  transform: scale(1.1);
}

.image-fullscreen-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.image-fullscreen-container img {
  max-width: 85%;
  max-height: 85%;
  object-fit: contain;
  border-radius: 12px;
  box-shadow: var(--shadow-modern);
}

/* États de chargement consolidés */
.futuristic-loading,
.futuristic-conversation-start {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.futuristic-loading-text {
  color: var(--accent-color);
  font-size: 0.875rem;
}

.futuristic-typing-dots {
  display: flex;
  gap: 5px;
}

.futuristic-typing-dot {
  width: 8px;
  height: 8px;
  background-color: var(--accent-color);
  border-radius: 50%;
  animation: pulse 1.5s infinite ease-in-out;
}

.futuristic-conversation-start {
  width: 100%;
  margin: 20px 0;
}

.futuristic-conversation-start-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    var(--accent-color),
    transparent
  );
  opacity: 0.3;
}

.futuristic-conversation-start-text {
  margin: 0 10px;
  padding: 4px 12px;
  background-color: var(--glass-effect);
  border-radius: var(--border-radius-md);
  font-size: 0.75rem;
  text-transform: uppercase;
  color: var(--accent-color);
}

/* États et messages ultra-consolidés */
.futuristic-error-container,
.futuristic-voice-message-container {
  margin: 15px;
  padding: 15px;
  border-radius: 14px;
  display: flex;
  align-items: flex-start;
  transition: var(--transition-fast);
}

.futuristic-error-container {
  background: rgba(255, 0, 0, 0.1);
  border-left: 3px solid #ff3b30;
}

.futuristic-voice-message-container {
  min-width: 200px;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: var(--shadow-modern);
  padding: 8px;
  margin: 0;
}

.futuristic-error-icon,
.futuristic-voice-play-button {
  color: #ff3b30;
  font-size: 1.25rem;
  margin-right: 15px;
}

/* Style .futuristic-voice-play-button supprimé - Déjà défini dans la section consolidée */

.futuristic-error-title {
  color: #ff3b30;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.futuristic-error-message {
  color: var(--text-dim);
  font-size: 0.8125rem;
}

.futuristic-message-pending,
.futuristic-message-sending {
  opacity: 0.7;
}

.futuristic-message-error {
  border: 1px solid rgba(239, 68, 68, 0.5);
}

.futuristic-voice-message {
  display: flex;
  align-items: center;
  gap: 12px;
}

.futuristic-voice-waveform {
  display: flex;
  align-items: center;
  gap: 3px;
  height: 36px;
}

.futuristic-voice-bar {
  width: 3px;
  background-color: currentColor;
  border-radius: 4px;
  transition: var(--transition-fast);
}

/* Indicateurs ultra-consolidés */
.image-modal img {
  animation: scaleIn 0.3s ease-in-out;
  transition: transform 0.2s ease;
}

.futuristic-typing-indicator,
.futuristic-no-messages {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.futuristic-no-messages {
  flex-direction: column;
  height: 100%;
  padding: 20px;
  text-align: center;
}

.futuristic-typing-bubble {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 10px 15px;
  margin-left: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.futuristic-no-messages-icon {
  font-size: 3rem;
  color: var(--accent-color);
  margin-bottom: 20px;
  opacity: 0.7;
}

.futuristic-no-messages-text {
  color: var(--text-dim);
  font-size: 1rem;
  margin-bottom: 30px;
  line-height: 1.5;
}

/* Aperçu de fichier futuriste */
.futuristic-file-preview {
  position: relative;
  margin-bottom: 10px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  max-width: 200px;
  border: 2px solid rgba(0, 247, 255, 0.3);
  box-shadow: var(--glow-effect);
}

.futuristic-preview-image {
  width: 100%;
  display: block;
}

.futuristic-remove-button {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.futuristic-remove-button:hover {
  background: rgba(255, 0, 0, 0.7);
  transform: scale(1.1);
}

/* Interface d'appel ultra-simplifiée */
.active-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 9998;
}

.video-call-interface,
.audio-call-interface,
.minimized-call-interface {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-call-interface {
  background: #000;
}

.audio-call-interface,
.minimized-call-interface {
  background: var(--primary-gradient);
}

.minimized-call-interface {
  justify-content: space-between;
  padding: 1rem;
  color: white;
}

.remote-video,
.local-video {
  border-radius: 12px;
  object-fit: cover;
}

.remote-video {
  width: 100%;
  height: 100%;
}

.local-video {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
}

.call-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  padding: 2rem;
}

.control-buttons,
.minimized-controls {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.minimized-controls {
  gap: 0.5rem;
}

.control-btn,
.minimized-btn {
  border: none;
  background: transparent;
  color: white;
  cursor: pointer;
  transition: var(--transition-fast);
}

.control-btn {
  width: 60px;
  height: 60px;
  font-size: 1.5rem;
}

.minimized-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.control-btn.end-call,
.minimized-btn.end-call {
  border-radius: 50%;
  background: #ff6b6b;
}

.control-btn.end-call:hover,
.minimized-btn:hover {
  transform: scale(1.15);
}

.minimized-info {
  display: flex;
  flex-direction: column;
}

/* Responsive simplifié */
@media (max-width: 768px) {
  .local-video,
  .local-avatar {
    width: 120px;
    height: 90px;
    top: 10px;
    right: 10px;
  }

  .control-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .call-controls {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .control-btn {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
}

/* Panneaux consolidés */
.notification-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-panel {
  width: 90%;
  max-width: 600px;
  height: 80%;
  background: var(--dark-primary);
  border-radius: 20px;
  box-shadow: var(--shadow-modern);
  border: 2px solid var(--glass-border);
  display: flex;
  flex-direction: column;
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: var(--primary-gradient);
}

.notification-title {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.notification-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  cursor: pointer;
  transition: var(--transition-fast);
  color: white;
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
  background: var(--glass-effect);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--glass-border);
  transition: var(--transition-fast);
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.notification-content {
  flex: 1;
}

/* Éléments interactifs consolidés */
.status-action-btn,
.item-action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: var(--glass-effect);
  color: var(--text-dim);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.status-action-btn:hover,
.item-action-btn:hover {
  background: var(--accent-color);
  color: white;
  transform: scale(1.1);
}

.notification-badge {
  background: var(--accent-gradient);
  border-radius: 6px;
  padding: 0 4px;
  font-size: 10px;
  font-weight: 700;
  color: white;
  min-width: 18px;
  height: 18px;
  z-index: 10;
}

/* Boutons et contrôles ultra-consolidés - Suppression de duplication */
.whatsapp-action-button,
.voice-play-btn-modern,
.futuristic-voice-play-button {
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
}

.whatsapp-action-button {
  width: 44px;
  height: 44px;
  background: transparent;
  font-size: 1.1rem;
}

.voice-play-btn-modern,
.futuristic-voice-play-button {
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
}

.voice-play-btn-modern:hover,
.futuristic-voice-play-button:hover {
  transform: scale(1.2);
}

/* Menu consolidé */
.theme-selector-menu {
  display: block;
  visibility: visible;
  opacity: 1;
  z-index: 100;
  background: var(--glass-effect);
  border: 2px solid var(--glass-border);
  box-shadow: var(--shadow-modern);
}

.theme-selector-menu a {
  transition: var(--transition-fast);
  border-radius: 8px;
  margin: 2px;
}

.theme-selector-menu a:hover {
  transform: translateX(5px);
  background: var(--accent-color);
}

/* Optimisations finales */
* {
  box-sizing: border-box;
}

@media (pointer: coarse) {
  .whatsapp-action-button,
  .whatsapp-send-button,
  .whatsapp-voice-button {
    min-width: 44px;
    min-height: 44px;
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms;
    transition-duration: 0.01ms;
  }
}
