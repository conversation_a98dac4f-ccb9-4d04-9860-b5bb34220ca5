{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nfunction AddProjectComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \" Titre est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProjectComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \" Description est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProjectComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \" Date limite est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProjectComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \" Groupe est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"opacity-50 cursor-not-allowed\": a0\n  };\n};\nexport let AddProjectComponent = /*#__PURE__*/(() => {\n  class AddProjectComponent {\n    constructor(fb, projetService, router, authService) {\n      this.fb = fb;\n      this.projetService = projetService;\n      this.router = router;\n      this.authService = authService;\n      this.selectedFiles = [];\n      this.isSubmitting = false;\n      this.projetForm = this.fb.group({\n        titre: ['', Validators.required],\n        description: [''],\n        dateLimite: ['', Validators.required],\n        fichiers: [null],\n        groupe: ['', Validators.required] // ← champ pour l'ID du groupe\n      });\n    }\n\n    onFileChange(event) {\n      const input = event.target;\n      if (input.files) {\n        this.selectedFiles = Array.from(input.files);\n      }\n    }\n    onSubmit() {\n      if (this.projetForm.invalid) return;\n      this.isSubmitting = true;\n      console.log('Soumission du formulaire de projet');\n      const formData = new FormData();\n      formData.append('titre', this.projetForm.value.titre);\n      formData.append('description', this.projetForm.value.description || '');\n      formData.append('dateLimite', this.projetForm.value.dateLimite);\n      formData.append('groupe', this.projetForm.value.groupe);\n      // Méthode 1: Via le service d'authentification (recommandée)\n      const userIdFromService = this.authService.getCurrentUserId();\n      // Méthode 2: Via le currentUser du service\n      const currentUser = this.authService.getCurrentUser();\n      // Méthode 3: Vérification localStorage\n      const user = localStorage.getItem('user');\n      // Utiliser l'ID du service d'authentification en priorité\n      let userId = userIdFromService;\n      if (!userId && currentUser) {\n        userId = currentUser._id || currentUser.id;\n      }\n      if (!userId && user) {\n        userId = JSON.parse(user).id;\n      }\n      if (userId) {\n        formData.append('professeur', userId);\n      } else {\n        alert(\"Erreur: Impossible de récupérer l'ID utilisateur. Veuillez vous reconnecter.\");\n        return;\n      }\n      this.selectedFiles.forEach(file => {\n        formData.append('fichiers', file);\n      });\n      console.log('Données du formulaire:', {\n        titre: this.projetForm.value.titre,\n        description: this.projetForm.value.description,\n        dateLimite: this.projetForm.value.dateLimite,\n        groupe: this.projetForm.value.groupe,\n        fichiers: this.selectedFiles.map(f => f.name)\n      });\n      this.projetService.addProjet(formData).subscribe({\n        next: () => {\n          console.log('Projet ajouté avec succès');\n          alert('Projet ajouté avec succès');\n          this.router.navigate(['/admin/projects']);\n        },\n        error: err => {\n          console.error(\"Erreur lors de l'ajout du projet:\", err);\n          alert(\"Erreur lors de l'ajout du projet: \" + (err.error?.message || err.message || 'Erreur inconnue'));\n          this.isSubmitting = false;\n        },\n        complete: () => {\n          this.isSubmitting = false;\n        }\n      });\n    }\n    static {\n      this.ɵfac = function AddProjectComponent_Factory(t) {\n        return new (t || AddProjectComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthuserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AddProjectComponent,\n        selectors: [[\"app-add-project\"]],\n        decls: 42,\n        vars: 9,\n        consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"min-h-screen\"], [1, \"max-w-2xl\", \"mx-auto\", \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\"], [1, \"border-t-4\", \"border-[#4f5fad]\", \"p-6\"], [1, \"text-2xl\", \"font-bold\", \"text-center\", \"text-[#4f5fad]\"], [1, \"p-6\", \"md:p-8\"], [\"enctype\", \"multipart/form-data\", 1, \"space-y-6\", 3, \"formGroup\", \"ngSubmit\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"type\", \"text\", \"id\", \"titre\", \"formControlName\", \"titre\", \"placeholder\", \"Titre du projet\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"focus:border-[#7826b5]\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"transition-all\"], [\"class\", \"text-[#ff6b69] text-sm mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"id\", \"description\", \"formControlName\", \"description\", \"placeholder\", \"Description du projet\", \"rows\", \"4\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"focus:border-[#7826b5]\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"transition-all\"], [\"for\", \"dateLimite\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"type\", \"date\", \"id\", \"dateLimite\", \"formControlName\", \"dateLimite\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"focus:border-[#7826b5]\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"transition-all\"], [\"for\", \"fichiers\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"type\", \"file\", \"id\", \"fichiers\", \"multiple\", \"\", 1, \"w-full\", \"px-4\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"bg-white\", \"focus:outline-none\", 3, \"change\"], [\"for\", \"groupe\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"id\", \"groupe\", \"formControlName\", \"groupe\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"focus:border-[#7826b5]\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"bg-white\", \"transition-all\"], [\"value\", \"\"], [\"value\", \"2cinfo1\"], [\"value\", \"2cinfo2\"], [\"value\", \"2cinfo3\"], [\"type\", \"submit\", 1, \"w-full\", \"bg-[#7826b5]\", \"hover:bg-[#4f5fad]\", \"text-white\", \"font-bold\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"focus:ring-offset-2\", 3, \"disabled\", \"ngClass\"], [1, \"text-[#ff6b69]\", \"text-sm\", \"mt-1\"]],\n        template: function AddProjectComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n            i0.ɵɵtext(4, \" Ajouter un projet \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"form\", 5);\n            i0.ɵɵlistener(\"ngSubmit\", function AddProjectComponent_Template_form_ngSubmit_6_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(7, \"div\")(8, \"label\", 6);\n            i0.ɵɵtext(9, \"Titre\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"input\", 7);\n            i0.ɵɵtemplate(11, AddProjectComponent_div_11_Template, 2, 0, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\")(13, \"label\", 9);\n            i0.ɵɵtext(14, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(15, \"textarea\", 10);\n            i0.ɵɵtemplate(16, AddProjectComponent_div_16_Template, 2, 0, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\")(18, \"label\", 11);\n            i0.ɵɵtext(19, \"Date limite\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(20, \"input\", 12);\n            i0.ɵɵtemplate(21, AddProjectComponent_div_21_Template, 2, 0, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\")(23, \"label\", 13);\n            i0.ɵɵtext(24, \"Fichiers\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"input\", 14);\n            i0.ɵɵlistener(\"change\", function AddProjectComponent_Template_input_change_25_listener($event) {\n              return ctx.onFileChange($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\")(27, \"label\", 15);\n            i0.ɵɵtext(28, \"Groupe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"select\", 16)(30, \"option\", 17);\n            i0.ɵɵtext(31, \"-- Choisir un groupe --\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"option\", 18);\n            i0.ɵɵtext(33, \"2cinfo1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"option\", 19);\n            i0.ɵɵtext(35, \"2cinfo2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"option\", 20);\n            i0.ɵɵtext(37, \"2cinfo3\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(38, AddProjectComponent_div_38_Template, 2, 0, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"div\")(40, \"button\", 21);\n            i0.ɵɵtext(41, \" Ajouter \");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formGroup\", ctx.projetForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.projetForm.get(\"titre\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.projetForm.get(\"titre\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.projetForm.get(\"description\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.projetForm.get(\"description\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.projetForm.get(\"dateLimite\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.projetForm.get(\"dateLimite\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.projetForm.get(\"groupe\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.projetForm.get(\"groupe\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.projetForm.invalid)(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx.projetForm.invalid));\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName]\n      });\n    }\n  }\n  return AddProjectComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}