{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/reunion.service\";\nimport * as i3 from \"src/app/services/planning.service\";\nimport * as i4 from \"@app/services/data.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/services/authuser.service\";\nimport * as i7 from \"@angular/common\";\nfunction ReunionFormComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error.message || \"Une erreur est survenue\", \" \");\n  }\n}\nfunction ReunionFormComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.successMessage, \" \");\n  }\n}\nfunction ReunionFormComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Le titre est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" La date est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" L'heure de d\\u00E9but est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" L'heure de fin est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_41_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r11.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r11.titre);\n  }\n}\nfunction ReunionFormComponent_div_41_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Le planning est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 33);\n    i0.ɵɵtext(2, \"Planning *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 34)(4, \"option\", 35);\n    i0.ɵɵtext(5, \"S\\u00E9lectionnez un planning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ReunionFormComponent_div_41_option_6_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ReunionFormComponent_div_41_div_7_Template, 2, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.plannings);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r6.reunionForm.get(\"planning\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r6.reunionForm.get(\"planning\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction ReunionFormComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵtext(2, \"Planning s\\u00E9lectionn\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 41);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedPlanning.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Du \", i0.ɵɵpipeBind2(7, 3, ctx_r7.selectedPlanning.dateDebut, \"mediumDate\"), \" au \", i0.ɵɵpipeBind2(8, 6, ctx_r7.selectedPlanning.dateFin, \"mediumDate\"), \" \");\n  }\n}\nfunction ReunionFormComponent_ng_container_47_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r14._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r14.username);\n  }\n}\nfunction ReunionFormComponent_ng_container_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReunionFormComponent_ng_container_47_option_1_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const users_r12 = ctx.ngIf;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", users_r12);\n  }\n}\nexport let ReunionFormComponent = /*#__PURE__*/(() => {\n  class ReunionFormComponent {\n    constructor(fb, reunionService, planningService, userService, route, router, authService) {\n      this.fb = fb;\n      this.reunionService = reunionService;\n      this.planningService = planningService;\n      this.userService = userService;\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n      this.plannings = [];\n      this.loading = true;\n      this.isSubmitting = false;\n      this.error = null;\n      this.successMessage = null;\n      this.isEditMode = false;\n      this.currentReunionId = null;\n      this.planningIdFromUrl = null;\n      this.selectedPlanning = null;\n      this.reunionForm = this.fb.group({\n        titre: ['', Validators.required],\n        description: [''],\n        date: ['', Validators.required],\n        heureDebut: ['', Validators.required],\n        heureFin: ['', Validators.required],\n        lieu: [''],\n        lienVisio: [''],\n        planning: ['', Validators.required],\n        participants: [[]]\n      });\n      this.users$ = this.userService.getAllUsers();\n    }\n    ngOnInit() {\n      this.loadPlannings();\n      this.checkEditMode();\n      this.checkPlanningParam();\n    }\n    checkEditMode() {\n      const reunionId = this.route.snapshot.paramMap.get('id');\n      if (reunionId) {\n        this.isEditMode = true;\n        this.currentReunionId = reunionId;\n        this.loadReunion(reunionId);\n      }\n    }\n    loadPlannings() {\n      const userId = this.authService.getCurrentUserId();\n      if (!userId) return;\n      this.planningService.getPlanningsByUser(userId).subscribe({\n        next: response => {\n          this.plannings = response.plannings || [];\n        },\n        error: err => {\n          this.error = err;\n        }\n      });\n    }\n    loadReunion(id) {\n      this.reunionService.getReunionById(id).subscribe({\n        next: reunion => {\n          this.reunionForm.patchValue({\n            titre: reunion.titre,\n            description: reunion.description,\n            dateDebut: this.formatDateForInput(reunion.dateDebut),\n            dateFin: this.formatDateForInput(reunion.dateFin),\n            lieu: reunion.lieu,\n            lienVisio: reunion.lienVisio,\n            planningId: reunion.planningId,\n            participants: reunion.participants\n          });\n          this.loading = false;\n        },\n        error: err => {\n          this.error = err;\n          this.loading = false;\n        }\n      });\n    }\n    formatDateForInput(date) {\n      return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm\n    }\n\n    checkPlanningParam() {\n      const planningId = this.route.snapshot.queryParamMap.get('planningId');\n      if (planningId) {\n        this.planningIdFromUrl = planningId;\n        // Si un ID de planning est fourni dans les paramètres de requête, le sélectionner automatiquement\n        this.reunionForm.patchValue({\n          planning: planningId\n        });\n        // Récupérer les détails du planning pour l'affichage\n        this.planningService.getPlanningById(planningId).subscribe({\n          next: response => {\n            this.selectedPlanning = response.planning;\n          },\n          error: err => {\n            console.error('Erreur lors de la récupération du planning:', err);\n          }\n        });\n      }\n    }\n    onSubmit() {\n      if (this.reunionForm.invalid) return;\n      this.isSubmitting = true;\n      this.error = null;\n      this.successMessage = null;\n      const formValue = this.reunionForm.value;\n      const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]\n      const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]\n      const heureFin = formValue.heureFin;\n      const reunionData = {\n        titre: formValue.titre,\n        description: formValue.description,\n        date: date,\n        heureDebut: heureDebut,\n        heureFin: heureFin,\n        lieu: formValue.lieu,\n        lienVisio: formValue.lienVisio,\n        planning: formValue.planning,\n        participants: formValue.participants || []\n      };\n      console.log(reunionData);\n      this.reunionService.createReunion(reunionData).subscribe({\n        next: () => {\n          this.successMessage = 'Réunion créée avec succès!';\n          this.isSubmitting = false;\n          // Redirect to reunions list page\n          this.router.navigate(['/reunions']);\n        },\n        error: err => {\n          this.error = err;\n          this.isSubmitting = false;\n        }\n      });\n    }\n    resetForm() {\n      // Reset the form to its initial state\n      this.reunionForm.reset({\n        titre: '',\n        description: '',\n        date: '',\n        heureDebut: '',\n        heureFin: '',\n        lieu: '',\n        lienVisio: '',\n        planning: '',\n        participants: []\n      });\n      // Mark the form as pristine and untouched to reset validation states\n      this.reunionForm.markAsPristine();\n      this.reunionForm.markAsUntouched();\n    }\n    goReunion() {\n      this.router.navigate(['/reunions']);\n    }\n    static {\n      this.ɵfac = function ReunionFormComponent_Factory(t) {\n        return new (t || ReunionFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ReunionService), i0.ɵɵdirectiveInject(i3.PlanningService), i0.ɵɵdirectiveInject(i4.DataService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.AuthuserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReunionFormComponent,\n        selectors: [[\"app-reunion-form\"]],\n        decls: 54,\n        vars: 15,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"mb-6\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [\"for\", \"date\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"date\", \"type\", \"date\", \"formControlName\", \"date\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureDebut\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureDebut\", \"type\", \"time\", \"formControlName\", \"heureDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureFin\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureFin\", \"type\", \"time\", \"formControlName\", \"heureFin\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"lieu\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lieu\", \"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"lienVisio\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lienVisio\", \"type\", \"url\", \"formControlName\", \"lienVisio\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [4, \"ngIf\"], [\"class\", \"bg-gray-50 p-3 rounded-md\", 4, \"ngIf\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"mt-6\", \"flex\", \"justify-end\", \"space-x-3\"], [\"type\", \"button\", 1, \"px-4\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-50\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-4\", \"py-2\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-green-100\", \"border\", \"border-green-400\", \"text-green-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [\"for\", \"planning\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"planning\", \"formControlName\", \"planning\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"bg-gray-50\", \"p-3\", \"rounded-md\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [1, \"text-gray-900\", \"font-medium\"], [1, \"text-xs\", \"text-gray-500\", \"mt-1\"]],\n        template: function ReunionFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\", 1);\n            i0.ɵɵtext(2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"form\", 2);\n            i0.ɵɵlistener(\"ngSubmit\", function ReunionFormComponent_Template_form_ngSubmit_3_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtemplate(4, ReunionFormComponent_div_4_Template, 2, 1, \"div\", 3);\n            i0.ɵɵtemplate(5, ReunionFormComponent_div_5_Template, 2, 1, \"div\", 4);\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\")(8, \"label\", 6);\n            i0.ɵɵtext(9, \"Titre *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"input\", 7);\n            i0.ɵɵtemplate(11, ReunionFormComponent_div_11_Template, 2, 0, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\")(13, \"label\", 9);\n            i0.ɵɵtext(14, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(15, \"textarea\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 11)(17, \"div\")(18, \"label\", 12);\n            i0.ɵɵtext(19, \"Date *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(20, \"input\", 13);\n            i0.ɵɵtemplate(21, ReunionFormComponent_div_21_Template, 2, 0, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\")(23, \"label\", 14);\n            i0.ɵɵtext(24, \"Heure de d\\u00E9but *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"input\", 15);\n            i0.ɵɵtemplate(26, ReunionFormComponent_div_26_Template, 2, 0, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"div\")(28, \"label\", 16);\n            i0.ɵɵtext(29, \"Heure de fin *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(30, \"input\", 17);\n            i0.ɵɵtemplate(31, ReunionFormComponent_div_31_Template, 2, 0, \"div\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 18)(33, \"div\")(34, \"label\", 19);\n            i0.ɵɵtext(35, \"Lieu\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(36, \"input\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"div\")(38, \"label\", 21);\n            i0.ɵɵtext(39, \"Lien Visio\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(40, \"input\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(41, ReunionFormComponent_div_41_Template, 8, 2, \"div\", 23);\n            i0.ɵɵtemplate(42, ReunionFormComponent_div_42_Template, 9, 9, \"div\", 24);\n            i0.ɵɵelementStart(43, \"div\")(44, \"label\", 25);\n            i0.ɵɵtext(45, \"Participants\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"select\", 26);\n            i0.ɵɵtemplate(47, ReunionFormComponent_ng_container_47_Template, 2, 1, \"ng-container\", 23);\n            i0.ɵɵpipe(48, \"async\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(49, \"div\", 27)(50, \"button\", 28);\n            i0.ɵɵlistener(\"click\", function ReunionFormComponent_Template_button_click_50_listener() {\n              return ctx.goReunion();\n            });\n            i0.ɵɵtext(51, \" Annuler \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"button\", 29);\n            i0.ɵɵtext(53);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_4_0;\n            let tmp_5_0;\n            let tmp_6_0;\n            let tmp_7_0;\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier la R\\u00E9union\" : \"Nouvelle R\\u00E9union\", \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.reunionForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_6_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_7_0.touched));\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", !ctx.planningIdFromUrl);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.planningIdFromUrl && ctx.selectedPlanning);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(48, 13, ctx.users$));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.reunionForm.invalid || ctx.isSubmitting);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Enregistrement...\" : \"Enregistrer\", \" \");\n          }\n        },\n        dependencies: [i7.NgForOf, i7.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.AsyncPipe, i7.DatePipe]\n      });\n    }\n  }\n  return ReunionFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}