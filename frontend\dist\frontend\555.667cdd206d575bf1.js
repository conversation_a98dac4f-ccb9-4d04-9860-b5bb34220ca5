"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[555],{555:(fo,et,f)=>{f.r(et),f.d(et,{ProjectsModule:()=>ho});var p=f(177),d=f(4341),b=f(6647),D=f(5312),t=f(7705),O=f(1873),S=f(6535),g=f(4085),w=f(6860),I=f(5964),B=f(6697),it=f(6977),F=f(8203);class X{attach(o){return this._attachedHost=o,o.attach(this)}detach(){let o=this._attachedHost;null!=o&&(this._attachedHost=null,o.detach())}get isAttached(){return null!=this._attachedHost}setAttachedHost(o){this._attachedHost=o}}class H extends X{constructor(o,e,i,n,r){super(),this.component=o,this.viewContainerRef=e,this.injector=i,this.componentFactoryResolver=n,this.projectableNodes=r}}class ot extends X{constructor(o,e,i,n){super(),this.templateRef=o,this.viewContainerRef=e,this.context=i,this.injector=n}get origin(){return this.templateRef.elementRef}attach(o,e=this.context){return this.context=e,super.attach(o)}detach(){return this.context=void 0,super.detach()}}class Bt extends X{constructor(o){super(),this.element=o instanceof t.aKT?o.nativeElement:o}}class U{constructor(){this._isDisposed=!1,this.attachDomPortal=null}hasAttached(){return!!this._attachedPortal}attach(o){return o instanceof H?(this._attachedPortal=o,this.attachComponentPortal(o)):o instanceof ot?(this._attachedPortal=o,this.attachTemplatePortal(o)):this.attachDomPortal&&o instanceof Bt?(this._attachedPortal=o,this.attachDomPortal(o)):void 0}detach(){this._attachedPortal&&(this._attachedPortal.setAttachedHost(null),this._attachedPortal=null),this._invokeDisposeFn()}dispose(){this.hasAttached()&&this.detach(),this._invokeDisposeFn(),this._isDisposed=!0}setDisposeFn(o){this._disposeFn=o}_invokeDisposeFn(){this._disposeFn&&(this._disposeFn(),this._disposeFn=null)}}class Nt extends U{constructor(o,e,i,n,r){super(),this.outletElement=o,this._componentFactoryResolver=e,this._appRef=i,this._defaultInjector=n,this.attachDomPortal=s=>{const l=s.element,u=this._document.createComment("dom-portal");l.parentNode.insertBefore(u,l),this.outletElement.appendChild(l),this._attachedPortal=s,super.setDisposeFn(()=>{u.parentNode&&u.parentNode.replaceChild(l,u)})},this._document=r}attachComponentPortal(o){const i=(o.componentFactoryResolver||this._componentFactoryResolver).resolveComponentFactory(o.component);let n;return o.viewContainerRef?(n=o.viewContainerRef.createComponent(i,o.viewContainerRef.length,o.injector||o.viewContainerRef.injector,o.projectableNodes||void 0),this.setDisposeFn(()=>n.destroy())):(n=i.create(o.injector||this._defaultInjector||t.zZn.NULL),this._appRef.attachView(n.hostView),this.setDisposeFn(()=>{this._appRef.viewCount>0&&this._appRef.detachView(n.hostView),n.destroy()})),this.outletElement.appendChild(this._getComponentRootNode(n)),this._attachedPortal=o,n}attachTemplatePortal(o){let e=o.viewContainerRef,i=e.createEmbeddedView(o.templateRef,o.context,{injector:o.injector});return i.rootNodes.forEach(n=>this.outletElement.appendChild(n)),i.detectChanges(),this.setDisposeFn(()=>{let n=e.indexOf(i);-1!==n&&e.remove(n)}),this._attachedPortal=o,i}dispose(){super.dispose(),this.outletElement.remove()}_getComponentRootNode(o){return o.hostView.rootNodes[0]}}let q=(()=>{class a extends U{constructor(e,i,n){super(),this._componentFactoryResolver=e,this._viewContainerRef=i,this._isInitialized=!1,this.attached=new t.bkB,this.attachDomPortal=r=>{const s=r.element,l=this._document.createComment("dom-portal");r.setAttachedHost(this),s.parentNode.insertBefore(l,s),this._getRootNode().appendChild(s),this._attachedPortal=r,super.setDisposeFn(()=>{l.parentNode&&l.parentNode.replaceChild(s,l)})},this._document=n}get portal(){return this._attachedPortal}set portal(e){this.hasAttached()&&!e&&!this._isInitialized||(this.hasAttached()&&super.detach(),e&&super.attach(e),this._attachedPortal=e||null)}get attachedRef(){return this._attachedRef}ngOnInit(){this._isInitialized=!0}ngOnDestroy(){super.dispose(),this._attachedRef=this._attachedPortal=null}attachComponentPortal(e){e.setAttachedHost(this);const i=null!=e.viewContainerRef?e.viewContainerRef:this._viewContainerRef,r=(e.componentFactoryResolver||this._componentFactoryResolver).resolveComponentFactory(e.component),s=i.createComponent(r,i.length,e.injector||i.injector,e.projectableNodes||void 0);return i!==this._viewContainerRef&&this._getRootNode().appendChild(s.hostView.rootNodes[0]),super.setDisposeFn(()=>s.destroy()),this._attachedPortal=e,this._attachedRef=s,this.attached.emit(s),s}attachTemplatePortal(e){e.setAttachedHost(this);const i=this._viewContainerRef.createEmbeddedView(e.templateRef,e.context,{injector:e.injector});return super.setDisposeFn(()=>this._viewContainerRef.clear()),this._attachedPortal=e,this._attachedRef=i,this.attached.emit(i),i}_getRootNode(){const e=this._viewContainerRef.element.nativeElement;return e.nodeType===e.ELEMENT_NODE?e:e.parentNode}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(t.OM3),t.rXU(t.c1b),t.rXU(p.qQ))}}static{this.\u0275dir=t.FsC({type:a,selectors:[["","cdkPortalOutlet",""]],inputs:{portal:["cdkPortalOutlet","portal"]},outputs:{attached:"attached"},exportAs:["cdkPortalOutlet"],features:[t.Vt3]})}}return a})(),N=(()=>{class a{static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({})}}return a})();var E=f(1413),nt=f(8359),at=f(7786);const rt=(0,w.CZ)();class Vt{constructor(o,e){this._viewportRuler=o,this._previousHTMLStyles={top:"",left:""},this._isEnabled=!1,this._document=e}attach(){}enable(){if(this._canBeEnabled()){const o=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=o.style.left||"",this._previousHTMLStyles.top=o.style.top||"",o.style.left=(0,g.a1)(-this._previousScrollPosition.left),o.style.top=(0,g.a1)(-this._previousScrollPosition.top),o.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){const o=this._document.documentElement,i=o.style,n=this._document.body.style,r=i.scrollBehavior||"",s=n.scrollBehavior||"";this._isEnabled=!1,i.left=this._previousHTMLStyles.left,i.top=this._previousHTMLStyles.top,o.classList.remove("cdk-global-scrollblock"),rt&&(i.scrollBehavior=n.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),rt&&(i.scrollBehavior=r,n.scrollBehavior=s)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;const e=this._document.body,i=this._viewportRuler.getViewportSize();return e.scrollHeight>i.height||e.scrollWidth>i.width}}class $t{constructor(o,e,i,n){this._scrollDispatcher=o,this._ngZone=e,this._viewportRuler=i,this._config=n,this._scrollSubscription=null,this._detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}}attach(o){this._overlayRef=o}enable(){if(this._scrollSubscription)return;const o=this._scrollDispatcher.scrolled(0).pipe((0,I.p)(e=>!e||!this._overlayRef.overlayElement.contains(e.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=o.subscribe(()=>{const e=this._viewportRuler.getViewportScrollPosition().top;Math.abs(e-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=o.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}}class st{enable(){}disable(){}attach(){}}function Z(a,o){return o.some(e=>a.bottom<e.top||a.top>e.bottom||a.right<e.left||a.left>e.right)}function lt(a,o){return o.some(e=>a.top<e.top||a.bottom>e.bottom||a.left<e.left||a.right>e.right)}class Gt{constructor(o,e,i,n){this._scrollDispatcher=o,this._viewportRuler=e,this._ngZone=i,this._config=n,this._scrollSubscription=null}attach(o){this._overlayRef=o}enable(){this._scrollSubscription||(this._scrollSubscription=this._scrollDispatcher.scrolled(this._config?this._config.scrollThrottle:0).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){const e=this._overlayRef.overlayElement.getBoundingClientRect(),{width:i,height:n}=this._viewportRuler.getViewportSize();Z(e,[{width:i,height:n,bottom:n,right:i,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}}))}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}}let zt=(()=>{class a{constructor(e,i,n,r){this._scrollDispatcher=e,this._viewportRuler=i,this._ngZone=n,this.noop=()=>new st,this.close=s=>new $t(this._scrollDispatcher,this._ngZone,this._viewportRuler,s),this.block=()=>new Vt(this._viewportRuler,this._document),this.reposition=s=>new Gt(this._scrollDispatcher,this._viewportRuler,this._ngZone,s),this._document=r}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(S.R),t.KVO(S.Xj),t.KVO(t.SKi),t.KVO(p.qQ))}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})();class dt{constructor(o){if(this.scrollStrategy=new st,this.panelClass="",this.hasBackdrop=!1,this.backdropClass="cdk-overlay-dark-backdrop",this.disposeOnNavigation=!1,o){const e=Object.keys(o);for(const i of e)void 0!==o[i]&&(this[i]=o[i])}}}class Yt{constructor(o,e){this.connectionPair=o,this.scrollableViewProperties=e}}let ct=(()=>{class a{constructor(e){this._attachedOverlays=[],this._document=e}ngOnDestroy(){this.detach()}add(e){this.remove(e),this._attachedOverlays.push(e)}remove(e){const i=this._attachedOverlays.indexOf(e);i>-1&&this._attachedOverlays.splice(i,1),0===this._attachedOverlays.length&&this.detach()}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(p.qQ))}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})(),Xt=(()=>{class a extends ct{constructor(e,i){super(e),this._ngZone=i,this._keydownListener=n=>{const r=this._attachedOverlays;for(let s=r.length-1;s>-1;s--)if(r[s]._keydownEvents.observers.length>0){const l=r[s]._keydownEvents;this._ngZone?this._ngZone.run(()=>l.next(n)):l.next(n);break}}}add(e){super.add(e),this._isAttached||(this._ngZone?this._ngZone.runOutsideAngular(()=>this._document.body.addEventListener("keydown",this._keydownListener)):this._document.body.addEventListener("keydown",this._keydownListener),this._isAttached=!0)}detach(){this._isAttached&&(this._document.body.removeEventListener("keydown",this._keydownListener),this._isAttached=!1)}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(p.qQ),t.KVO(t.SKi,8))}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})(),Ht=(()=>{class a extends ct{constructor(e,i,n){super(e),this._platform=i,this._ngZone=n,this._cursorStyleIsSet=!1,this._pointerDownListener=r=>{this._pointerDownEventTarget=(0,w.Fb)(r)},this._clickListener=r=>{const s=(0,w.Fb)(r),l="click"===r.type&&this._pointerDownEventTarget?this._pointerDownEventTarget:s;this._pointerDownEventTarget=null;const u=this._attachedOverlays.slice();for(let m=u.length-1;m>-1;m--){const c=u[m];if(c._outsidePointerEvents.observers.length<1||!c.hasAttached())continue;if(c.overlayElement.contains(s)||c.overlayElement.contains(l))break;const h=c._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>h.next(r)):h.next(r)}}}add(e){if(super.add(e),!this._isAttached){const i=this._document.body;this._ngZone?this._ngZone.runOutsideAngular(()=>this._addEventListeners(i)):this._addEventListeners(i),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=i.style.cursor,i.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){if(this._isAttached){const e=this._document.body;e.removeEventListener("pointerdown",this._pointerDownListener,!0),e.removeEventListener("click",this._clickListener,!0),e.removeEventListener("auxclick",this._clickListener,!0),e.removeEventListener("contextmenu",this._clickListener,!0),this._platform.IOS&&this._cursorStyleIsSet&&(e.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1}}_addEventListeners(e){e.addEventListener("pointerdown",this._pointerDownListener,!0),e.addEventListener("click",this._clickListener,!0),e.addEventListener("auxclick",this._clickListener,!0),e.addEventListener("contextmenu",this._clickListener,!0)}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(p.qQ),t.KVO(w.OD),t.KVO(t.SKi,8))}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})(),V=(()=>{class a{constructor(e,i){this._platform=i,this._document=e}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._containerElement||this._createContainer(),this._containerElement}_createContainer(){const e="cdk-overlay-container";if(this._platform.isBrowser||(0,w.v8)()){const n=this._document.querySelectorAll(`.${e}[platform="server"], .${e}[platform="test"]`);for(let r=0;r<n.length;r++)n[r].remove()}const i=this._document.createElement("div");i.classList.add(e),(0,w.v8)()?i.setAttribute("platform","test"):this._platform.isBrowser||i.setAttribute("platform","server"),this._document.body.appendChild(i),this._containerElement=i}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(p.qQ),t.KVO(w.OD))}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})();class P{constructor(o,e,i,n,r,s,l,u,m,c=!1){this._portalOutlet=o,this._host=e,this._pane=i,this._config=n,this._ngZone=r,this._keyboardDispatcher=s,this._document=l,this._location=u,this._outsideClickDispatcher=m,this._animationsDisabled=c,this._backdropElement=null,this._backdropClick=new E.B,this._attachments=new E.B,this._detachments=new E.B,this._locationChanges=nt.yU.EMPTY,this._backdropClickHandler=h=>this._backdropClick.next(h),this._backdropTransitionendHandler=h=>{this._disposeBackdrop(h.target)},this._keydownEvents=new E.B,this._outsidePointerEvents=new E.B,n.scrollStrategy&&(this._scrollStrategy=n.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=n.positionStrategy}get overlayElement(){return this._pane}get backdropElement(){return this._backdropElement}get hostElement(){return this._host}attach(o){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);const e=this._portalOutlet.attach(o);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._ngZone.onStable.pipe((0,B.s)(1)).subscribe(()=>{this.hasAttached()&&this.updatePosition()}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),"function"==typeof e?.onDestroy&&e.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),e}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();const o=this._portalOutlet.detach();return this._detachments.next(),this._keyboardDispatcher.remove(this),this._detachContentWhenStable(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),o}dispose(){const o=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._disposeBackdrop(this._backdropElement),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._previousHostParent=this._pane=this._host=null,o&&this._detachments.next(),this._detachments.complete()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(o){o!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=o,this.hasAttached()&&(o.attach(this),this.updatePosition()))}updateSize(o){this._config={...this._config,...o},this._updateElementSize()}setDirection(o){this._config={...this._config,direction:o},this._updateElementDirection()}addPanelClass(o){this._pane&&this._toggleClasses(this._pane,o,!0)}removePanelClass(o){this._pane&&this._toggleClasses(this._pane,o,!1)}getDirection(){const o=this._config.direction;return o?"string"==typeof o?o:o.value:"ltr"}updateScrollStrategy(o){o!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=o,this.hasAttached()&&(o.attach(this),o.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;const o=this._pane.style;o.width=(0,g.a1)(this._config.width),o.height=(0,g.a1)(this._config.height),o.minWidth=(0,g.a1)(this._config.minWidth),o.minHeight=(0,g.a1)(this._config.minHeight),o.maxWidth=(0,g.a1)(this._config.maxWidth),o.maxHeight=(0,g.a1)(this._config.maxHeight)}_togglePointerEvents(o){this._pane.style.pointerEvents=o?"":"none"}_attachBackdrop(){const o="cdk-overlay-backdrop-showing";this._backdropElement=this._document.createElement("div"),this._backdropElement.classList.add("cdk-overlay-backdrop"),this._animationsDisabled&&this._backdropElement.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropElement,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropElement,this._host),this._backdropElement.addEventListener("click",this._backdropClickHandler),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>{this._backdropElement&&this._backdropElement.classList.add(o)})}):this._backdropElement.classList.add(o)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){const o=this._backdropElement;if(o){if(this._animationsDisabled)return void this._disposeBackdrop(o);o.classList.remove("cdk-overlay-backdrop-showing"),this._ngZone.runOutsideAngular(()=>{o.addEventListener("transitionend",this._backdropTransitionendHandler)}),o.style.pointerEvents="none",this._backdropTimeout=this._ngZone.runOutsideAngular(()=>setTimeout(()=>{this._disposeBackdrop(o)},500))}}_toggleClasses(o,e,i){const n=(0,g.FG)(e||[]).filter(r=>!!r);n.length&&(i?o.classList.add(...n):o.classList.remove(...n))}_detachContentWhenStable(){this._ngZone.runOutsideAngular(()=>{const o=this._ngZone.onStable.pipe((0,it.Q)((0,at.h)(this._attachments,this._detachments))).subscribe(()=>{(!this._pane||!this._host||0===this._pane.children.length)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),o.unsubscribe())})})}_disposeScrollStrategy(){const o=this._scrollStrategy;o&&(o.disable(),o.detach&&o.detach())}_disposeBackdrop(o){o&&(o.removeEventListener("click",this._backdropClickHandler),o.removeEventListener("transitionend",this._backdropTransitionendHandler),o.remove(),this._backdropElement===o&&(this._backdropElement=null)),this._backdropTimeout&&(clearTimeout(this._backdropTimeout),this._backdropTimeout=void 0)}}const ut="cdk-overlay-connected-position-bounding-box",Ut=/([A-Za-z%]+)$/;class qt{get positions(){return this._preferredPositions}constructor(o,e,i,n,r){this._viewportRuler=e,this._document=i,this._platform=n,this._overlayContainer=r,this._lastBoundingBoxSize={width:0,height:0},this._isPushed=!1,this._canPush=!0,this._growAfterOpen=!1,this._hasFlexibleDimensions=!0,this._positionLocked=!1,this._viewportMargin=0,this._scrollables=[],this._preferredPositions=[],this._positionChanges=new E.B,this._resizeSubscription=nt.yU.EMPTY,this._offsetX=0,this._offsetY=0,this._appliedPanelClasses=[],this.positionChanges=this._positionChanges,this.setOrigin(o)}attach(o){this._validatePositions(),o.hostElement.classList.add(ut),this._overlayRef=o,this._boundingBox=o.hostElement,this._pane=o.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition)return void this.reapplyLastPosition();this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();const o=this._originRect,e=this._overlayRect,i=this._viewportRect,n=this._containerRect,r=[];let s;for(let l of this._preferredPositions){let u=this._getOriginPoint(o,n,l),m=this._getOverlayPoint(u,e,l),c=this._getOverlayFit(m,e,i,l);if(c.isCompletelyWithinViewport)return this._isPushed=!1,void this._applyPosition(l,u);this._canFitWithFlexibleDimensions(c,m,i)?r.push({position:l,origin:u,overlayRect:e,boundingBoxRect:this._calculateBoundingBoxRect(u,l)}):(!s||s.overlayFit.visibleArea<c.visibleArea)&&(s={overlayFit:c,overlayPoint:m,originPoint:u,position:l,overlayRect:e})}if(r.length){let l=null,u=-1;for(const m of r){const c=m.boundingBoxRect.width*m.boundingBoxRect.height*(m.position.weight||1);c>u&&(u=c,l=m)}return this._isPushed=!1,void this._applyPosition(l.position,l.origin)}if(this._canPush)return this._isPushed=!0,void this._applyPosition(s.position,s.originPoint);this._applyPosition(s.position,s.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&j(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(ut),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;const o=this._lastPosition;if(o){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();const e=this._getOriginPoint(this._originRect,this._containerRect,o);this._applyPosition(o,e)}else this.apply()}withScrollableContainers(o){return this._scrollables=o,this}withPositions(o){return this._preferredPositions=o,-1===o.indexOf(this._lastPosition)&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(o){return this._viewportMargin=o,this}withFlexibleDimensions(o=!0){return this._hasFlexibleDimensions=o,this}withGrowAfterOpen(o=!0){return this._growAfterOpen=o,this}withPush(o=!0){return this._canPush=o,this}withLockedPosition(o=!0){return this._positionLocked=o,this}setOrigin(o){return this._origin=o,this}withDefaultOffsetX(o){return this._offsetX=o,this}withDefaultOffsetY(o){return this._offsetY=o,this}withTransformOriginOn(o){return this._transformOriginSelector=o,this}_getOriginPoint(o,e,i){let n,r;if("center"==i.originX)n=o.left+o.width/2;else{const s=this._isRtl()?o.right:o.left,l=this._isRtl()?o.left:o.right;n="start"==i.originX?s:l}return e.left<0&&(n-=e.left),r="center"==i.originY?o.top+o.height/2:"top"==i.originY?o.top:o.bottom,e.top<0&&(r-=e.top),{x:n,y:r}}_getOverlayPoint(o,e,i){let n,r;return n="center"==i.overlayX?-e.width/2:"start"===i.overlayX?this._isRtl()?-e.width:0:this._isRtl()?0:-e.width,r="center"==i.overlayY?-e.height/2:"top"==i.overlayY?0:-e.height,{x:o.x+n,y:o.y+r}}_getOverlayFit(o,e,i,n){const r=pt(e);let{x:s,y:l}=o,u=this._getOffset(n,"x"),m=this._getOffset(n,"y");u&&(s+=u),m&&(l+=m);let C=0-l,x=l+r.height-i.height,y=this._subtractOverflows(r.width,0-s,s+r.width-i.width),_=this._subtractOverflows(r.height,C,x),L=y*_;return{visibleArea:L,isCompletelyWithinViewport:r.width*r.height===L,fitsInViewportVertically:_===r.height,fitsInViewportHorizontally:y==r.width}}_canFitWithFlexibleDimensions(o,e,i){if(this._hasFlexibleDimensions){const n=i.bottom-e.y,r=i.right-e.x,s=mt(this._overlayRef.getConfig().minHeight),l=mt(this._overlayRef.getConfig().minWidth);return(o.fitsInViewportVertically||null!=s&&s<=n)&&(o.fitsInViewportHorizontally||null!=l&&l<=r)}return!1}_pushOverlayOnScreen(o,e,i){if(this._previousPushAmount&&this._positionLocked)return{x:o.x+this._previousPushAmount.x,y:o.y+this._previousPushAmount.y};const n=pt(e),r=this._viewportRect,s=Math.max(o.x+n.width-r.width,0),l=Math.max(o.y+n.height-r.height,0),u=Math.max(r.top-i.top-o.y,0),m=Math.max(r.left-i.left-o.x,0);let c=0,h=0;return c=n.width<=r.width?m||-s:o.x<this._viewportMargin?r.left-i.left-o.x:0,h=n.height<=r.height?u||-l:o.y<this._viewportMargin?r.top-i.top-o.y:0,this._previousPushAmount={x:c,y:h},{x:o.x+c,y:o.y+h}}_applyPosition(o,e){if(this._setTransformOrigin(o),this._setOverlayElementStyles(e,o),this._setBoundingBoxStyles(e,o),o.panelClass&&this._addPanelClasses(o.panelClass),this._lastPosition=o,this._positionChanges.observers.length){const i=this._getScrollVisibility(),n=new Yt(o,i);this._positionChanges.next(n)}this._isInitialRender=!1}_setTransformOrigin(o){if(!this._transformOriginSelector)return;const e=this._boundingBox.querySelectorAll(this._transformOriginSelector);let i,n=o.overlayY;i="center"===o.overlayX?"center":this._isRtl()?"start"===o.overlayX?"right":"left":"start"===o.overlayX?"left":"right";for(let r=0;r<e.length;r++)e[r].style.transformOrigin=`${i} ${n}`}_calculateBoundingBoxRect(o,e){const i=this._viewportRect,n=this._isRtl();let r,s,l,c,h,C;if("top"===e.overlayY)s=o.y,r=i.height-s+this._viewportMargin;else if("bottom"===e.overlayY)l=i.height-o.y+2*this._viewportMargin,r=i.height-l+this._viewportMargin;else{const x=Math.min(i.bottom-o.y+i.top,o.y),y=this._lastBoundingBoxSize.height;r=2*x,s=o.y-x,r>y&&!this._isInitialRender&&!this._growAfterOpen&&(s=o.y-y/2)}if("end"===e.overlayX&&!n||"start"===e.overlayX&&n)C=i.width-o.x+this._viewportMargin,c=o.x-this._viewportMargin;else if("start"===e.overlayX&&!n||"end"===e.overlayX&&n)h=o.x,c=i.right-o.x;else{const x=Math.min(i.right-o.x+i.left,o.x),y=this._lastBoundingBoxSize.width;c=2*x,h=o.x-x,c>y&&!this._isInitialRender&&!this._growAfterOpen&&(h=o.x-y/2)}return{top:s,left:h,bottom:l,right:C,width:c,height:r}}_setBoundingBoxStyles(o,e){const i=this._calculateBoundingBoxRect(o,e);!this._isInitialRender&&!this._growAfterOpen&&(i.height=Math.min(i.height,this._lastBoundingBoxSize.height),i.width=Math.min(i.width,this._lastBoundingBoxSize.width));const n={};if(this._hasExactPosition())n.top=n.left="0",n.bottom=n.right=n.maxHeight=n.maxWidth="",n.width=n.height="100%";else{const r=this._overlayRef.getConfig().maxHeight,s=this._overlayRef.getConfig().maxWidth;n.height=(0,g.a1)(i.height),n.top=(0,g.a1)(i.top),n.bottom=(0,g.a1)(i.bottom),n.width=(0,g.a1)(i.width),n.left=(0,g.a1)(i.left),n.right=(0,g.a1)(i.right),n.alignItems="center"===e.overlayX?"center":"end"===e.overlayX?"flex-end":"flex-start",n.justifyContent="center"===e.overlayY?"center":"bottom"===e.overlayY?"flex-end":"flex-start",r&&(n.maxHeight=(0,g.a1)(r)),s&&(n.maxWidth=(0,g.a1)(s))}this._lastBoundingBoxSize=i,j(this._boundingBox.style,n)}_resetBoundingBoxStyles(){j(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){j(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(o,e){const i={},n=this._hasExactPosition(),r=this._hasFlexibleDimensions,s=this._overlayRef.getConfig();if(n){const c=this._viewportRuler.getViewportScrollPosition();j(i,this._getExactOverlayY(e,o,c)),j(i,this._getExactOverlayX(e,o,c))}else i.position="static";let l="",u=this._getOffset(e,"x"),m=this._getOffset(e,"y");u&&(l+=`translateX(${u}px) `),m&&(l+=`translateY(${m}px)`),i.transform=l.trim(),s.maxHeight&&(n?i.maxHeight=(0,g.a1)(s.maxHeight):r&&(i.maxHeight="")),s.maxWidth&&(n?i.maxWidth=(0,g.a1)(s.maxWidth):r&&(i.maxWidth="")),j(this._pane.style,i)}_getExactOverlayY(o,e,i){let n={top:"",bottom:""},r=this._getOverlayPoint(e,this._overlayRect,o);return this._isPushed&&(r=this._pushOverlayOnScreen(r,this._overlayRect,i)),"bottom"===o.overlayY?n.bottom=this._document.documentElement.clientHeight-(r.y+this._overlayRect.height)+"px":n.top=(0,g.a1)(r.y),n}_getExactOverlayX(o,e,i){let s,n={left:"",right:""},r=this._getOverlayPoint(e,this._overlayRect,o);return this._isPushed&&(r=this._pushOverlayOnScreen(r,this._overlayRect,i)),s=this._isRtl()?"end"===o.overlayX?"left":"right":"end"===o.overlayX?"right":"left","right"===s?n.right=this._document.documentElement.clientWidth-(r.x+this._overlayRect.width)+"px":n.left=(0,g.a1)(r.x),n}_getScrollVisibility(){const o=this._getOriginRect(),e=this._pane.getBoundingClientRect(),i=this._scrollables.map(n=>n.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:lt(o,i),isOriginOutsideView:Z(o,i),isOverlayClipped:lt(e,i),isOverlayOutsideView:Z(e,i)}}_subtractOverflows(o,...e){return e.reduce((i,n)=>i-Math.max(n,0),o)}_getNarrowedViewportRect(){const o=this._document.documentElement.clientWidth,e=this._document.documentElement.clientHeight,i=this._viewportRuler.getViewportScrollPosition();return{top:i.top+this._viewportMargin,left:i.left+this._viewportMargin,right:i.left+o-this._viewportMargin,bottom:i.top+e-this._viewportMargin,width:o-2*this._viewportMargin,height:e-2*this._viewportMargin}}_isRtl(){return"rtl"===this._overlayRef.getDirection()}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(o,e){return"x"===e?null==o.offsetX?this._offsetX:o.offsetX:null==o.offsetY?this._offsetY:o.offsetY}_validatePositions(){}_addPanelClasses(o){this._pane&&(0,g.FG)(o).forEach(e=>{""!==e&&-1===this._appliedPanelClasses.indexOf(e)&&(this._appliedPanelClasses.push(e),this._pane.classList.add(e))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(o=>{this._pane.classList.remove(o)}),this._appliedPanelClasses=[])}_getOriginRect(){const o=this._origin;if(o instanceof t.aKT)return o.nativeElement.getBoundingClientRect();if(o instanceof Element)return o.getBoundingClientRect();const e=o.width||0,i=o.height||0;return{top:o.y,bottom:o.y+i,left:o.x,right:o.x+e,height:i,width:e}}}function j(a,o){for(let e in o)o.hasOwnProperty(e)&&(a[e]=o[e]);return a}function mt(a){if("number"!=typeof a&&null!=a){const[o,e]=a.split(Ut);return e&&"px"!==e?null:parseFloat(o)}return a||null}function pt(a){return{top:Math.floor(a.top),right:Math.floor(a.right),bottom:Math.floor(a.bottom),left:Math.floor(a.left),width:Math.floor(a.width),height:Math.floor(a.height)}}const ht="cdk-global-overlay-wrapper";class Zt{constructor(){this._cssPosition="static",this._topOffset="",this._bottomOffset="",this._alignItems="",this._xPosition="",this._xOffset="",this._width="",this._height="",this._isDisposed=!1}attach(o){const e=o.getConfig();this._overlayRef=o,this._width&&!e.width&&o.updateSize({width:this._width}),this._height&&!e.height&&o.updateSize({height:this._height}),o.hostElement.classList.add(ht),this._isDisposed=!1}top(o=""){return this._bottomOffset="",this._topOffset=o,this._alignItems="flex-start",this}left(o=""){return this._xOffset=o,this._xPosition="left",this}bottom(o=""){return this._topOffset="",this._bottomOffset=o,this._alignItems="flex-end",this}right(o=""){return this._xOffset=o,this._xPosition="right",this}start(o=""){return this._xOffset=o,this._xPosition="start",this}end(o=""){return this._xOffset=o,this._xPosition="end",this}width(o=""){return this._overlayRef?this._overlayRef.updateSize({width:o}):this._width=o,this}height(o=""){return this._overlayRef?this._overlayRef.updateSize({height:o}):this._height=o,this}centerHorizontally(o=""){return this.left(o),this._xPosition="center",this}centerVertically(o=""){return this.top(o),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;const o=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement.style,i=this._overlayRef.getConfig(),{width:n,height:r,maxWidth:s,maxHeight:l}=i,u=!("100%"!==n&&"100vw"!==n||s&&"100%"!==s&&"100vw"!==s),m=!("100%"!==r&&"100vh"!==r||l&&"100%"!==l&&"100vh"!==l),c=this._xPosition,h=this._xOffset,C="rtl"===this._overlayRef.getConfig().direction;let x="",y="",_="";u?_="flex-start":"center"===c?(_="center",C?y=h:x=h):C?"left"===c||"end"===c?(_="flex-end",x=h):("right"===c||"start"===c)&&(_="flex-start",y=h):"left"===c||"start"===c?(_="flex-start",x=h):("right"===c||"end"===c)&&(_="flex-end",y=h),o.position=this._cssPosition,o.marginLeft=u?"0":x,o.marginTop=m?"0":this._topOffset,o.marginBottom=this._bottomOffset,o.marginRight=u?"0":y,e.justifyContent=_,e.alignItems=m?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;const o=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement,i=e.style;e.classList.remove(ht),i.justifyContent=i.alignItems=o.marginTop=o.marginBottom=o.marginLeft=o.marginRight=o.position="",this._overlayRef=null,this._isDisposed=!0}}let Kt=(()=>{class a{constructor(e,i,n,r){this._viewportRuler=e,this._document=i,this._platform=n,this._overlayContainer=r}global(){return new Zt}flexibleConnectedTo(e){return new qt(e,this._viewportRuler,this._document,this._platform,this._overlayContainer)}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(S.Xj),t.KVO(p.qQ),t.KVO(w.OD),t.KVO(V))}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})(),Wt=0,M=(()=>{class a{constructor(e,i,n,r,s,l,u,m,c,h,C,x){this.scrollStrategies=e,this._overlayContainer=i,this._componentFactoryResolver=n,this._positionBuilder=r,this._keyboardDispatcher=s,this._injector=l,this._ngZone=u,this._document=m,this._directionality=c,this._location=h,this._outsideClickDispatcher=C,this._animationsModuleType=x}create(e){const i=this._createHostElement(),n=this._createPaneElement(i),r=this._createPortalOutlet(n),s=new dt(e);return s.direction=s.direction||this._directionality.value,new P(r,i,n,s,this._ngZone,this._keyboardDispatcher,this._document,this._location,this._outsideClickDispatcher,"NoopAnimations"===this._animationsModuleType)}position(){return this._positionBuilder}_createPaneElement(e){const i=this._document.createElement("div");return i.id="cdk-overlay-"+Wt++,i.classList.add("cdk-overlay-pane"),e.appendChild(i),i}_createHostElement(){const e=this._document.createElement("div");return this._overlayContainer.getContainerElement().appendChild(e),e}_createPortalOutlet(e){return this._appRef||(this._appRef=this._injector.get(t.o8S)),new Nt(e,this._componentFactoryResolver,this._appRef,this._injector,this._document)}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(zt),t.KVO(V),t.KVO(t.OM3),t.KVO(Kt),t.KVO(Xt),t.KVO(t.zZn),t.KVO(t.SKi),t.KVO(p.qQ),t.KVO(F.dS),t.KVO(p.aZ),t.KVO(Ht),t.KVO(t.bc$,8))}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})();const te={provide:new t.nKC("cdk-connected-overlay-scroll-strategy"),deps:[M],useFactory:function Jt(a){return()=>a.scrollStrategies.reposition()}};let ft=(()=>{class a{static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({providers:[M,te],imports:[F.jI,N,S.E9,S.E9]})}}return a})();var k=f(9037),$=f(7336),gt=f(9030),bt=f(7673),_t=f(9172);function ee(a,o){}class G{constructor(){this.role="dialog",this.panelClass="",this.hasBackdrop=!0,this.backdropClass="",this.disableClose=!1,this.width="",this.height="",this.data=null,this.ariaDescribedBy=null,this.ariaLabelledBy=null,this.ariaLabel=null,this.ariaModal=!0,this.autoFocus="first-tabbable",this.restoreFocus=!0,this.closeOnNavigation=!0,this.closeOnDestroy=!0,this.closeOnOverlayDetachments=!0}}let vt=(()=>{class a extends U{constructor(e,i,n,r,s,l,u,m){super(),this._elementRef=e,this._focusTrapFactory=i,this._config=r,this._interactivityChecker=s,this._ngZone=l,this._overlayRef=u,this._focusMonitor=m,this._elementFocusedBeforeDialogWasOpened=null,this._closeInteractionType=null,this._ariaLabelledByQueue=[],this.attachDomPortal=c=>{this._portalOutlet.hasAttached();const h=this._portalOutlet.attachDomPortal(c);return this._contentAttached(),h},this._document=n,this._config.ariaLabelledBy&&this._ariaLabelledByQueue.push(this._config.ariaLabelledBy)}_contentAttached(){this._initializeFocusTrap(),this._handleBackdropClicks(),this._captureInitialFocus()}_captureInitialFocus(){this._trapFocus()}ngOnDestroy(){this._restoreFocus()}attachComponentPortal(e){this._portalOutlet.hasAttached();const i=this._portalOutlet.attachComponentPortal(e);return this._contentAttached(),i}attachTemplatePortal(e){this._portalOutlet.hasAttached();const i=this._portalOutlet.attachTemplatePortal(e);return this._contentAttached(),i}_recaptureFocus(){this._containsFocus()||this._trapFocus()}_forceFocus(e,i){this._interactivityChecker.isFocusable(e)||(e.tabIndex=-1,this._ngZone.runOutsideAngular(()=>{const n=()=>{e.removeEventListener("blur",n),e.removeEventListener("mousedown",n),e.removeAttribute("tabindex")};e.addEventListener("blur",n),e.addEventListener("mousedown",n)})),e.focus(i)}_focusByCssSelector(e,i){let n=this._elementRef.nativeElement.querySelector(e);n&&this._forceFocus(n,i)}_trapFocus(){const e=this._elementRef.nativeElement;switch(this._config.autoFocus){case!1:case"dialog":this._containsFocus()||e.focus();break;case!0:case"first-tabbable":this._focusTrap.focusInitialElementWhenReady().then(i=>{i||this._focusDialogContainer()});break;case"first-heading":this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role="heading"]');break;default:this._focusByCssSelector(this._config.autoFocus)}}_restoreFocus(){const e=this._config.restoreFocus;let i=null;if("string"==typeof e?i=this._document.querySelector(e):"boolean"==typeof e?i=e?this._elementFocusedBeforeDialogWasOpened:null:e&&(i=e),this._config.restoreFocus&&i&&"function"==typeof i.focus){const n=(0,w.vc)(),r=this._elementRef.nativeElement;(!n||n===this._document.body||n===r||r.contains(n))&&(this._focusMonitor?(this._focusMonitor.focusVia(i,this._closeInteractionType),this._closeInteractionType=null):i.focus())}this._focusTrap&&this._focusTrap.destroy()}_focusDialogContainer(){this._elementRef.nativeElement.focus&&this._elementRef.nativeElement.focus()}_containsFocus(){const e=this._elementRef.nativeElement,i=(0,w.vc)();return e===i||e.contains(i)}_initializeFocusTrap(){this._focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement),this._document&&(this._elementFocusedBeforeDialogWasOpened=(0,w.vc)())}_handleBackdropClicks(){this._overlayRef.backdropClick().subscribe(()=>{this._config.disableClose&&this._recaptureFocus()})}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(t.aKT),t.rXU(k.GX),t.rXU(p.qQ,8),t.rXU(G),t.rXU(k.Z7),t.rXU(t.SKi),t.rXU(P),t.rXU(k.FN))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["cdk-dialog-container"]],viewQuery:function(i,n){if(1&i&&t.GBs(q,7),2&i){let r;t.mGM(r=t.lsd())&&(n._portalOutlet=r.first)}},hostAttrs:["tabindex","-1",1,"cdk-dialog-container"],hostVars:6,hostBindings:function(i,n){2&i&&t.BMQ("id",n._config.id||null)("role",n._config.role)("aria-modal",n._config.ariaModal)("aria-labelledby",n._config.ariaLabel?null:n._ariaLabelledByQueue[0])("aria-label",n._config.ariaLabel)("aria-describedby",n._config.ariaDescribedBy||null)},features:[t.Vt3],decls:1,vars:0,consts:[["cdkPortalOutlet",""]],template:function(i,n){1&i&&t.DNE(0,ee,0,0,"ng-template",0)},dependencies:[q],styles:[".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}"],encapsulation:2})}}return a})();class K{constructor(o,e){this.overlayRef=o,this.config=e,this.closed=new E.B,this.disableClose=e.disableClose,this.backdropClick=o.backdropClick(),this.keydownEvents=o.keydownEvents(),this.outsidePointerEvents=o.outsidePointerEvents(),this.id=e.id,this.keydownEvents.subscribe(i=>{i.keyCode===$._f&&!this.disableClose&&!(0,$.rp)(i)&&(i.preventDefault(),this.close(void 0,{focusOrigin:"keyboard"}))}),this.backdropClick.subscribe(()=>{this.disableClose||this.close(void 0,{focusOrigin:"mouse"})}),this._detachSubscription=o.detachments().subscribe(()=>{!1!==e.closeOnOverlayDetachments&&this.close()})}close(o,e){if(this.containerInstance){const i=this.closed;this.containerInstance._closeInteractionType=e?.focusOrigin||"program",this._detachSubscription.unsubscribe(),this.overlayRef.dispose(),i.next(o),i.complete(),this.componentInstance=this.containerInstance=null}}updatePosition(){return this.overlayRef.updatePosition(),this}updateSize(o="",e=""){return this.overlayRef.updateSize({width:o,height:e}),this}addPanelClass(o){return this.overlayRef.addPanelClass(o),this}removePanelClass(o){return this.overlayRef.removePanelClass(o),this}}const xt=new t.nKC("DialogScrollStrategy"),ie=new t.nKC("DialogData"),oe=new t.nKC("DefaultDialogConfig"),ae={provide:xt,deps:[M],useFactory:function ne(a){return()=>a.scrollStrategies.block()}};let re=0,yt=(()=>{class a{get openDialogs(){return this._parentDialog?this._parentDialog.openDialogs:this._openDialogsAtThisLevel}get afterOpened(){return this._parentDialog?this._parentDialog.afterOpened:this._afterOpenedAtThisLevel}constructor(e,i,n,r,s,l){this._overlay=e,this._injector=i,this._defaultOptions=n,this._parentDialog=r,this._overlayContainer=s,this._openDialogsAtThisLevel=[],this._afterAllClosedAtThisLevel=new E.B,this._afterOpenedAtThisLevel=new E.B,this._ariaHiddenElements=new Map,this.afterAllClosed=(0,gt.v)(()=>this.openDialogs.length?this._getAfterAllClosed():this._getAfterAllClosed().pipe((0,_t.Z)(void 0))),this._scrollStrategy=l}open(e,i){(i={...this._defaultOptions||new G,...i}).id=i.id||"cdk-dialog-"+re++,i.id&&this.getDialogById(i.id);const r=this._getOverlayConfig(i),s=this._overlay.create(r),l=new K(s,i),u=this._attachContainer(s,l,i);return l.containerInstance=u,this._attachDialogContent(e,l,u,i),this.openDialogs.length||this._hideNonDialogContentFromAssistiveTechnology(),this.openDialogs.push(l),l.closed.subscribe(()=>this._removeOpenDialog(l,!0)),this.afterOpened.next(l),l}closeAll(){W(this.openDialogs,e=>e.close())}getDialogById(e){return this.openDialogs.find(i=>i.id===e)}ngOnDestroy(){W(this._openDialogsAtThisLevel,e=>{!1===e.config.closeOnDestroy&&this._removeOpenDialog(e,!1)}),W(this._openDialogsAtThisLevel,e=>e.close()),this._afterAllClosedAtThisLevel.complete(),this._afterOpenedAtThisLevel.complete(),this._openDialogsAtThisLevel=[]}_getOverlayConfig(e){const i=new dt({positionStrategy:e.positionStrategy||this._overlay.position().global().centerHorizontally().centerVertically(),scrollStrategy:e.scrollStrategy||this._scrollStrategy(),panelClass:e.panelClass,hasBackdrop:e.hasBackdrop,direction:e.direction,minWidth:e.minWidth,minHeight:e.minHeight,maxWidth:e.maxWidth,maxHeight:e.maxHeight,width:e.width,height:e.height,disposeOnNavigation:e.closeOnNavigation});return e.backdropClass&&(i.backdropClass=e.backdropClass),i}_attachContainer(e,i,n){const r=n.injector||n.viewContainerRef?.injector,s=[{provide:G,useValue:n},{provide:K,useValue:i},{provide:P,useValue:e}];let l;n.container?"function"==typeof n.container?l=n.container:(l=n.container.type,s.push(...n.container.providers(n))):l=vt;const u=new H(l,n.viewContainerRef,t.zZn.create({parent:r||this._injector,providers:s}),n.componentFactoryResolver);return e.attach(u).instance}_attachDialogContent(e,i,n,r){if(e instanceof t.C4Q){const s=this._createInjector(r,i,n,void 0);let l={$implicit:r.data,dialogRef:i};r.templateContext&&(l={...l,..."function"==typeof r.templateContext?r.templateContext():r.templateContext}),n.attachTemplatePortal(new ot(e,null,l,s))}else{const s=this._createInjector(r,i,n,this._injector),l=n.attachComponentPortal(new H(e,r.viewContainerRef,s,r.componentFactoryResolver));i.componentRef=l,i.componentInstance=l.instance}}_createInjector(e,i,n,r){const s=e.injector||e.viewContainerRef?.injector,l=[{provide:ie,useValue:e.data},{provide:K,useValue:i}];return e.providers&&("function"==typeof e.providers?l.push(...e.providers(i,e,n)):l.push(...e.providers)),e.direction&&(!s||!s.get(F.dS,null,{optional:!0}))&&l.push({provide:F.dS,useValue:{value:e.direction,change:(0,bt.of)()}}),t.zZn.create({parent:s||r,providers:l})}_removeOpenDialog(e,i){const n=this.openDialogs.indexOf(e);n>-1&&(this.openDialogs.splice(n,1),this.openDialogs.length||(this._ariaHiddenElements.forEach((r,s)=>{r?s.setAttribute("aria-hidden",r):s.removeAttribute("aria-hidden")}),this._ariaHiddenElements.clear(),i&&this._getAfterAllClosed().next()))}_hideNonDialogContentFromAssistiveTechnology(){const e=this._overlayContainer.getContainerElement();if(e.parentElement){const i=e.parentElement.children;for(let n=i.length-1;n>-1;n--){const r=i[n];r!==e&&"SCRIPT"!==r.nodeName&&"STYLE"!==r.nodeName&&!r.hasAttribute("aria-live")&&(this._ariaHiddenElements.set(r,r.getAttribute("aria-hidden")),r.setAttribute("aria-hidden","true"))}}}_getAfterAllClosed(){const e=this._parentDialog;return e?e._getAfterAllClosed():this._afterAllClosedAtThisLevel}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(M),t.KVO(t.zZn),t.KVO(oe,8),t.KVO(a,12),t.KVO(V),t.KVO(xt))}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac})}}return a})();function W(a,o){let e=a.length;for(;e--;)o(a[e])}let se=(()=>{class a{static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({providers:[yt,ae],imports:[ft,N,k.Pd,N]})}}return a})();const de=new t.nKC("mat-sanity-checks",{providedIn:"root",factory:function le(){return!0}});let R=(()=>{class a{constructor(e,i,n){this._sanityChecks=i,this._document=n,this._hasDoneGlobalChecks=!1,e._applyBodyHighContrastModeCssClasses(),this._hasDoneGlobalChecks||(this._hasDoneGlobalChecks=!0)}_checkIsEnabled(e){return!(0,w.v8)()&&("boolean"==typeof this._sanityChecks?this._sanityChecks:!!this._sanityChecks[e])}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(k.Q_),t.KVO(de,8),t.KVO(p.qQ))}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({imports:[F.jI,F.jI]})}}return a})();const kt=(0,w.BQ)({passive:!0,capture:!0});class me{constructor(){this._events=new Map,this._delegateEventHandler=o=>{const e=(0,w.Fb)(o);e&&this._events.get(o.type)?.forEach((i,n)=>{(n===e||n.contains(e))&&i.forEach(r=>r.handleEvent(o))})}}addHandler(o,e,i,n){const r=this._events.get(e);if(r){const s=r.get(i);s?s.add(n):r.set(i,new Set([n]))}else this._events.set(e,new Map([[i,new Set([n])]])),o.runOutsideAngular(()=>{document.addEventListener(e,this._delegateEventHandler,kt)})}removeHandler(o,e,i){const n=this._events.get(o);if(!n)return;const r=n.get(e);r&&(r.delete(i),0===r.size&&n.delete(e),0===n.size&&(this._events.delete(o),document.removeEventListener(o,this._delegateEventHandler,kt)))}}class Q{static{this._eventManager=new me}constructor(o,e,i,n){this._target=o,this._ngZone=e,this._platform=n,this._isPointerDown=!1,this._activeRipples=new Map,this._pointerUpEventsRegistered=!1,n.isBrowser&&(this._containerElement=(0,g.i8)(i))}fadeInRipple(o,e,i={}){const n=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),r={...Ct,...i.animation},s=i.radius||function he(a,o,e){const i=Math.max(Math.abs(a-e.left),Math.abs(a-e.right)),n=Math.max(Math.abs(o-e.top),Math.abs(o-e.bottom));return Math.sqrt(i*i+n*n)}(o,e,n),l=o-n.left,u=e-n.top,m=r.enterDuration,c=document.createElement("div");c.classList.add("mat-ripple-element"),c.style.left=l-s+"px",c.style.top=u-s+"px",c.style.height=2*s+"px",c.style.width=2*s+"px",null!=i.color&&(c.style.backgroundColor=i.color),c.style.transitionDuration=`${m}ms`,this._containerElement.appendChild(c);const h=window.getComputedStyle(c),x=h.transitionDuration,y="none"===h.transitionProperty||"0s"===x||"0s, 0s"===x||0===n.width&&0===n.height,_=new ue(this,c,i,y);c.style.transform="scale3d(1, 1, 1)",_.state=0,i.persistent||(this._mostRecentTransientRipple=_);return!y&&(m||r.exitDuration)&&this._ngZone.runOutsideAngular(()=>{const Tt=()=>this._finishRippleTransition(_),Lt=()=>this._destroyRipple(_);c.addEventListener("transitionend",Tt),c.addEventListener("transitioncancel",Lt)}),this._activeRipples.set(_,null),(y||!m)&&this._finishRippleTransition(_),_}fadeOutRipple(o){if(2===o.state||3===o.state)return;const e=o.element,i={...Ct,...o.config.animation};e.style.transitionDuration=`${i.exitDuration}ms`,e.style.opacity="0",o.state=2,(o._animationForciblyDisabledThroughCss||!i.exitDuration)&&this._finishRippleTransition(o)}fadeOutAll(){this._getActiveRipples().forEach(o=>o.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(o=>{o.config.persistent||o.fadeOut()})}setupTriggerEvents(o){const e=(0,g.i8)(o);!this._platform.isBrowser||!e||e===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=e,jt.forEach(i=>{Q._eventManager.addHandler(this._ngZone,i,e,this)}))}handleEvent(o){"mousedown"===o.type?this._onMousedown(o):"touchstart"===o.type?this._onTouchStart(o):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{Ft.forEach(e=>{this._triggerElement.addEventListener(e,this,Dt)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(o){0===o.state?this._startFadeOutTransition(o):2===o.state&&this._destroyRipple(o)}_startFadeOutTransition(o){const e=o===this._mostRecentTransientRipple,{persistent:i}=o.config;o.state=1,!i&&(!e||!this._isPointerDown)&&o.fadeOut()}_destroyRipple(o){const e=this._activeRipples.get(o)??null;this._activeRipples.delete(o),this._activeRipples.size||(this._containerRect=null),o===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),o.state=3,null!==e&&(o.element.removeEventListener("transitionend",e.onTransitionEnd),o.element.removeEventListener("transitioncancel",e.onTransitionCancel)),o.element.remove()}_onMousedown(o){const e=(0,k._G)(o),i=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+800;!this._target.rippleDisabled&&!e&&!i&&(this._isPointerDown=!0,this.fadeInRipple(o.clientX,o.clientY,this._target.rippleConfig))}_onTouchStart(o){if(!this._target.rippleDisabled&&!(0,k.w6)(o)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;const e=o.changedTouches;if(e)for(let i=0;i<e.length;i++)this.fadeInRipple(e[i].clientX,e[i].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(o=>{!o.config.persistent&&(1===o.state||o.config.terminateOnPointerUp&&0===o.state)&&o.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){const o=this._triggerElement;o&&(jt.forEach(e=>Q._eventManager.removeHandler(e,o,this)),this._pointerUpEventsRegistered&&Ft.forEach(e=>o.removeEventListener(e,this,Dt)))}}let fe=(()=>{class a{static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({imports:[R,R]})}}return a})();function ge(a,o){}f(9969);class z{constructor(){this.role="dialog",this.panelClass="",this.hasBackdrop=!0,this.backdropClass="",this.disableClose=!1,this.width="",this.height="",this.maxWidth="80vw",this.data=null,this.ariaDescribedBy=null,this.ariaLabelledBy=null,this.ariaLabel=null,this.ariaModal=!0,this.autoFocus="first-tabbable",this.restoreFocus=!0,this.delayFocusTrap=!0,this.closeOnNavigation=!0}}const J="mdc-dialog--open",Mt="mdc-dialog--opening",Rt="mdc-dialog--closing";let ve=(()=>{class a extends vt{constructor(e,i,n,r,s,l,u,m){super(e,i,n,r,s,l,u,m),this._animationStateChanged=new t.bkB}_captureInitialFocus(){this._config.delayFocusTrap||this._trapFocus()}_openAnimationDone(e){this._config.delayFocusTrap&&this._trapFocus(),this._animationStateChanged.next({state:"opened",totalTime:e})}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(t.aKT),t.rXU(k.GX),t.rXU(p.qQ,8),t.rXU(z),t.rXU(k.Z7),t.rXU(t.SKi),t.rXU(P),t.rXU(k.FN))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["ng-component"]],features:[t.Vt3],decls:0,vars:0,template:function(i,n){},encapsulation:2})}}return a})();const Ot="--mat-dialog-transition-duration";function St(a){return null==a?null:"number"==typeof a?a:a.endsWith("ms")?(0,g.OE)(a.substring(0,a.length-2)):a.endsWith("s")?1e3*(0,g.OE)(a.substring(0,a.length-1)):"0"===a?0:null}let xe=(()=>{class a extends ve{constructor(e,i,n,r,s,l,u,m,c){super(e,i,n,r,s,l,u,c),this._animationMode=m,this._animationsEnabled="NoopAnimations"!==this._animationMode,this._hostElement=this._elementRef.nativeElement,this._enterAnimationDuration=this._animationsEnabled?St(this._config.enterAnimationDuration)??150:0,this._exitAnimationDuration=this._animationsEnabled?St(this._config.exitAnimationDuration)??75:0,this._animationTimer=null,this._finishDialogOpen=()=>{this._clearAnimationClasses(),this._openAnimationDone(this._enterAnimationDuration)},this._finishDialogClose=()=>{this._clearAnimationClasses(),this._animationStateChanged.emit({state:"closed",totalTime:this._exitAnimationDuration})}}_contentAttached(){super._contentAttached(),this._startOpenAnimation()}ngOnDestroy(){super.ngOnDestroy(),null!==this._animationTimer&&clearTimeout(this._animationTimer)}_startOpenAnimation(){this._animationStateChanged.emit({state:"opening",totalTime:this._enterAnimationDuration}),this._animationsEnabled?(this._hostElement.style.setProperty(Ot,`${this._enterAnimationDuration}ms`),this._requestAnimationFrame(()=>this._hostElement.classList.add(Mt,J)),this._waitForAnimationToComplete(this._enterAnimationDuration,this._finishDialogOpen)):(this._hostElement.classList.add(J),Promise.resolve().then(()=>this._finishDialogOpen()))}_startExitAnimation(){this._animationStateChanged.emit({state:"closing",totalTime:this._exitAnimationDuration}),this._hostElement.classList.remove(J),this._animationsEnabled?(this._hostElement.style.setProperty(Ot,`${this._exitAnimationDuration}ms`),this._requestAnimationFrame(()=>this._hostElement.classList.add(Rt)),this._waitForAnimationToComplete(this._exitAnimationDuration,this._finishDialogClose)):Promise.resolve().then(()=>this._finishDialogClose())}_clearAnimationClasses(){this._hostElement.classList.remove(Mt,Rt)}_waitForAnimationToComplete(e,i){null!==this._animationTimer&&clearTimeout(this._animationTimer),this._animationTimer=setTimeout(i,e)}_requestAnimationFrame(e){this._ngZone.runOutsideAngular(()=>{"function"==typeof requestAnimationFrame?requestAnimationFrame(e):e()})}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(t.aKT),t.rXU(k.GX),t.rXU(p.qQ,8),t.rXU(z),t.rXU(k.Z7),t.rXU(t.SKi),t.rXU(P),t.rXU(t.bc$,8),t.rXU(k.FN))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["mat-dialog-container"]],hostAttrs:["tabindex","-1",1,"mat-mdc-dialog-container","mdc-dialog"],hostVars:8,hostBindings:function(i,n){2&i&&(t.Mr5("id",n._config.id),t.BMQ("aria-modal",n._config.ariaModal)("role",n._config.role)("aria-labelledby",n._config.ariaLabel?null:n._ariaLabelledByQueue[0])("aria-label",n._config.ariaLabel)("aria-describedby",n._config.ariaDescribedBy||null),t.AVh("_mat-animation-noopable",!n._animationsEnabled))},features:[t.Vt3],decls:3,vars:0,consts:[[1,"mdc-dialog__container"],[1,"mat-mdc-dialog-surface","mdc-dialog__surface"],["cdkPortalOutlet",""]],template:function(i,n){1&i&&(t.j41(0,"div",0)(1,"div",1),t.DNE(2,ge,0,0,"ng-template",2),t.k0s()())},dependencies:[q],styles:['.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;transform:scale(0.8);opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:"";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{transform:none;opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{display:block;width:100%;height:100%}.mat-mdc-dialog-container{--mdc-dialog-container-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);--mdc-dialog-container-shadow-color:#000;--mdc-dialog-container-shape:4px;--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition-duration:var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container{transition:none}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-actions{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}'],encapsulation:2})}}return a})();class ye{constructor(o,e,i){this._ref=o,this._containerInstance=i,this._afterOpened=new E.B,this._beforeClosed=new E.B,this._state=0,this.disableClose=e.disableClose,this.id=o.id,i._animationStateChanged.pipe((0,I.p)(n=>"opened"===n.state),(0,B.s)(1)).subscribe(()=>{this._afterOpened.next(),this._afterOpened.complete()}),i._animationStateChanged.pipe((0,I.p)(n=>"closed"===n.state),(0,B.s)(1)).subscribe(()=>{clearTimeout(this._closeFallbackTimeout),this._finishDialogClose()}),o.overlayRef.detachments().subscribe(()=>{this._beforeClosed.next(this._result),this._beforeClosed.complete(),this._finishDialogClose()}),(0,at.h)(this.backdropClick(),this.keydownEvents().pipe((0,I.p)(n=>n.keyCode===$._f&&!this.disableClose&&!(0,$.rp)(n)))).subscribe(n=>{this.disableClose||(n.preventDefault(),function we(a,o,e){a._closeInteractionType=o,a.close(e)}(this,"keydown"===n.type?"keyboard":"mouse"))})}close(o){this._result=o,this._containerInstance._animationStateChanged.pipe((0,I.p)(e=>"closing"===e.state),(0,B.s)(1)).subscribe(e=>{this._beforeClosed.next(o),this._beforeClosed.complete(),this._ref.overlayRef.detachBackdrop(),this._closeFallbackTimeout=setTimeout(()=>this._finishDialogClose(),e.totalTime+100)}),this._state=1,this._containerInstance._startExitAnimation()}afterOpened(){return this._afterOpened}afterClosed(){return this._ref.closed}beforeClosed(){return this._beforeClosed}backdropClick(){return this._ref.backdropClick}keydownEvents(){return this._ref.keydownEvents}updatePosition(o){let e=this._ref.config.positionStrategy;return o&&(o.left||o.right)?o.left?e.left(o.left):e.right(o.right):e.centerHorizontally(),o&&(o.top||o.bottom)?o.top?e.top(o.top):e.bottom(o.bottom):e.centerVertically(),this._ref.updatePosition(),this}updateSize(o="",e=""){return this._ref.updateSize(o,e),this}addPanelClass(o){return this._ref.addPanelClass(o),this}removePanelClass(o){return this._ref.removePanelClass(o),this}getState(){return this._state}_finishDialogClose(){this._state=2,this._ref.close(this._result,{focusOrigin:this._closeInteractionType}),this.componentInstance=null}}const Ee=new t.nKC("MatMdcDialogData"),ke=new t.nKC("mat-mdc-dialog-default-options"),It=new t.nKC("mat-mdc-dialog-scroll-strategy"),De={provide:It,deps:[M],useFactory:function Ce(a){return()=>a.scrollStrategies.block()}};let je=0,Fe=(()=>{class a{get openDialogs(){return this._parentDialog?this._parentDialog.openDialogs:this._openDialogsAtThisLevel}get afterOpened(){return this._parentDialog?this._parentDialog.afterOpened:this._afterOpenedAtThisLevel}_getAfterAllClosed(){const e=this._parentDialog;return e?e._getAfterAllClosed():this._afterAllClosedAtThisLevel}constructor(e,i,n,r,s,l,u,m,c,h){this._overlay=e,this._defaultOptions=n,this._parentDialog=r,this._dialogRefConstructor=u,this._dialogContainerType=m,this._dialogDataToken=c,this._openDialogsAtThisLevel=[],this._afterAllClosedAtThisLevel=new E.B,this._afterOpenedAtThisLevel=new E.B,this._idPrefix="mat-dialog-",this.dialogConfigClass=z,this.afterAllClosed=(0,gt.v)(()=>this.openDialogs.length?this._getAfterAllClosed():this._getAfterAllClosed().pipe((0,_t.Z)(void 0))),this._scrollStrategy=l,this._dialog=i.get(yt)}open(e,i){let n;(i={...this._defaultOptions||new z,...i}).id=i.id||`${this._idPrefix}${je++}`,i.scrollStrategy=i.scrollStrategy||this._scrollStrategy();const r=this._dialog.open(e,{...i,positionStrategy:this._overlay.position().global().centerHorizontally().centerVertically(),disableClose:!0,closeOnDestroy:!1,closeOnOverlayDetachments:!1,container:{type:this._dialogContainerType,providers:()=>[{provide:this.dialogConfigClass,useValue:i},{provide:G,useValue:i}]},templateContext:()=>({dialogRef:n}),providers:(s,l,u)=>(n=new this._dialogRefConstructor(s,i,u),n.updatePosition(i?.position),[{provide:this._dialogContainerType,useValue:u},{provide:this._dialogDataToken,useValue:l.data},{provide:this._dialogRefConstructor,useValue:n}])});return n.componentRef=r.componentRef,n.componentInstance=r.componentInstance,this.openDialogs.push(n),this.afterOpened.next(n),n.afterClosed().subscribe(()=>{const s=this.openDialogs.indexOf(n);s>-1&&(this.openDialogs.splice(s,1),this.openDialogs.length||this._getAfterAllClosed().next())}),n}closeAll(){this._closeDialogs(this.openDialogs)}getDialogById(e){return this.openDialogs.find(i=>i.id===e)}ngOnDestroy(){this._closeDialogs(this._openDialogsAtThisLevel),this._afterAllClosedAtThisLevel.complete(),this._afterOpenedAtThisLevel.complete()}_closeDialogs(e){let i=e.length;for(;i--;)e[i].close()}static{this.\u0275fac=function(i){t.QTQ()}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac})}}return a})(),Pt=(()=>{class a extends Fe{constructor(e,i,n,r,s,l,u,m){super(e,i,r,l,u,s,ye,xe,Ee,m),this._idPrefix="mat-mdc-dialog-"}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(M),t.KVO(t.zZn),t.KVO(p.aZ,8),t.KVO(ke,8),t.KVO(It),t.KVO(a,12),t.KVO(V),t.KVO(t.bc$,8))}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac})}}return a})(),Me=(()=>{class a{static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({providers:[Pt,De],imports:[se,ft,N,R,R]})}}return a})();var Re=f(8490);const Oe=["confirmDialog"];function Se(a,o){if(1&a&&(t.j41(0,"div",44)(1,"div",45),t.qSk(),t.j41(2,"svg",46),t.nrm(3,"path",47),t.k0s(),t.joV(),t.j41(4,"span",48),t.EFF(5),t.k0s()(),t.j41(6,"a",49),t.qSk(),t.j41(7,"svg",50),t.nrm(8,"path",51),t.k0s(),t.EFF(9," T\xe9l\xe9charger "),t.k0s()()),2&a){const e=o.$implicit,i=t.XpG(4);t.R7$(5),t.JRh(i.getFileName(e)),t.R7$(1),t.Y8G("href",i.getFileUrl(e),t.B4B)("download",i.getFileName(e))}}function Ie(a,o){if(1&a&&(t.j41(0,"div",30)(1,"h4",31),t.qSk(),t.j41(2,"svg",32),t.nrm(3,"path",41),t.k0s(),t.EFF(4),t.k0s(),t.joV(),t.j41(5,"div",42),t.DNE(6,Se,10,3,"div",43),t.k0s()()),2&a){const e=t.XpG().$implicit;t.R7$(4),t.SpI(" Fichiers (",e.fichiers.length,") "),t.R7$(2),t.Y8G("ngForOf",e.fichiers)}}function Pe(a,o){1&a&&(t.j41(0,"div",30)(1,"h4",31),t.qSk(),t.j41(2,"svg",32),t.nrm(3,"path",41),t.k0s(),t.EFF(4," Fichiers "),t.k0s(),t.joV(),t.j41(5,"div",52),t.qSk(),t.j41(6,"svg",53),t.nrm(7,"path",33),t.k0s(),t.EFF(8," Aucun fichier joint "),t.k0s()())}const Ae=function(a){return["/admin/projects/editProjet",a]},Te=function(a){return["/admin/projects/details",a]},Le=function(){return["/admin/projects/rendus"]},Be=function(a){return{projetId:a}};function Ne(a,o){if(1&a){const e=t.RV6();t.j41(0,"div",13)(1,"div",14)(2,"div",15)(3,"h3",16),t.EFF(4),t.k0s(),t.j41(5,"div",17)(6,"a",18),t.qSk(),t.j41(7,"svg",19),t.nrm(8,"path",20),t.k0s()(),t.joV(),t.j41(9,"a",21),t.bIt("click",function(){const r=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(r._id&&s.openDeleteDialog(r._id))}),t.qSk(),t.j41(10,"svg",19),t.nrm(11,"path",22),t.k0s()()()(),t.joV(),t.j41(12,"div",23)(13,"span",24),t.EFF(14),t.k0s(),t.j41(15,"span",25),t.EFF(16,"\u2022"),t.k0s(),t.j41(17,"span",26),t.qSk(),t.j41(18,"svg",27),t.nrm(19,"path",28),t.k0s(),t.EFF(20),t.nI1(21,"date"),t.k0s()()(),t.joV(),t.j41(22,"div",29)(23,"div",30)(24,"h4",31),t.qSk(),t.j41(25,"svg",32),t.nrm(26,"path",33),t.k0s(),t.EFF(27," Description "),t.k0s(),t.joV(),t.j41(28,"p",34),t.EFF(29),t.k0s()(),t.DNE(30,Ie,7,2,"div",35),t.DNE(31,Pe,9,0,"div",35),t.j41(32,"div",36)(33,"a",37),t.qSk(),t.j41(34,"svg",38),t.nrm(35,"path",33),t.k0s(),t.EFF(36," D\xe9tails "),t.k0s(),t.joV(),t.j41(37,"a",39),t.qSk(),t.j41(38,"svg",38),t.nrm(39,"path",40),t.k0s(),t.EFF(40," Rendus "),t.k0s()()()()}if(2&a){const e=o.$implicit;t.R7$(4),t.SpI(" ",e.titre," "),t.R7$(2),t.Y8G("routerLink",t.eq3(13,Ae,e._id)),t.R7$(8),t.JRh(e.groupe||"Tous"),t.R7$(6),t.SpI(" ",t.i5U(21,10,e.dateLimite,"dd/MM/yyyy")," "),t.R7$(9),t.SpI(" ",e.description||"Aucune description fournie"," "),t.R7$(1),t.Y8G("ngIf",e.fichiers&&e.fichiers.length>0),t.R7$(1),t.Y8G("ngIf",!e.fichiers||0===e.fichiers.length),t.R7$(2),t.Y8G("routerLink",t.eq3(15,Te,e._id)),t.R7$(4),t.Y8G("routerLink",t.lJ4(17,Le))("queryParams",t.eq3(18,Be,e._id))}}function Ve(a,o){if(1&a&&(t.qSk(),t.joV(),t.j41(0,"div",11),t.DNE(1,Ne,41,20,"div",12),t.k0s()),2&a){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.projets)}}function $e(a,o){1&a&&(t.qSk(),t.joV(),t.j41(0,"div",54),t.nrm(1,"div",55),t.j41(2,"p",56),t.EFF(3,"Chargement des projets..."),t.k0s()())}function Ge(a,o){1&a&&(t.qSk(),t.joV(),t.j41(0,"div",57)(1,"div",58),t.qSk(),t.j41(2,"svg",59),t.nrm(3,"path",60),t.k0s()(),t.joV(),t.j41(4,"h3",61),t.EFF(5," Aucun projet disponible "),t.k0s(),t.j41(6,"p",62),t.EFF(7," Commencez par cr\xe9er votre premier projet en cliquant sur le bouton ci-dessous "),t.k0s(),t.j41(8,"a",63),t.qSk(),t.j41(9,"svg",64),t.nrm(10,"path",65),t.k0s(),t.EFF(11," Ajouter un projet "),t.k0s()())}function ze(a,o){if(1&a){const e=t.RV6();t.qSk(),t.joV(),t.j41(0,"div",66)(1,"div",67)(2,"h2",68),t.EFF(3),t.k0s(),t.j41(4,"p",69),t.EFF(5),t.k0s(),t.j41(6,"div",70)(7,"button",71),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.onDeleteCancel())}),t.EFF(8," Annuler "),t.k0s(),t.j41(9,"button",72),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.onDeleteConfirm())}),t.EFF(10," Supprimer "),t.k0s()()()()}if(2&a){const e=t.XpG();t.R7$(3),t.SpI(" ",e.dialogData.title," "),t.R7$(2),t.JRh(e.dialogData.message)}}let Ye=(()=>{class a{constructor(e,i,n,r){this.projetService=e,this.router=i,this.dialog=n,this.authService=r,this.projets=[],this.isLoading=!0,this.isAdmin=!1,this.showDeleteDialog=!1,this.projectIdToDelete=null,this.dialogData={title:"Confirmer la suppression",message:"\xcates-vous s\xfbr de vouloir supprimer ce projet?"}}ngOnInit(){this.loadProjets(),this.checkAdminStatus()}loadProjets(){this.isLoading=!0,this.projetService.getProjets().subscribe({next:e=>{this.projets=e,this.isLoading=!1},error:e=>{this.isLoading=!1,alert("Erreur lors du chargement des projets: "+(e.error?.message||e.message||"Erreur inconnue"))}})}loadProjects(){this.loadProjets()}checkAdminStatus(){this.isAdmin=this.authService.isAdmin()}editProjet(e){e&&this.router.navigate(["/admin/projects/edit",e])}viewProjetDetails(e){e&&this.router.navigate(["/admin/projects/detail",e])}deleteProjet(e){e&&confirm("\xcates-vous s\xfbr de vouloir supprimer ce projet ?")&&this.projetService.deleteProjet(e).subscribe({next:()=>{alert("Projet supprim\xe9 avec succ\xe8s"),this.loadProjets()},error:i=>{alert("Erreur lors de la suppression du projet: "+(i.error?.message||i.message||"Erreur inconnue"))}})}openDeleteDialog(e){e&&(this.showDeleteDialog=!0,this.projectIdToDelete=e)}onDeleteConfirm(){this.projectIdToDelete&&this.projetService.deleteProjet(this.projectIdToDelete).subscribe({next:()=>{alert("Projet supprim\xe9 avec succ\xe8s"),this.loadProjets(),this.showDeleteDialog=!1},error:e=>{alert("Erreur lors de la suppression du projet: "+(e.error?.message||e.message||"Erreur inconnue")),this.showDeleteDialog=!1}})}onDeleteCancel(){this.showDeleteDialog=!1}getFileUrl(e){if(!e)return"";let i=e;if(e.includes("/")||e.includes("\\")){const n=e.split(/[\/\\]/);i=n[n.length-1]}return`${D.c.urlBackend}projets/telecharger/${i}`}getFileName(e){if(!e)return"Fichier";if(e.includes("/")||e.includes("\\")){const i=e.split(/[\/\\]/);return i[i.length-1]}return e}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(O.e),t.rXU(b.Ix),t.rXU(Pt),t.rXU(Re.u))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-list-project"]],viewQuery:function(i,n){if(1&i&&t.GBs(Oe,5),2&i){let r;t.mGM(r=t.lsd())&&(n.confirmDialog=r.first)}},decls:15,vars:4,consts:[[1,"container-fluid","p-4","md:p-6","bg-[#edf1f4]","min-h-screen"],[1,"flex","flex-col","md:flex-row","md:items-center","md:justify-between","mb-6"],[1,"text-2xl","font-bold","text-[#4f5fad]","mb-2","md:mb-0"],[1,"text-[#6d6870]","text-sm"],["routerLink","/admin/projects/new",1,"inline-flex","items-center","bg-[#7826b5]","hover:bg-[#5f1d8f]","text-white","px-4","py-2","rounded-lg","shadow","transition-all","mt-4","md:mt-0"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 20 20","fill","currentColor",1,"h-4","w-4","mr-2"],["fill-rule","evenodd","d","M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z","clip-rule","evenodd"],["class","grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",4,"ngIf"],["class","text-center py-16",4,"ngIf"],["class","bg-white rounded-xl shadow-md p-8 text-center mb-8",4,"ngIf"],["class","fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",4,"ngIf"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-3","gap-6","mb-8"],["class","bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 group",4,"ngFor","ngForOf"],[1,"bg-white","rounded-xl","shadow-md","overflow-hidden","hover:shadow-xl","transition-all","duration-300","transform","hover:-translate-y-1","group"],[1,"border-t-4","border-[#4f5fad]","p-5","relative"],[1,"flex","justify-between","items-start"],[1,"text-xl","font-bold","text-[#4f5fad]","truncate","pr-16"],[1,"absolute","top-4","right-4","flex","space-x-2","opacity-0","group-hover:opacity-100","transition-opacity"],[1,"bg-[#edf1f4]","hover:bg-[#dce4ec]","p-1.5","rounded-lg","text-[#4f5fad]","transition-colors",3,"routerLink"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 20 20","fill","currentColor",1,"h-5","w-5"],["d","M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"],[1,"bg-[#edf1f4]","hover:bg-[#dce4ec]","p-1.5","rounded-lg","text-[#ff6b69]","transition-colors","cursor-pointer",3,"click"],["fill-rule","evenodd","d","M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 112 0v6a1 1 0 11-2 0V8z","clip-rule","evenodd"],[1,"flex","items-center","mt-3","text-sm"],[1,"bg-[#4f5fad]/10","px-2.5","py-1","rounded-full","text-xs","font-medium","text-[#4f5fad]"],[1,"mx-2","text-[#bdc6cc]"],[1,"text-[#6d6870]","text-xs"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-3.5","w-3.5","inline","mr-1"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"],[1,"p-5","border-t","border-[#edf1f4]"],[1,"mb-5"],[1,"text-xs","font-semibold","text-[#6d6870]","uppercase","tracking-wider","mb-2","flex","items-center"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-1","text-[#4f5fad]"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-[#6d6870]","text-sm","line-clamp-3"],["class","mb-5",4,"ngIf"],[1,"mt-6","flex","space-x-3"],[1,"flex-1","bg-[#edf1f4]","hover:bg-[#dce4ec]","text-[#4f5fad]","py-2","px-4","rounded-lg","text-sm","font-medium","text-center","flex","items-center","justify-center","transition-colors",3,"routerLink"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-1.5"],[1,"flex-1","bg-[#7826b5]","hover:bg-[#5f1d8f]","text-white","py-2","px-4","rounded-lg","text-sm","font-medium","text-center","flex","items-center","justify-center","transition-colors",3,"routerLink","queryParams"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"space-y-2","bg-[#edf1f4]","rounded-lg","p-3"],["class","flex items-center justify-between text-sm",4,"ngFor","ngForOf"],[1,"flex","items-center","justify-between","text-sm"],[1,"flex","items-center","truncate","max-w-[70%]"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","text-[#7826b5]","mr-2","flex-shrink-0"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"],[1,"truncate","text-[#6d6870]"],[1,"text-[#4f5fad]","hover:text-[#7826b5]","flex","items-center",3,"href","download"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-1"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"],[1,"bg-[#edf1f4]","rounded-lg","p-3","text-sm","text-[#6d6870]","flex","items-center","justify-center"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4","mr-1","text-[#bdc6cc]"],[1,"text-center","py-16"],[1,"animate-spin","rounded-full","h-10","w-10","border-t-2","border-b-2","border-[#4f5fad]","mx-auto"],[1,"mt-4","text-[#6d6870]","font-medium"],[1,"bg-white","rounded-xl","shadow-md","p-8","text-center","mb-8"],[1,"w-20","h-20","mx-auto","mb-6","bg-[#f0e6ff]","rounded-full","flex","items-center","justify-center"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-10","w-10","text-[#7826b5]"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","1.5","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-xl","font-bold","text-[#4f5fad]","mb-2"],[1,"text-[#6d6870]","mb-6","max-w-md","mx-auto"],["routerLink","/admin/projects/new",1,"inline-flex","items-center","px-4","py-2","bg-[#7826b5]","hover:bg-[#5f1d8f]","text-white","rounded-lg","shadow","transition-all"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 20 20","fill","currentColor",1,"h-5","w-5","mr-2"],["d","M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"],[1,"fixed","inset-0","z-50","flex","items-center","justify-center","bg-black","bg-opacity-50"],[1,"bg-white","rounded-xl","shadow-lg","w-full","max-w-md","p-6"],[1,"text-xl","font-bold","text-[#4f5fad]","mb-4"],[1,"text-[#6d6870]","mb-6"],[1,"flex","justify-end","space-x-4"],[1,"px-4","py-2","bg-[#edf1f4]","text-[#4f5fad]","hover:bg-[#dce4ec]","rounded-lg",3,"click"],[1,"px-4","py-2","bg-[#7826b5]","hover:bg-[#5f1d8f]","text-white","rounded-lg",3,"click"]],template:function(i,n){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div")(3,"h1",2),t.EFF(4," Gestion des Projets "),t.k0s(),t.j41(5,"p",3),t.EFF(6," Cr\xe9ez, g\xe9rez et suivez vos projets acad\xe9miques "),t.k0s()(),t.j41(7,"a",4),t.qSk(),t.j41(8,"svg",5),t.nrm(9,"path",6),t.k0s(),t.EFF(10," Ajouter un projet "),t.k0s()(),t.DNE(11,Ve,2,1,"div",7),t.DNE(12,$e,4,0,"div",8),t.DNE(13,Ge,12,0,"div",9),t.DNE(14,ze,11,2,"div",10),t.k0s()),2&i&&(t.R7$(11),t.Y8G("ngIf",!n.isLoading&&n.projets&&n.projets.length>0),t.R7$(1),t.Y8G("ngIf",n.isLoading),t.R7$(1),t.Y8G("ngIf",!(n.isLoading||n.projets&&0!==n.projets.length)),t.R7$(1),t.Y8G("ngIf",n.showDeleteDialog))},dependencies:[p.Sq,p.bT,b.Wk,p.vh]})}}return a})();var Xe=f(9271);function He(a,o){1&a&&(t.j41(0,"div",22),t.EFF(1," Titre est requis "),t.k0s())}function Ue(a,o){1&a&&(t.j41(0,"div",22),t.EFF(1," Description est requise "),t.k0s())}function qe(a,o){1&a&&(t.j41(0,"div",22),t.EFF(1," Date limite est requise "),t.k0s())}function Ze(a,o){1&a&&(t.j41(0,"div",22),t.EFF(1," Groupe est requis "),t.k0s())}const Ke=function(a){return{"opacity-50 cursor-not-allowed":a}};let We=(()=>{class a{constructor(e,i,n,r){this.fb=e,this.projetService=i,this.router=n,this.authService=r,this.selectedFiles=[],this.isSubmitting=!1,this.projetForm=this.fb.group({titre:["",d.k0.required],description:[""],dateLimite:["",d.k0.required],fichiers:[null],groupe:["",d.k0.required]})}onFileChange(e){const i=e.target;i.files&&(this.selectedFiles=Array.from(i.files))}onSubmit(){if(this.projetForm.invalid)return;this.isSubmitting=!0,console.log("Soumission du formulaire de projet");const e=new FormData;e.append("titre",this.projetForm.value.titre),e.append("description",this.projetForm.value.description||""),e.append("dateLimite",this.projetForm.value.dateLimite),e.append("groupe",this.projetForm.value.groupe);const i=this.authService.getCurrentUserId(),n=this.authService.getCurrentUser(),r=localStorage.getItem("user");let s=i;!s&&n&&(s=n._id||n.id),!s&&r&&(s=JSON.parse(r).id),s?(e.append("professeur",s),this.selectedFiles.forEach(l=>{e.append("fichiers",l)}),console.log("Donn\xe9es du formulaire:",{titre:this.projetForm.value.titre,description:this.projetForm.value.description,dateLimite:this.projetForm.value.dateLimite,groupe:this.projetForm.value.groupe,fichiers:this.selectedFiles.map(l=>l.name)}),this.projetService.addProjet(e).subscribe({next:()=>{console.log("Projet ajout\xe9 avec succ\xe8s"),alert("Projet ajout\xe9 avec succ\xe8s"),this.router.navigate(["/admin/projects"])},error:l=>{console.error("Erreur lors de l'ajout du projet:",l),alert("Erreur lors de l'ajout du projet: "+(l.error?.message||l.message||"Erreur inconnue")),this.isSubmitting=!1},complete:()=>{this.isSubmitting=!1}})):alert("Erreur: Impossible de r\xe9cup\xe9rer l'ID utilisateur. Veuillez vous reconnecter.")}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(d.ok),t.rXU(O.e),t.rXU(b.Ix),t.rXU(Xe.V))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-add-project"]],decls:42,vars:9,consts:[[1,"container-fluid","p-4","md:p-6","bg-[#edf1f4]","min-h-screen"],[1,"max-w-2xl","mx-auto","bg-white","rounded-xl","shadow-md","overflow-hidden"],[1,"border-t-4","border-[#4f5fad]","p-6"],[1,"text-2xl","font-bold","text-center","text-[#4f5fad]"],[1,"p-6","md:p-8"],["enctype","multipart/form-data",1,"space-y-6",3,"formGroup","ngSubmit"],["for","titre",1,"block","text-sm","font-medium","text-[#6d6870]","mb-1"],["type","text","id","titre","formControlName","titre","placeholder","Titre du projet",1,"w-full","px-4","py-3","rounded-lg","border","border-[#bdc6cc]","focus:border-[#7826b5]","focus:ring-2","focus:ring-[#dac4ea]","transition-all"],["class","text-[#ff6b69] text-sm mt-1",4,"ngIf"],["for","description",1,"block","text-sm","font-medium","text-[#6d6870]","mb-1"],["id","description","formControlName","description","placeholder","Description du projet","rows","4",1,"w-full","px-4","py-3","rounded-lg","border","border-[#bdc6cc]","focus:border-[#7826b5]","focus:ring-2","focus:ring-[#dac4ea]","transition-all"],["for","dateLimite",1,"block","text-sm","font-medium","text-[#6d6870]","mb-1"],["type","date","id","dateLimite","formControlName","dateLimite",1,"w-full","px-4","py-3","rounded-lg","border","border-[#bdc6cc]","focus:border-[#7826b5]","focus:ring-2","focus:ring-[#dac4ea]","transition-all"],["for","fichiers",1,"block","text-sm","font-medium","text-[#6d6870]","mb-1"],["type","file","id","fichiers","multiple","",1,"w-full","px-4","py-2","rounded-lg","border","border-[#bdc6cc]","bg-white","focus:outline-none",3,"change"],["for","groupe",1,"block","text-sm","font-medium","text-[#6d6870]","mb-1"],["id","groupe","formControlName","groupe",1,"w-full","px-4","py-3","rounded-lg","border","border-[#bdc6cc]","focus:border-[#7826b5]","focus:ring-2","focus:ring-[#dac4ea]","bg-white","transition-all"],["value",""],["value","2cinfo1"],["value","2cinfo2"],["value","2cinfo3"],["type","submit",1,"w-full","bg-[#7826b5]","hover:bg-[#4f5fad]","text-white","font-bold","py-3","px-4","rounded-lg","transition-all","focus:outline-none","focus:ring-2","focus:ring-[#dac4ea]","focus:ring-offset-2",3,"disabled","ngClass"],[1,"text-[#ff6b69]","text-sm","mt-1"]],template:function(i,n){if(1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2",3),t.EFF(4," Ajouter un projet "),t.k0s()(),t.j41(5,"div",4)(6,"form",5),t.bIt("ngSubmit",function(){return n.onSubmit()}),t.j41(7,"div")(8,"label",6),t.EFF(9,"Titre"),t.k0s(),t.nrm(10,"input",7),t.DNE(11,He,2,0,"div",8),t.k0s(),t.j41(12,"div")(13,"label",9),t.EFF(14,"Description"),t.k0s(),t.nrm(15,"textarea",10),t.DNE(16,Ue,2,0,"div",8),t.k0s(),t.j41(17,"div")(18,"label",11),t.EFF(19,"Date limite"),t.k0s(),t.nrm(20,"input",12),t.DNE(21,qe,2,0,"div",8),t.k0s(),t.j41(22,"div")(23,"label",13),t.EFF(24,"Fichiers"),t.k0s(),t.j41(25,"input",14),t.bIt("change",function(s){return n.onFileChange(s)}),t.k0s()(),t.j41(26,"div")(27,"label",15),t.EFF(28,"Groupe"),t.k0s(),t.j41(29,"select",16)(30,"option",17),t.EFF(31,"-- Choisir un groupe --"),t.k0s(),t.j41(32,"option",18),t.EFF(33,"2cinfo1"),t.k0s(),t.j41(34,"option",19),t.EFF(35,"2cinfo2"),t.k0s(),t.j41(36,"option",20),t.EFF(37,"2cinfo3"),t.k0s()(),t.DNE(38,Ze,2,0,"div",8),t.k0s(),t.j41(39,"div")(40,"button",21),t.EFF(41," Ajouter "),t.k0s()()()()()()),2&i){let r,s,l,u;t.R7$(6),t.Y8G("formGroup",n.projetForm),t.R7$(5),t.Y8G("ngIf",(null==(r=n.projetForm.get("titre"))?null:r.invalid)&&(null==(r=n.projetForm.get("titre"))?null:r.touched)),t.R7$(5),t.Y8G("ngIf",(null==(s=n.projetForm.get("description"))?null:s.invalid)&&(null==(s=n.projetForm.get("description"))?null:s.touched)),t.R7$(5),t.Y8G("ngIf",(null==(l=n.projetForm.get("dateLimite"))?null:l.invalid)&&(null==(l=n.projetForm.get("dateLimite"))?null:l.touched)),t.R7$(17),t.Y8G("ngIf",(null==(u=n.projetForm.get("groupe"))?null:u.invalid)&&(null==(u=n.projetForm.get("groupe"))?null:u.touched)),t.R7$(2),t.Y8G("disabled",n.projetForm.invalid)("ngClass",t.eq3(7,Ke,n.projetForm.invalid))}},dependencies:[p.YU,p.bT,d.qT,d.xH,d.y7,d.me,d.wz,d.BC,d.cb,d.j4,d.JD]})}}return a})(),Qe=(()=>{class a{constructor(e,i,n,r){this.route=e,this.fb=i,this.projetService=n,this.router=r}ngOnInit(){this.projectId=this.route.snapshot.paramMap.get("id")||"",this.updateForm=this.fb.group({titre:["",d.k0.required],description:[""],groupe:[""],dateLimite:["",d.k0.required]}),this.projetService.getProjetById(this.projectId).subscribe(e=>{this.updateForm.patchValue({titre:e.titre,description:e.description,groupe:e.groupe,dateLimite:e.dateLimite})})}onSubmit(){this.updateForm.valid&&this.projetService.updateProjet(this.projectId,this.updateForm.value).subscribe(()=>{alert("Projet mis \xe0 jour avec succ\xe8s"),this.router.navigate(["/projects"])})}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(b.nX),t.rXU(d.ok),t.rXU(O.e),t.rXU(b.Ix))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-update-project"]],decls:22,vars:2,consts:[[1,"max-w-xl","mx-auto","mt-10","p-6","bg-white","shadow-md","rounded-xl"],[1,"text-2xl","font-bold","text-[#4f5fad]","mb-4"],[3,"formGroup","ngSubmit"],[1,"mb-4"],[1,"block","text-sm","font-medium","text-gray-700"],["formControlName","titre",1,"mt-1","block","w-full","border","border-gray-300","rounded-md","p-2"],["formControlName","groupe",1,"mt-1","block","w-full","border","border-gray-300","rounded-md","p-2"],["type","date","formControlName","dateLimite",1,"mt-1","block","w-full","border","border-gray-300","rounded-md","p-2"],["formControlName","description",1,"mt-1","block","w-full","border","border-gray-300","rounded-md","p-2"],["type","submit",1,"bg-[#7826b5]","text-white","px-4","py-2","rounded","hover:bg-[#5f1d8f]",3,"disabled"]],template:function(i,n){1&i&&(t.j41(0,"div",0)(1,"h2",1),t.EFF(2,"Modifier le projet"),t.k0s(),t.j41(3,"form",2),t.bIt("ngSubmit",function(){return n.onSubmit()}),t.j41(4,"div",3)(5,"label",4),t.EFF(6,"Titre"),t.k0s(),t.nrm(7,"input",5),t.k0s(),t.j41(8,"div",3)(9,"label",4),t.EFF(10,"Groupe"),t.k0s(),t.nrm(11,"input",6),t.k0s(),t.j41(12,"div",3)(13,"label",4),t.EFF(14,"Date limite"),t.k0s(),t.nrm(15,"input",7),t.k0s(),t.j41(16,"div",3)(17,"label",4),t.EFF(18,"Description"),t.k0s(),t.nrm(19,"textarea",8),t.k0s(),t.j41(20,"button",9),t.EFF(21," Mettre \xe0 jour "),t.k0s()()()),2&i&&(t.R7$(3),t.Y8G("formGroup",n.updateForm),t.R7$(17),t.Y8G("disabled",!n.updateForm.valid))},dependencies:[d.qT,d.me,d.BC,d.cb,d.j4,d.JD]})}}return a})();var Je=f(4704);function ti(a,o){if(1&a&&(t.j41(0,"div",41)(1,"a",42),t.qSk(),t.j41(2,"svg",43),t.nrm(3,"path",44),t.k0s(),t.joV(),t.j41(4,"span"),t.EFF(5,"T\xe9l\xe9charger"),t.k0s()()()),2&a){const e=o.$implicit,i=t.XpG(2);t.R7$(1),t.Y8G("href",i.getFileUrl(e),t.B4B)}}function ei(a,o){if(1&a&&(t.j41(0,"div")(1,"h4",35),t.EFF(2,"Fichiers joints"),t.k0s(),t.j41(3,"div",39),t.DNE(4,ti,6,1,"div",40),t.k0s()()),2&a){const e=t.XpG();t.R7$(4),t.Y8G("ngForOf",e.projet.fichiers)}}function ii(a,o){1&a&&(t.j41(0,"div",45),t.EFF(1," Aucun fichier joint "),t.k0s())}function oi(a,o){if(1&a&&(t.j41(0,"div",46)(1,"div",47),t.EFF(2),t.k0s(),t.j41(3,"div",48)(4,"div",49),t.EFF(5),t.k0s(),t.j41(6,"div",32),t.EFF(7),t.k0s()()()),2&a){const e=o.$implicit,i=t.XpG();t.R7$(2),t.Lme(" ",(null==e.nom?null:e.nom.charAt(0))||"","",(null==e.prenom?null:e.prenom.charAt(0))||""," "),t.R7$(3),t.Lme("",e.prenom," ",e.nom,""),t.R7$(2),t.JRh(i.formatDate(e.dateRendu))}}function ni(a,o){1&a&&(t.j41(0,"div",50),t.EFF(1," Aucun rendu pour le moment "),t.k0s())}const ai=function(a){return["/admin/projects/editProjet",a]},ri=function(){return["/admin/projects/rendus"]},si=function(a){return{projetId:a}},li=function(){return[]};let di=(()=>{class a{constructor(e,i,n,r){this.route=e,this.router=i,this.projectService=n,this.fileService=r,this.projet=null}ngOnInit(){const e=this.route.snapshot.paramMap.get("id");e&&this.projectService.getProjetById(e).subscribe(i=>{this.projet=i})}getFileUrl(e){return this.fileService.getDownloadUrl(e)}deleteProjet(e){e&&confirm("\xcates-vous s\xfbr de vouloir supprimer ce projet ?")&&this.projectService.deleteProjet(e).subscribe({next:()=>{alert("Projet supprim\xe9 avec succ\xe8s"),this.router.navigate(["/admin/projects"])},error:i=>{console.error("Erreur lors de la suppression du projet",i),alert("Erreur lors de la suppression du projet")}})}formatDate(e){const i=new Date(e);return`${i.getDate().toString().padStart(2,"0")}/${(i.getMonth()+1).toString().padStart(2,"0")}/${i.getFullYear()}`}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(b.nX),t.rXU(b.Ix),t.rXU(O.e),t.rXU(Je.E))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-detail-project"]],decls:63,vars:24,consts:[[1,"max-w-6xl","mx-auto","my-8","space-y-6"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-6"],[1,"md:col-span-2","bg-white","rounded-2xl","shadow-md","hover:shadow-xl","transition-shadow","duration-300","overflow-hidden","border","border-[#e4e7ec]"],[1,"bg-gradient-to-r","from-[#6C63FF]","to-[#C77DFF]","p-6","text-white","rounded-t-2xl"],[1,"flex","justify-between","items-start"],[1,"text-2xl","font-bold"],[1,"mt-1","text-sm","text-[#e4dbf8]"],[1,"px-3","py-1","bg-white/20","rounded-full","text-xs","font-semibold"],[1,"p-6","space-y-6"],[1,"text-sm","font-semibold","text-gray-600","mb-2"],[1,"text-gray-700"],[4,"ngIf"],["class","text-sm text-gray-500 italic",4,"ngIf"],[1,"flex","flex-wrap","gap-3","pt-4","border-t","border-gray-100"],[1,"flex-1","bg-gradient-to-r","from-[#3CAEA3]","to-[#20BF55]","hover:from-[#2d9b91]","hover:to-[#18a046]","text-white","py-2.5","px-4","rounded-lg","font-medium","text-sm","text-center","shadow-sm","hover:shadow-md","transition-all","flex","items-center","justify-center",3,"routerLink"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","mr-2"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"],[1,"flex-1","bg-gradient-to-r","from-[#F5576C]","to-[#F093FB]","hover:from-[#e04054]","hover:to-[#d87fe0]","text-white","py-2.5","px-4","rounded-lg","font-medium","text-sm","text-center","shadow-sm","hover:shadow-md","transition-all","flex","items-center","justify-center",3,"click"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"],[1,"flex-1","bg-gradient-to-r","from-[#6C63FF]","to-[#8E2DE2]","hover:from-[#5046e5]","hover:to-[#7816c7]","text-white","py-2.5","px-4","rounded-lg","font-medium","text-sm","text-center","shadow-sm","hover:shadow-md","transition-all","flex","items-center","justify-center",3,"routerLink","queryParams"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"],[1,"bg-white","rounded-2xl","shadow-md","border","border-[#e4e7ec]","overflow-hidden"],[1,"bg-gradient-to-r","from-[#6C63FF]","to-[#C77DFF]","p-4","text-white"],[1,"font-semibold"],[1,"flex","justify-between","mb-2"],[1,"text-sm","font-medium","text-gray-700"],[1,"text-sm","font-medium","text-[#6C63FF]"],[1,"w-full","bg-gray-200","rounded-full","h-2.5"],[1,"bg-gradient-to-r","from-[#6C63FF]","to-[#C77DFF]","h-2.5","rounded-full"],[1,"grid","grid-cols-2","gap-4"],[1,"bg-[#f0f7ff]","p-4","rounded-lg","border","border-[#e4e7ec]"],[1,"text-2xl","font-bold","text-[#4A00E0]"],[1,"text-xs","text-gray-500"],[1,"bg-[#fff5f5]","p-4","rounded-lg","border","border-[#e4e7ec]"],[1,"text-2xl","font-bold","text-[#E02D6D]"],[1,"text-sm","font-semibold","text-gray-600","mb-3"],[1,"space-y-3"],["class","flex items-center space-x-3",4,"ngFor","ngForOf"],["class","text-sm text-gray-500 italic text-center py-2",4,"ngIf"],[1,"grid","grid-cols-1","sm:grid-cols-2","gap-3"],["class","flex items-center",4,"ngFor","ngForOf"],[1,"flex","items-center"],["download","",1,"flex-1","text-center","text-sm","text-white","bg-gradient-to-r","from-[#8E2DE2]","to-[#4A00E0]","hover:from-[#7c22d2]","hover:to-[#3f00cc]","rounded-lg","py-2","px-4","transition-all","hover:scale-[1.02]","flex","items-center","justify-center","space-x-2",3,"href"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-4","w-4"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"],[1,"text-sm","text-gray-500","italic"],[1,"flex","items-center","space-x-3"],[1,"h-8","w-8","rounded-full","bg-[#6C63FF]","flex","items-center","justify-center","text-white","text-xs","font-bold"],[1,"text-sm"],[1,"font-medium"],[1,"text-sm","text-gray-500","italic","text-center","py-2"]],template:function(i,n){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div")(6,"h3",5),t.EFF(7),t.k0s(),t.j41(8,"p",6),t.EFF(9),t.k0s()(),t.j41(10,"span",7),t.EFF(11),t.k0s()()(),t.j41(12,"div",8)(13,"div")(14,"h4",9),t.EFF(15,"Description"),t.k0s(),t.j41(16,"p",10),t.EFF(17),t.k0s()(),t.DNE(18,ei,5,1,"div",11),t.DNE(19,ii,2,0,"div",12),t.j41(20,"div",13)(21,"a",14),t.qSk(),t.j41(22,"svg",15),t.nrm(23,"path",16),t.k0s(),t.EFF(24," Modifier "),t.k0s(),t.joV(),t.j41(25,"button",17),t.bIt("click",function(){return n.deleteProjet(null==n.projet?null:n.projet._id)}),t.qSk(),t.j41(26,"svg",15),t.nrm(27,"path",18),t.k0s(),t.EFF(28," Supprimer "),t.k0s(),t.joV(),t.j41(29,"a",19),t.qSk(),t.j41(30,"svg",15),t.nrm(31,"path",20),t.k0s(),t.EFF(32," Voir les rendus "),t.k0s()()()(),t.joV(),t.j41(33,"div",21)(34,"div",22)(35,"h3",23),t.EFF(36,"Statistiques de rendu"),t.k0s()(),t.j41(37,"div",8)(38,"div")(39,"div",24)(40,"span",25),t.EFF(41,"Progression"),t.k0s(),t.j41(42,"span",26),t.EFF(43),t.k0s()(),t.j41(44,"div",27),t.nrm(45,"div",28),t.k0s()(),t.j41(46,"div",29)(47,"div",30)(48,"div",31),t.EFF(49),t.k0s(),t.j41(50,"div",32),t.EFF(51,"Rendus"),t.k0s()(),t.j41(52,"div",33)(53,"div",34),t.EFF(54),t.k0s(),t.j41(55,"div",32),t.EFF(56,"En attente"),t.k0s()()(),t.j41(57,"div")(58,"h4",35),t.EFF(59,"Derniers rendus"),t.k0s(),t.j41(60,"div",36),t.DNE(61,oi,8,5,"div",37),t.DNE(62,ni,2,0,"div",38),t.k0s()()()()()()),2&i&&(t.R7$(7),t.JRh(null==n.projet?null:n.projet.titre),t.R7$(2),t.Lme(" Groupe: ",null==n.projet?null:n.projet.groupe," \u2022 Date limite: ",null!=n.projet&&n.projet.dateLimite?n.formatDate(null==n.projet?null:n.projet.dateLimite):""," "),t.R7$(2),t.SpI(" ",(null==n.projet?null:n.projet.statut)||"En cours"," "),t.R7$(6),t.JRh((null==n.projet?null:n.projet.description)||"Aucune description fournie"),t.R7$(1),t.Y8G("ngIf",(null==n.projet||null==n.projet.fichiers?null:n.projet.fichiers.length)>0),t.R7$(1),t.Y8G("ngIf",!(null!=n.projet&&n.projet.fichiers)||0===n.projet.fichiers.length),t.R7$(2),t.Y8G("routerLink",t.eq3(18,ai,null==n.projet?null:n.projet._id)),t.R7$(8),t.Y8G("routerLink",t.lJ4(20,ri))("queryParams",t.eq3(21,si,null==n.projet?null:n.projet._id)),t.R7$(14),t.Lme("",(null==n.projet||null==n.projet.etudiantsRendus?null:n.projet.etudiantsRendus.length)||0,"/",(null==n.projet?null:n.projet.totalEtudiants)||0,""),t.R7$(2),t.xc7("width",((null==n.projet||null==n.projet.etudiantsRendus?null:n.projet.etudiantsRendus.length)||0)/((null==n.projet?null:n.projet.totalEtudiants)||1)*100,"%"),t.R7$(4),t.JRh((null==n.projet||null==n.projet.etudiantsRendus?null:n.projet.etudiantsRendus.length)||0),t.R7$(5),t.JRh(((null==n.projet?null:n.projet.totalEtudiants)||0)-((null==n.projet||null==n.projet.etudiantsRendus?null:n.projet.etudiantsRendus.length)||0)),t.R7$(7),t.Y8G("ngForOf",(null==n.projet?null:n.projet.derniersRendus)||t.lJ4(23,li)),t.R7$(1),t.Y8G("ngIf",!(null!=n.projet&&n.projet.derniersRendus)||0===n.projet.derniersRendus.length))},dependencies:[p.Sq,p.bT,b.Wk]})}}return a})();var T=f(7169);function ci(a,o){if(1&a&&(t.j41(0,"option",13),t.EFF(1),t.k0s()),2&a){const e=o.$implicit;t.Y8G("value",e),t.R7$(1),t.JRh(e)}}function ui(a,o){if(1&a&&(t.j41(0,"option",13),t.EFF(1),t.k0s()),2&a){const e=o.$implicit;t.Y8G("value",e._id),t.R7$(1),t.JRh(e.titre)}}function mi(a,o){1&a&&(t.j41(0,"div",14),t.nrm(1,"div",15),t.k0s())}function pi(a,o){if(1&a&&(t.j41(0,"div",16),t.EFF(1),t.k0s()),2&a){const e=t.XpG();t.R7$(1),t.SpI(" ",e.error," ")}}const hi=function(a){return["/admin/projects/evaluation-details",a]};function fi(a,o){if(1&a&&(t.j41(0,"a",37),t.EFF(1," Voir l'\xe9valuation "),t.k0s()),2&a){const e=t.XpG().$implicit;t.Y8G("routerLink",t.eq3(1,hi,e._id))}}function gi(a,o){if(1&a){const e=t.RV6();t.j41(0,"div",33)(1,"button",38),t.bIt("click",function(){t.eBV(e);const n=t.XpG().$implicit,r=t.XpG(2);return t.Njj(r.evaluerRendu(n._id,"manual"))}),t.EFF(2," \xc9valuer manuellement "),t.k0s(),t.j41(3,"button",39),t.bIt("click",function(){t.eBV(e);const n=t.XpG().$implicit,r=t.XpG(2);return t.Njj(r.evaluerRendu(n._id,"ai"))}),t.EFF(4," \xc9valuer par IA "),t.k0s()()}}function bi(a,o){if(1&a){const e=t.RV6();t.j41(0,"button",40),t.bIt("click",function(){t.eBV(e);const n=t.XpG().$implicit,r=t.XpG(2);return t.Njj(r.navigateToEditEvaluation(n._id))}),t.EFF(1," Modifier l'\xe9valuation "),t.k0s()}}function _i(a,o){if(1&a&&(t.j41(0,"tr")(1,"td",24)(2,"div",25)(3,"div",26),t.EFF(4),t.k0s(),t.j41(5,"div",27)(6,"div",28),t.EFF(7),t.k0s(),t.j41(8,"div",29),t.EFF(9),t.k0s()()()(),t.j41(10,"td",24)(11,"div",30),t.EFF(12),t.k0s()(),t.j41(13,"td",24)(14,"div",30),t.EFF(15),t.k0s()(),t.j41(16,"td",24)(17,"div",30),t.EFF(18),t.k0s()(),t.j41(19,"td",24)(20,"span",31),t.EFF(21),t.k0s()(),t.j41(22,"td",32)(23,"div",33),t.DNE(24,fi,2,3,"a",34),t.DNE(25,gi,5,0,"div",35),t.DNE(26,bi,2,0,"button",36),t.k0s()()()),2&a){const e=o.$implicit,i=t.XpG(2);t.R7$(4),t.Lme(" ",null==e.etudiant||null==e.etudiant.nom?null:e.etudiant.nom.charAt(0),"",null==e.etudiant||null==e.etudiant.prenom?null:e.etudiant.prenom.charAt(0)," "),t.R7$(3),t.Lme(" ",null==e.etudiant?null:e.etudiant.nom," ",null==e.etudiant?null:e.etudiant.prenom," "),t.R7$(2),t.SpI(" ",null==e.etudiant?null:e.etudiant.email," "),t.R7$(3),t.JRh((null==e.etudiant?null:e.etudiant.group)||"Non sp\xe9cifi\xe9"),t.R7$(3),t.JRh(null==e.projet?null:e.projet.titre),t.R7$(3),t.JRh(i.formatDate(e.dateSoumission)),t.R7$(2),t.Y8G("ngClass",i.getClasseStatut(e)),t.R7$(1),t.SpI(" ",i.getStatutEvaluation(e)," "),t.R7$(3),t.Y8G("ngIf",e.evaluation),t.R7$(1),t.Y8G("ngIf",!e.evaluation),t.R7$(1),t.Y8G("ngIf",e.evaluation)}}function vi(a,o){if(1&a&&(t.j41(0,"div",17)(1,"div",18)(2,"table",19)(3,"thead",20)(4,"tr")(5,"th",21),t.EFF(6," \xc9tudiant "),t.k0s(),t.j41(7,"th",21),t.EFF(8," Groupe "),t.k0s(),t.j41(9,"th",21),t.EFF(10," Projet "),t.k0s(),t.j41(11,"th",21),t.EFF(12," Date de soumission "),t.k0s(),t.j41(13,"th",21),t.EFF(14," Statut "),t.k0s(),t.j41(15,"th",21),t.EFF(16," Actions "),t.k0s()()(),t.j41(17,"tbody",22),t.DNE(18,_i,27,13,"tr",23),t.k0s()()()()),2&a){const e=t.XpG();t.R7$(18),t.Y8G("ngForOf",e.filteredRendus)}}function xi(a,o){1&a&&(t.j41(0,"div",41),t.qSk(),t.j41(1,"svg",42),t.nrm(2,"path",43),t.k0s(),t.joV(),t.j41(3,"h3",44),t.EFF(4,"Aucun rendu disponible"),t.k0s(),t.j41(5,"p",45),t.EFF(6,"Aucun rendu ne correspond \xe0 vos crit\xe8res de filtrage"),t.k0s()())}let yi=(()=>{class a{constructor(e,i,n,r){this.rendusService=e,this.projetService=i,this.router=n,this.datePipe=r,this.rendus=[],this.filteredRendus=[],this.isLoading=!0,this.error="",this.searchTerm="",this.filterStatus="all",this.filtreGroupe="",this.filtreProjet="",this.groupes=[],this.projets=[]}ngOnInit(){this.loadRendus(),this.loadProjets(),this.extractGroupes()}loadRendus(){this.isLoading=!0,this.rendusService.getAllRendus().subscribe({next:e=>{this.rendus=e,this.extractGroupes(),this.applyFilters(),this.isLoading=!1},error:e=>{console.error("Erreur lors du chargement des rendus",e),this.error="Impossible de charger les rendus. Veuillez r\xe9essayer plus tard.",this.isLoading=!1}})}loadProjets(){this.projetService.getProjets().subscribe({next:e=>{this.projets=e},error:e=>{console.error("Erreur lors du chargement des projets",e)}})}extractGroupes(){if(this.rendus&&this.rendus.length>0){const e=new Set;this.rendus.forEach(i=>{i.etudiant?.groupe&&e.add(i.etudiant.groupe)}),this.groupes=Array.from(e)}}applyFilters(){let e=this.rendus;if("evaluated"===this.filterStatus?e=e.filter(i=>i.evaluation&&i.evaluation.scores):"pending"===this.filterStatus&&(e=e.filter(i=>!i.evaluation||!i.evaluation.scores)),""!==this.searchTerm.trim()){const i=this.searchTerm.toLowerCase().trim();e=e.filter(n=>n.etudiant?.nom?.toLowerCase().includes(i)||n.etudiant?.prenom?.toLowerCase().includes(i)||n.projet?.titre?.toLowerCase().includes(i))}this.filtreGroupe&&(e=e.filter(i=>i.etudiant?.groupe===this.filtreGroupe)),this.filtreProjet&&(e=e.filter(i=>i.projet?._id===this.filtreProjet)),this.filteredRendus=e}filtrerRendus(){return this.filteredRendus}onSearchChange(){this.applyFilters()}setFilterStatus(e){this.filterStatus=e,this.applyFilters()}evaluateRendu(e){this.router.navigate(["/admin/projects/evaluate",e])}evaluerRendu(e,i){this.router.navigate(["/admin/projects/evaluate",e],{queryParams:{mode:i}})}viewEvaluationDetails(e){this.router.navigate(["/admin/projects/evaluation-details",e])}getStatusClass(e){return e.evaluation&&e.evaluation.scores?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}getClasseStatut(e){return this.getStatusClass(e)}getStatusText(e){return e.evaluation&&e.evaluation._id||"\xe9valu\xe9"===e.statut?"\xc9valu\xe9":"En attente"}getStatutEvaluation(e){return this.getStatusText(e)}getScoreTotal(e){if(!e.evaluation||!e.evaluation.scores)return 0;const i=e.evaluation.scores;return i.structure+i.pratiques+i.fonctionnalite+i.originalite}getScoreClass(e){return e>=16?"text-green-600":e>=12?"text-blue-600":e>=8?"text-yellow-600":"text-red-600"}formatDate(e){return e&&this.datePipe.transform(e,"dd/MM/yyyy")||""}navigateToEditEvaluation(e){this.router.navigate(["/admin/projects/edit-evaluation",e])}getFileUrl(e){if(!e)return"";let i=e;if(e.includes("/")||e.includes("\\")){const n=e.split(/[\/\\]/);i=n[n.length-1]}return`${D.c.urlBackend}projets/telecharger/${i}`}getFileName(e){if(!e)return"Fichier";if(e.includes("/")||e.includes("\\")){const i=e.split(/[\/\\]/);return i[i.length-1]}return e}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(T.R),t.rXU(O.e),t.rXU(b.Ix),t.rXU(p.vh))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-list-rendus"]],features:[t.Jv_([p.vh])],decls:24,vars:8,consts:[[1,"min-h-screen","bg-[#edf1f4]","p-4","md:p-6"],[1,"max-w-6xl","mx-auto","flex","justify-between","items-center","mb-6"],[1,"text-2xl","md:text-3xl","font-bold","text-[#4f5fad]"],[1,"max-w-6xl","mx-auto","bg-white","rounded-xl","shadow-md","p-4","mb-6"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-4"],[1,"block","text-sm","font-medium","text-gray-700","mb-1"],[1,"w-full","p-2","border","border-gray-300","rounded-md","focus:ring-[#4f5fad]","focus:border-[#4f5fad]",3,"ngModel","ngModelChange"],["value",""],[3,"value",4,"ngFor","ngForOf"],["class","max-w-6xl mx-auto flex justify-center py-12",4,"ngIf"],["class","max-w-6xl mx-auto bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",4,"ngIf"],["class","max-w-6xl mx-auto bg-white rounded-xl shadow-md overflow-hidden",4,"ngIf"],["class","max-w-6xl mx-auto text-center py-12",4,"ngIf"],[3,"value"],[1,"max-w-6xl","mx-auto","flex","justify-center","py-12"],[1,"animate-spin","rounded-full","h-12","w-12","border-t-2","border-b-2","border-[#4f5fad]"],[1,"max-w-6xl","mx-auto","bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded","mb-4"],[1,"max-w-6xl","mx-auto","bg-white","rounded-xl","shadow-md","overflow-hidden"],[1,"overflow-x-auto"],[1,"min-w-full","divide-y","divide-gray-200"],[1,"bg-gray-50"],["scope","col",1,"px-6","py-3","text-left","text-xs","font-medium","text-gray-500","uppercase","tracking-wider"],[1,"bg-white","divide-y","divide-gray-200"],[4,"ngFor","ngForOf"],[1,"px-6","py-4","whitespace-nowrap"],[1,"flex","items-center"],[1,"h-8","w-8","rounded-full","bg-[#6C63FF]","flex","items-center","justify-center","text-white","text-xs","font-bold"],[1,"ml-4"],[1,"text-sm","font-medium","text-gray-900"],[1,"text-sm","text-gray-500"],[1,"text-sm","text-gray-900"],[1,"px-2","inline-flex","text-xs","leading-5","font-semibold","rounded-full",3,"ngClass"],[1,"px-6","py-4","whitespace-nowrap","text-sm","font-medium"],[1,"flex","space-x-2"],["class","text-indigo-600 hover:text-indigo-900",3,"routerLink",4,"ngIf"],["class","flex space-x-2",4,"ngIf"],["class","text-blue-600 hover:text-blue-800 mr-2",3,"click",4,"ngIf"],[1,"text-indigo-600","hover:text-indigo-900",3,"routerLink"],[1,"text-green-600","hover:text-green-900","bg-green-100","px-2","py-1","rounded",3,"click"],[1,"text-blue-600","hover:text-blue-900","bg-blue-100","px-2","py-1","rounded",3,"click"],[1,"text-blue-600","hover:text-blue-800","mr-2",3,"click"],[1,"max-w-6xl","mx-auto","text-center","py-12"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-16","w-16","mx-auto","text-[#bdc6cc]"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","1","d","M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"mt-4","text-lg","font-medium","text-[#6d6870]"],[1,"mt-1","text-sm","text-[#6d6870]"]],template:function(i,n){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"h1",2),t.EFF(3,"Liste des Rendus"),t.k0s()(),t.j41(4,"div",3)(5,"div",4)(6,"div")(7,"label",5),t.EFF(8,"Filtrer par groupe"),t.k0s(),t.j41(9,"select",6),t.bIt("ngModelChange",function(s){return n.filtreGroupe=s})("ngModelChange",function(){return n.applyFilters()}),t.j41(10,"option",7),t.EFF(11,"Tous les groupes"),t.k0s(),t.DNE(12,ci,2,2,"option",8),t.k0s()(),t.j41(13,"div")(14,"label",5),t.EFF(15,"Filtrer par projet"),t.k0s(),t.j41(16,"select",6),t.bIt("ngModelChange",function(s){return n.filtreProjet=s})("ngModelChange",function(){return n.applyFilters()}),t.j41(17,"option",7),t.EFF(18,"Tous les projets"),t.k0s(),t.DNE(19,ui,2,2,"option",8),t.k0s()()()(),t.DNE(20,mi,2,0,"div",9),t.DNE(21,pi,2,1,"div",10),t.DNE(22,vi,19,1,"div",11),t.DNE(23,xi,7,0,"div",12),t.k0s()),2&i&&(t.R7$(9),t.Y8G("ngModel",n.filtreGroupe),t.R7$(3),t.Y8G("ngForOf",n.groupes),t.R7$(4),t.Y8G("ngModel",n.filtreProjet),t.R7$(3),t.Y8G("ngForOf",n.projets),t.R7$(1),t.Y8G("ngIf",n.isLoading),t.R7$(1),t.Y8G("ngIf",n.error),t.R7$(1),t.Y8G("ngIf",!n.isLoading&&n.filteredRendus.length>0),t.R7$(1),t.Y8G("ngIf",!n.isLoading&&0===n.filteredRendus.length))},dependencies:[p.YU,p.Sq,p.bT,b.Wk,d.xH,d.y7,d.wz,d.BC,d.vS],styles:[".loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}"]})}}return a})();function wi(a,o){1&a&&(t.j41(0,"div",6),t.nrm(1,"div",7),t.k0s())}function Ei(a,o){if(1&a&&(t.j41(0,"div",8),t.EFF(1),t.k0s()),2&a){const e=t.XpG();t.R7$(1),t.SpI(" ",e.error," ")}}function ki(a,o){if(1&a&&(t.j41(0,"li")(1,"a",25),t.EFF(2),t.k0s()()),2&a){const e=o.$implicit;t.R7$(1),t.Y8G("href","http://localhost:3000/"+e,t.B4B),t.R7$(1),t.SpI(" ",e.split("/").pop()," ")}}function Ci(a,o){if(1&a&&(t.j41(0,"div",21)(1,"h3",22),t.EFF(2,"Fichiers joints:"),t.k0s(),t.j41(3,"ul",23),t.DNE(4,ki,3,2,"li",24),t.k0s()()),2&a){const e=t.XpG(2);t.R7$(4),t.Y8G("ngForOf",e.rendu.fichiers)}}function Di(a,o){if(1&a){const e=t.RV6();t.j41(0,"form",26),t.bIt("ngSubmit",function(){t.eBV(e);const n=t.XpG(2);return t.Njj(n.onSubmit())}),t.j41(1,"div",27)(2,"div",28)(3,"label",29),t.EFF(4,"Structure du code (0-5)"),t.k0s(),t.nrm(5,"input",30),t.k0s(),t.j41(6,"div",28)(7,"label",29),t.EFF(8,"Bonnes pratiques (0-5)"),t.k0s(),t.nrm(9,"input",31),t.k0s(),t.j41(10,"div",28)(11,"label",29),t.EFF(12,"Fonctionnalit\xe9 (0-5)"),t.k0s(),t.nrm(13,"input",32),t.k0s(),t.j41(14,"div",28)(15,"label",29),t.EFF(16,"Originalit\xe9 (0-5)"),t.k0s(),t.nrm(17,"input",33),t.k0s()(),t.j41(18,"div",34)(19,"label",29),t.EFF(20,"Commentaires"),t.k0s(),t.nrm(21,"textarea",35),t.k0s(),t.j41(22,"div",36)(23,"button",37),t.EFF(24," Soumettre l'\xe9valuation "),t.k0s()()()}if(2&a){const e=t.XpG(2);t.Y8G("formGroup",e.evaluationForm),t.R7$(23),t.Y8G("disabled",e.evaluationForm.invalid||e.isLoading)}}function ji(a,o){if(1&a){const e=t.RV6();t.j41(0,"div")(1,"p",40),t.EFF(2,"L'\xe9valuation sera r\xe9alis\xe9e automatiquement par notre syst\xe8me d'IA (Mistral 7B)."),t.k0s(),t.j41(3,"p",13),t.EFF(4,"L'IA analysera le code soumis et fournira une \xe9valuation bas\xe9e sur les crit\xe8res standards."),t.k0s(),t.j41(5,"div",36)(6,"button",41),t.bIt("click",function(){t.eBV(e);const n=t.XpG(3);return t.Njj(n.onSubmit())}),t.EFF(7," Lancer l'\xe9valuation IA "),t.k0s()()()}if(2&a){const e=t.XpG(3);t.R7$(6),t.Y8G("disabled",e.isSubmitting)}}function Fi(a,o){1&a&&(t.j41(0,"div",42),t.nrm(1,"div",43),t.j41(2,"p",44),t.EFF(3,"L'IA analyse le projet..."),t.k0s(),t.j41(4,"p",45),t.EFF(5,"Cela peut prendre quelques instants"),t.k0s()())}function Mi(a,o){if(1&a&&(t.j41(0,"div",38),t.DNE(1,ji,8,1,"div",5),t.DNE(2,Fi,6,0,"div",39),t.k0s()),2&a){const e=t.XpG(2);t.R7$(1),t.Y8G("ngIf",!e.aiProcessing),t.R7$(1),t.Y8G("ngIf",e.aiProcessing)}}function Ri(a,o){if(1&a){const e=t.RV6();t.j41(0,"div")(1,"div",9)(2,"h2",10),t.EFF(3,"Informations sur le rendu"),t.k0s(),t.j41(4,"p")(5,"span",11),t.EFF(6,"Projet:"),t.k0s(),t.EFF(7),t.k0s(),t.j41(8,"p")(9,"span",11),t.EFF(10,"\xc9tudiant:"),t.k0s(),t.EFF(11),t.k0s(),t.j41(12,"p")(13,"span",11),t.EFF(14,"Date de soumission:"),t.k0s(),t.EFF(15),t.nI1(16,"date"),t.k0s(),t.j41(17,"p")(18,"span",11),t.EFF(19,"Description:"),t.k0s(),t.EFF(20),t.k0s(),t.DNE(21,Ci,5,1,"div",12),t.k0s(),t.j41(22,"div",13)(23,"div",14)(24,"h2",15),t.EFF(25,"Mode d'\xe9valuation"),t.k0s(),t.j41(26,"div",16)(27,"span",17),t.EFF(28),t.k0s(),t.j41(29,"button",18),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.toggleEvaluationMode())}),t.EFF(30," Changer de mode "),t.k0s()()(),t.DNE(31,Di,25,2,"form",19),t.DNE(32,Mi,3,2,"div",20),t.k0s()()}if(2&a){const e=t.XpG();t.R7$(7),t.SpI(" ",e.rendu.projet.titre,""),t.R7$(4),t.Lme(" ",e.rendu.etudiant.nom," ",e.rendu.etudiant.prenom,""),t.R7$(4),t.SpI(" ",t.i5U(16,9,e.rendu.dateSoumission,"dd/MM/yyyy HH:mm"),""),t.R7$(5),t.SpI(" ",e.rendu.description,""),t.R7$(1),t.Y8G("ngIf",e.rendu.fichiers&&e.rendu.fichiers.length>0),t.R7$(7),t.JRh("manual"===e.evaluationMode?"Manuel":"IA"),t.R7$(3),t.Y8G("ngIf","manual"===e.evaluationMode),t.R7$(1),t.Y8G("ngIf","ai"===e.evaluationMode)}}let Oi=(()=>{class a{constructor(e,i,n,r){this.fb=e,this.route=i,this.router=n,this.rendusService=r,this.renduId="",this.rendu=null,this.isLoading=!0,this.isSubmitting=!1,this.error="",this.evaluationMode="manual",this.aiProcessing=!1,this.evaluationForm=this.fb.group({scores:this.fb.group({structure:[0,[d.k0.required,d.k0.min(0),d.k0.max(5)]],pratiques:[0,[d.k0.required,d.k0.min(0),d.k0.max(5)]],fonctionnalite:[0,[d.k0.required,d.k0.min(0),d.k0.max(5)]],originalite:[0,[d.k0.required,d.k0.min(0),d.k0.max(5)]]}),commentaires:["",d.k0.required],utiliserIA:[!1]})}ngOnInit(){this.renduId=this.route.snapshot.paramMap.get("renduId")||"";const e=this.route.snapshot.queryParamMap.get("mode");if("ai"===e||"manual"===e)this.evaluationMode=e,this.evaluationForm.patchValue({utiliserIA:"ai"===e}),localStorage.setItem("evaluationMode",e);else{const i=localStorage.getItem("evaluationMode");("ai"===i||"manual"===i)&&(this.evaluationMode=i,this.evaluationForm.patchValue({utiliserIA:"ai"===i}))}this.renduId?this.loadRendu():(this.error="ID de rendu manquant",this.isLoading=!1)}loadRendu(){this.isLoading=!0,this.rendusService.getRenduById(this.renduId).subscribe({next:e=>{this.rendu=e,this.isLoading=!1},error:e=>{this.error="Erreur lors du chargement du rendu",this.isLoading=!1,console.error(e)}})}toggleEvaluationMode(){this.evaluationMode="manual"===this.evaluationMode?"ai":"manual",this.evaluationForm.patchValue({utiliserIA:"ai"===this.evaluationMode}),localStorage.setItem("evaluationMode",this.evaluationMode)}onSubmit(){"manual"===this.evaluationMode&&this.evaluationForm.invalid||(this.isSubmitting=!0,"ai"===this.evaluationMode&&(this.evaluationForm.patchValue({utiliserIA:!0}),this.aiProcessing=!0),this.rendusService.evaluateRendu(this.renduId,this.evaluationForm.value).subscribe({next:i=>{if("ai"===this.evaluationMode&&i.evaluation){const n=i.evaluation.scores;this.evaluationForm.patchValue({scores:{structure:n.structure||0,pratiques:n.pratiques||0,fonctionnalite:n.fonctionnalite||0,originalite:n.originalite||0},commentaires:i.evaluation.commentaires||"\xc9valuation g\xe9n\xe9r\xe9e par IA"}),this.aiProcessing=!1,this.isSubmitting=!1,this.error="",alert("\xc9valuation par IA r\xe9ussie! Vous pouvez modifier les r\xe9sultats avant de confirmer.")}else this.router.navigate(["/admin/projects/rendus"])},error:i=>{this.error="Erreur lors de l'\xe9valuation du rendu: "+(i.error?.message||i.message||"Erreur inconnue"),this.isSubmitting=!1,this.aiProcessing=!1,console.error(i)}}))}getScoreTotal(){const e=this.evaluationForm.get("scores")?.value;return e?e.structure+e.pratiques+e.fonctionnalite+e.originalite:0}getScoreMaximum(){return 20}annuler(){this.router.navigate(["/admin/projects/rendus"])}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(d.ok),t.rXU(b.nX),t.rXU(b.Ix),t.rXU(T.R))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-project-evaluation"]],decls:7,vars:3,consts:[[1,"container","mx-auto","px-4","py-8"],[1,"max-w-4xl","mx-auto","bg-white","rounded-lg","shadow-md","p-6"],[1,"text-2xl","font-bold","mb-6","text-gray-800"],["class","flex justify-center my-8",4,"ngIf"],["class","bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",4,"ngIf"],[4,"ngIf"],[1,"flex","justify-center","my-8"],[1,"animate-spin","rounded-full","h-12","w-12","border-t-2","border-b-2","border-purple-500"],[1,"bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded","mb-4"],[1,"mb-6","p-4","bg-gray-50","rounded-lg"],[1,"text-xl","font-semibold","mb-2"],[1,"font-medium"],["class","mt-4",4,"ngIf"],[1,"mb-6"],[1,"flex","items-center","justify-between","mb-4"],[1,"text-xl","font-semibold"],[1,"flex","items-center"],[1,"mr-2"],[1,"px-4","py-2","bg-purple-600","text-white","rounded","hover:bg-purple-700","transition-colors",3,"click"],[3,"formGroup","ngSubmit",4,"ngIf"],["class","bg-gray-50 p-4 rounded-lg",4,"ngIf"],[1,"mt-4"],[1,"font-medium","mb-2"],[1,"list-disc","pl-5"],[4,"ngFor","ngForOf"],["target","_blank",1,"text-blue-600","hover:underline",3,"href"],[3,"formGroup","ngSubmit"],["formGroupName","scores",1,"grid","grid-cols-1","md:grid-cols-2","gap-4","mb-6"],[1,"form-group"],[1,"block","text-gray-700","mb-2"],["type","number","formControlName","structure","min","0","max","5",1,"w-full","px-3","py-2","border","rounded","focus:outline-none","focus:ring-2","focus:ring-purple-500"],["type","number","formControlName","pratiques","min","0","max","5",1,"w-full","px-3","py-2","border","rounded","focus:outline-none","focus:ring-2","focus:ring-purple-500"],["type","number","formControlName","fonctionnalite","min","0","max","5",1,"w-full","px-3","py-2","border","rounded","focus:outline-none","focus:ring-2","focus:ring-purple-500"],["type","number","formControlName","originalite","min","0","max","5",1,"w-full","px-3","py-2","border","rounded","focus:outline-none","focus:ring-2","focus:ring-purple-500"],[1,"form-group","mb-6"],["formControlName","commentaires","rows","5",1,"w-full","px-3","py-2","border","rounded","focus:outline-none","focus:ring-2","focus:ring-purple-500"],[1,"flex","justify-end"],["type","submit",1,"px-6","py-2","bg-green-600","text-white","rounded","hover:bg-green-700","transition-colors","disabled:opacity-50",3,"disabled"],[1,"bg-gray-50","p-4","rounded-lg"],["class","text-center py-8",4,"ngIf"],[1,"mb-4"],[1,"px-6","py-2","bg-green-600","text-white","rounded","hover:bg-green-700","transition-colors","disabled:opacity-50",3,"disabled","click"],[1,"text-center","py-8"],[1,"animate-spin","rounded-full","h-12","w-12","border-t-2","border-b-2","border-purple-500","mx-auto","mb-4"],[1,"text-gray-700"],[1,"text-sm","text-gray-500","mt-2"]],template:function(i,n){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"h1",2),t.EFF(3,"\xc9valuation du projet"),t.k0s(),t.DNE(4,wi,2,0,"div",3),t.DNE(5,Ei,2,1,"div",4),t.DNE(6,Ri,33,12,"div",5),t.k0s()()),2&i&&(t.R7$(4),t.Y8G("ngIf",n.isLoading),t.R7$(1),t.Y8G("ngIf",n.error),t.R7$(1),t.Y8G("ngIf",n.rendu&&!n.isLoading))},dependencies:[p.Sq,p.bT,d.qT,d.me,d.Q0,d.BC,d.cb,d.VZ,d.zX,d.j4,d.JD,d.$R,p.vh],styles:[".container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.form-group[_ngcontent-%COMP%]{margin-bottom:1rem}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}"]})}}return a})();function Si(a,o){1&a&&(t.j41(0,"div",10),t.nrm(1,"div",11),t.k0s())}function Ii(a,o){if(1&a&&(t.j41(0,"div",12),t.EFF(1),t.k0s()),2&a){const e=t.XpG();t.R7$(1),t.SpI(" ",e.error," ")}}function Pi(a,o){if(1&a&&(t.j41(0,"div",40)(1,"div",33)(2,"span",41),t.EFF(3,"Structure du code"),t.k0s(),t.j41(4,"span",42),t.EFF(5),t.k0s()(),t.j41(6,"div",43),t.nrm(7,"div",44),t.k0s()()),2&a){const e=t.XpG(2);t.R7$(5),t.SpI("",e.rendu.evaluation.scores.structure,"/5"),t.R7$(2),t.xc7("width",e.rendu.evaluation.scores.structure/5*100,"%")}}function Ai(a,o){if(1&a&&(t.j41(0,"div",40)(1,"div",33)(2,"span",41),t.EFF(3,"Bonnes pratiques"),t.k0s(),t.j41(4,"span",42),t.EFF(5),t.k0s()(),t.j41(6,"div",43),t.nrm(7,"div",45),t.k0s()()),2&a){const e=t.XpG(2);t.R7$(5),t.SpI("",e.rendu.evaluation.scores.pratiques,"/5"),t.R7$(2),t.xc7("width",e.rendu.evaluation.scores.pratiques/5*100,"%")}}function Ti(a,o){if(1&a&&(t.j41(0,"div",40)(1,"div",33)(2,"span",41),t.EFF(3,"Fonctionnalit\xe9s"),t.k0s(),t.j41(4,"span",42),t.EFF(5),t.k0s()(),t.j41(6,"div",43),t.nrm(7,"div",46),t.k0s()()),2&a){const e=t.XpG(2);t.R7$(5),t.SpI("",e.rendu.evaluation.scores.fonctionnalite,"/5"),t.R7$(2),t.xc7("width",e.rendu.evaluation.scores.fonctionnalite/5*100,"%")}}function Li(a,o){if(1&a&&(t.j41(0,"div",40)(1,"div",33)(2,"span",41),t.EFF(3,"Originalit\xe9"),t.k0s(),t.j41(4,"span",42),t.EFF(5),t.k0s()(),t.j41(6,"div",43),t.nrm(7,"div",47),t.k0s()()),2&a){const e=t.XpG(2);t.R7$(5),t.SpI("",e.rendu.evaluation.scores.originalite,"/5"),t.R7$(2),t.xc7("width",e.rendu.evaluation.scores.originalite/5*100,"%")}}function Bi(a,o){if(1&a&&(t.j41(0,"div",29)(1,"h3",48),t.EFF(2,"Commentaires"),t.k0s(),t.j41(3,"div",40)(4,"p",49),t.EFF(5),t.k0s()()()),2&a){const e=t.XpG(2);t.R7$(5),t.JRh(e.rendu.evaluation.commentaires)}}function Ni(a,o){if(1&a&&(t.j41(0,"a",52),t.qSk(),t.j41(1,"svg",53),t.nrm(2,"path",54),t.k0s(),t.joV(),t.j41(3,"span",55),t.EFF(4),t.k0s()()),2&a){const e=o.$implicit,i=t.XpG(3);t.Y8G("href",i.getFileUrl(e),t.B4B)("download",i.getFileName(e)),t.R7$(4),t.JRh(i.getFileName(e))}}function Vi(a,o){if(1&a&&(t.j41(0,"div",29)(1,"h3",48),t.EFF(2,"Fichiers soumis"),t.k0s(),t.j41(3,"div",50),t.DNE(4,Ni,5,3,"a",51),t.k0s()()),2&a){const e=t.XpG(2);t.R7$(4),t.Y8G("ngForOf",e.rendu.fichiers)}}function $i(a,o){if(1&a&&(t.j41(0,"div",56),t.EFF(1),t.nI1(2,"date"),t.k0s()),2&a){const e=t.XpG(2);t.R7$(1),t.SpI(" \xc9valu\xe9 le ",t.i5U(2,1,e.rendu.evaluation.dateEvaluation,"dd/MM/yyyy \xe0 HH:mm")," ")}}const Gi=function(a,o,e,i){return{"bg-green-600":a,"bg-blue-600":o,"bg-yellow-600":e,"bg-red-600":i}};function zi(a,o){if(1&a&&(t.j41(0,"div",13)(1,"div",14)(2,"div",15)(3,"div")(4,"h2",16),t.EFF(5),t.k0s(),t.j41(6,"p",17),t.EFF(7),t.nI1(8,"date"),t.k0s()(),t.j41(9,"div",18)(10,"span",19),t.EFF(11),t.k0s()()(),t.j41(12,"div",20)(13,"div",21),t.EFF(14),t.k0s(),t.j41(15,"div",22)(16,"p",23),t.EFF(17),t.k0s(),t.j41(18,"p",24),t.EFF(19),t.k0s()(),t.j41(20,"div",25)(21,"span",26),t.EFF(22),t.k0s()()()(),t.j41(23,"div",27)(24,"h3",28),t.EFF(25,"D\xe9tails des scores"),t.k0s(),t.j41(26,"div",29)(27,"div",30),t.DNE(28,Pi,8,3,"div",31),t.DNE(29,Ai,8,3,"div",31),t.DNE(30,Ti,8,3,"div",31),t.DNE(31,Li,8,3,"div",31),t.k0s(),t.j41(32,"div",32)(33,"div",33)(34,"span",34),t.EFF(35,"Score total"),t.k0s(),t.j41(36,"span",35),t.EFF(37),t.k0s()(),t.j41(38,"div",36),t.nrm(39,"div",37),t.k0s()()(),t.DNE(40,Bi,6,1,"div",38),t.DNE(41,Vi,5,1,"div",38),t.DNE(42,$i,3,4,"div",39),t.k0s()()),2&a){const e=t.XpG();t.R7$(5),t.JRh(null==e.rendu.projet?null:e.rendu.projet.titre),t.R7$(2),t.SpI("Soumis le ",t.i5U(8,24,e.rendu.dateSoumission,"dd/MM/yyyy \xe0 HH:mm"),""),t.R7$(3),t.Y8G("ngClass",e.getScoreClass()),t.R7$(1),t.Lme(" ",e.getScoreTotal(),"/",e.getScoreMaximum()," "),t.R7$(3),t.Lme(" ",null==e.rendu.etudiant||null==e.rendu.etudiant.nom?null:e.rendu.etudiant.nom.charAt(0),"",null==e.rendu.etudiant||null==e.rendu.etudiant.prenom?null:e.rendu.etudiant.prenom.charAt(0)," "),t.R7$(3),t.Lme("",null==e.rendu.etudiant?null:e.rendu.etudiant.nom," ",null==e.rendu.etudiant?null:e.rendu.etudiant.prenom,""),t.R7$(2),t.JRh(null==e.rendu.etudiant?null:e.rendu.etudiant.email),t.R7$(3),t.SpI(" ",(null==e.rendu.etudiant?null:e.rendu.etudiant.groupe)||"Groupe non sp\xe9cifi\xe9"," "),t.R7$(6),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.scores),t.R7$(1),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.scores),t.R7$(1),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.scores),t.R7$(1),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.scores),t.R7$(5),t.Y8G("ngClass",e.getScoreClass()),t.R7$(1),t.Lme("",e.getScoreTotal(),"/",e.getScoreMaximum(),""),t.R7$(2),t.xc7("width",e.getScorePercentage(),"%"),t.Y8G("ngClass",t.ziG(27,Gi,e.getScorePercentage()>=80,e.getScorePercentage()>=60&&e.getScorePercentage()<80,e.getScorePercentage()>=40&&e.getScorePercentage()<60,e.getScorePercentage()<40)),t.R7$(1),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.commentaires),t.R7$(1),t.Y8G("ngIf",e.rendu.fichiers&&e.rendu.fichiers.length>0),t.R7$(1),t.Y8G("ngIf",null==e.rendu.evaluation?null:e.rendu.evaluation.dateEvaluation)}}let Yi=(()=>{class a{constructor(e,i,n){this.route=e,this.router=i,this.rendusService=n,this.renduId="",this.rendu=null,this.isLoading=!0,this.error=""}ngOnInit(){this.renduId=this.route.snapshot.paramMap.get("renduId")||"",this.renduId?this.loadRendu():(this.error="ID de rendu manquant",this.isLoading=!1)}loadRendu(){this.isLoading=!0,this.rendusService.getRenduById(this.renduId).subscribe({next:e=>{this.rendu=e,this.isLoading=!1},error:e=>{this.error="Erreur lors du chargement du rendu",this.isLoading=!1,console.error(e)}})}getScoreTotal(){if(!this.rendu?.evaluation?.scores)return 0;const e=this.rendu.evaluation.scores;return e.structure+e.pratiques+e.fonctionnalite+e.originalite}getScoreMaximum(){return 20}getScorePercentage(){return this.getScoreTotal()/this.getScoreMaximum()*100}getScoreClass(){const e=this.getScorePercentage();return e>=80?"text-green-600":e>=60?"text-blue-600":e>=40?"text-yellow-600":"text-red-600"}retourListe(){this.router.navigate(["/admin/projects/rendus"])}getFileUrl(e){if(!e)return"";let i=e;if(e.includes("/")||e.includes("\\")){const n=e.split(/[\/\\]/);i=n[n.length-1]}return`${D.c.urlBackend}projets/telecharger/${i}`}getFileName(e){if(!e)return"Fichier";if(e.includes("/")||e.includes("\\")){const i=e.split(/[\/\\]/);return i[i.length-1]}return e}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(b.nX),t.rXU(b.Ix),t.rXU(T.R))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-evaluation-details"]],decls:11,vars:3,consts:[[1,"min-h-screen","bg-[#edf1f4]","p-4","md:p-6"],[1,"max-w-4xl","mx-auto"],[1,"flex","items-center","mb-6"],[1,"mr-4","p-2","rounded-full","hover:bg-gray-200","transition-colors",3,"click"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6","text-gray-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M10 19l-7-7m0 0l7-7m-7 7h18"],[1,"text-2xl","md:text-3xl","font-bold","text-[#4f5fad]"],["class","flex justify-center py-12",4,"ngIf"],["class","bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",4,"ngIf"],["class","bg-white rounded-xl shadow-md overflow-hidden",4,"ngIf"],[1,"flex","justify-center","py-12"],[1,"animate-spin","rounded-full","h-12","w-12","border-t-2","border-b-2","border-[#4f5fad]"],[1,"bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded","mb-4"],[1,"bg-white","rounded-xl","shadow-md","overflow-hidden"],[1,"p-6","border-b","border-gray-200"],[1,"flex","flex-col","md:flex-row","justify-between","items-start","md:items-center","mb-4"],[1,"text-xl","font-bold","text-gray-800"],[1,"text-sm","text-gray-500"],[1,"mt-2","md:mt-0"],[1,"text-2xl","font-bold",3,"ngClass"],[1,"flex","items-center","mb-4"],[1,"h-10","w-10","rounded-full","bg-[#6C63FF]","flex","items-center","justify-center","text-white","font-bold"],[1,"ml-4"],[1,"text-sm","font-medium","text-gray-900"],[1,"text-xs","text-gray-500"],[1,"ml-auto"],[1,"bg-[#f0f4f8]","px-3","py-1","rounded-full","text-xs","font-medium","text-[#4f5fad]"],[1,"p-6"],[1,"text-lg","font-semibold","mb-4"],[1,"mb-6"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-4"],["class","bg-gray-50 p-4 rounded-lg",4,"ngIf"],[1,"mt-6","bg-gray-50","p-4","rounded-lg"],[1,"flex","justify-between","items-center","mb-2"],[1,"text-base","font-medium","text-gray-700"],[1,"text-base","font-bold",3,"ngClass"],[1,"w-full","bg-gray-200","rounded-full","h-3"],[1,"h-3","rounded-full",3,"ngClass"],["class","mb-6",4,"ngIf"],["class","text-sm text-gray-500 text-right",4,"ngIf"],[1,"bg-gray-50","p-4","rounded-lg"],[1,"text-sm","font-medium","text-gray-700"],[1,"text-sm","font-bold"],[1,"w-full","bg-gray-200","rounded-full","h-2.5"],[1,"bg-blue-600","h-2.5","rounded-full"],[1,"bg-green-600","h-2.5","rounded-full"],[1,"bg-purple-600","h-2.5","rounded-full"],[1,"bg-yellow-600","h-2.5","rounded-full"],[1,"text-lg","font-semibold","mb-2"],[1,"text-gray-700","whitespace-pre-line"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-3"],["target","_blank","class","flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",3,"href","download",4,"ngFor","ngForOf"],["target","_blank",1,"flex","items-center","p-3","bg-gray-50","rounded-lg","hover:bg-gray-100","transition-colors",3,"href","download"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-5","w-5","text-gray-500","mr-2"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"],[1,"text-sm","text-gray-700","truncate"],[1,"text-sm","text-gray-500","text-right"]],template:function(i,n){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"button",3),t.bIt("click",function(){return n.retourListe()}),t.qSk(),t.j41(4,"svg",4),t.nrm(5,"path",5),t.k0s()(),t.joV(),t.j41(6,"h1",6),t.EFF(7,"D\xe9tails de l'\xe9valuation"),t.k0s()(),t.DNE(8,Si,2,0,"div",7),t.DNE(9,Ii,2,1,"div",8),t.DNE(10,zi,43,32,"div",9),t.k0s()()),2&i&&(t.R7$(8),t.Y8G("ngIf",n.isLoading),t.R7$(1),t.Y8G("ngIf",n.error),t.R7$(1),t.Y8G("ngIf",!n.isLoading&&!n.error&&n.rendu))},dependencies:[p.YU,p.Sq,p.bT,p.vh],styles:[".loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}"]})}}return a})();var Y=f(9437),Xi=f(980),tt=f(8810),Hi=f(1626);let Ui=(()=>{class a{constructor(e){this.http=e}getAllEvaluations(){return this.http.get(`${D.c.urlBackend}evaluations/getev`).pipe((0,Y.W)(i=>(console.error("Erreur HTTP lors de la r\xe9cup\xe9ration des \xe9valuations:",i),(0,tt.$)(()=>new Error("Erreur lors de la r\xe9cup\xe9ration des \xe9valuations")))))}getEvaluationById(e){return this.http.get(`${D.c.urlBackend}evaluations/${e}`).pipe((0,Y.W)(n=>(console.error("Erreur HTTP lors de la r\xe9cup\xe9ration de l'\xe9valuation:",n),(0,tt.$)(()=>new Error("Erreur lors de la r\xe9cup\xe9ration de l'\xe9valuation")))))}updateMissingGroups(){return this.http.post(`${D.c.urlBackend}evaluations/update-missing-groups`,{}).pipe((0,Y.W)(i=>(console.error("Erreur HTTP lors de la mise \xe0 jour des groupes:",i),(0,tt.$)(()=>new Error("Erreur lors de la mise \xe0 jour des groupes")))))}static{this.\u0275fac=function(i){return new(i||a)(t.KVO(Hi.Qq))}}static{this.\u0275prov=t.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})();function qi(a,o){1&a&&(t.j41(0,"div",8),t.nrm(1,"div",9),t.k0s())}function Zi(a,o){if(1&a){const e=t.RV6();t.j41(0,"div",10)(1,"div",11)(2,"div"),t.EFF(3),t.k0s(),t.j41(4,"button",12),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.loadEvaluations())}),t.EFF(5," R\xe9essayer "),t.k0s()()()}if(2&a){const e=t.XpG();t.R7$(3),t.JRh(e.error)}}function Ki(a,o){if(1&a&&(t.j41(0,"option",25),t.EFF(1),t.k0s()),2&a){const e=o.$implicit;t.Y8G("value",e),t.R7$(1),t.JRh(e)}}function Wi(a,o){if(1&a&&(t.j41(0,"option",25),t.EFF(1),t.k0s()),2&a){const e=o.$implicit;t.Y8G("value",e._id),t.R7$(1),t.JRh(e.titre)}}function Qi(a,o){if(1&a){const e=t.RV6();t.j41(0,"div",13)(1,"div",14)(2,"div")(3,"label",15),t.EFF(4,"Recherche"),t.k0s(),t.j41(5,"input",16),t.bIt("ngModelChange",function(n){t.eBV(e);const r=t.XpG();return t.Njj(r.searchTerm=n)})("input",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.onSearchChange())}),t.k0s()(),t.j41(6,"div")(7,"label",17),t.EFF(8,"Groupe"),t.k0s(),t.j41(9,"select",18),t.bIt("ngModelChange",function(n){t.eBV(e);const r=t.XpG();return t.Njj(r.filterGroupe=n)})("change",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.applyFilters())}),t.j41(10,"option",19),t.EFF(11,"Tous les groupes"),t.k0s(),t.DNE(12,Ki,2,2,"option",20),t.k0s()(),t.j41(13,"div")(14,"label",21),t.EFF(15,"Projet"),t.k0s(),t.j41(16,"select",22),t.bIt("ngModelChange",function(n){t.eBV(e);const r=t.XpG();return t.Njj(r.filterProjet=n)})("change",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.applyFilters())}),t.j41(17,"option",19),t.EFF(18,"Tous les projets"),t.k0s(),t.DNE(19,Wi,2,2,"option",20),t.k0s()(),t.j41(20,"div",23)(21,"button",24),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.resetFilters())}),t.EFF(22," R\xe9initialiser les filtres "),t.k0s()()()()}if(2&a){const e=t.XpG();t.R7$(5),t.Y8G("ngModel",e.searchTerm),t.R7$(4),t.Y8G("ngModel",e.filterGroupe),t.R7$(3),t.Y8G("ngForOf",e.groupes),t.R7$(4),t.Y8G("ngModel",e.filterProjet),t.R7$(3),t.Y8G("ngForOf",e.projets)}}function Ji(a,o){if(1&a){const e=t.RV6();t.j41(0,"div",26)(1,"button",27),t.bIt("click",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.updateMissingGroups())}),t.EFF(2," Mettre \xe0 jour les groupes manquants "),t.k0s()()}}function to(a,o){if(1&a&&(t.j41(0,"div",45),t.EFF(1),t.k0s()),2&a){const e=t.XpG().$implicit;t.R7$(1),t.SpI(" ID: ",e.projet||"Non disponible"," ")}}function eo(a,o){if(1&a){const e=t.RV6();t.j41(0,"tr")(1,"td",34)(2,"div",35)(3,"div")(4,"div",36),t.EFF(5),t.k0s(),t.j41(6,"div",37),t.EFF(7),t.k0s()()()(),t.j41(8,"td",34)(9,"div",38),t.EFF(10),t.k0s()(),t.j41(11,"td",34)(12,"div",38),t.EFF(13),t.k0s(),t.DNE(14,to,2,1,"div",39),t.k0s(),t.j41(15,"td",34)(16,"div",38),t.EFF(17),t.k0s()(),t.j41(18,"td",34)(19,"span",40),t.EFF(20),t.k0s()(),t.j41(21,"td",41)(22,"div",42)(23,"button",43),t.bIt("click",function(){const r=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(s.viewEvaluationDetails(r.rendu))}),t.EFF(24," Voir "),t.k0s(),t.j41(25,"button",44),t.bIt("click",function(){const r=t.eBV(e).$implicit,s=t.XpG(2);return t.Njj(s.editEvaluation(r.rendu))}),t.EFF(26," Modifier "),t.k0s()()()()}if(2&a){const e=o.$implicit,i=t.XpG(2);t.R7$(5),t.Lme(" ",(null==e.etudiant?null:e.etudiant.nom)||"N/A"," ",(null==e.etudiant?null:e.etudiant.prenom)||""," "),t.R7$(2),t.SpI(" ",(null==e.etudiant?null:e.etudiant.email)||"Email non disponible"," "),t.R7$(3),t.JRh((null==e.etudiant?null:e.etudiant.groupe)||"Non sp\xe9cifi\xe9"),t.R7$(3),t.SpI(" ",(null==e.projetDetails?null:e.projetDetails.titre)||(null==e.renduDetails||null==e.renduDetails.projet?null:e.renduDetails.projet.titre)||"Projet inconnu"," "),t.R7$(1),t.Y8G("ngIf",!(null!=e.projetDetails&&e.projetDetails.titre||null!=e.renduDetails&&null!=e.renduDetails.projet&&e.renduDetails.projet.titre)),t.R7$(3),t.JRh(i.formatDate(e.dateEvaluation)),t.R7$(2),t.Y8G("ngClass",i.getScoreClass(i.getScoreTotal(e))),t.R7$(1),t.SpI(" ",i.getScoreTotal(e),"/20 ")}}function io(a,o){if(1&a&&(t.j41(0,"div",28)(1,"table",29)(2,"thead",30)(3,"tr")(4,"th",31),t.EFF(5," \xc9tudiant "),t.k0s(),t.j41(6,"th",31),t.EFF(7," Groupe "),t.k0s(),t.j41(8,"th",31),t.EFF(9," Projet "),t.k0s(),t.j41(10,"th",31),t.EFF(11," Date d'\xe9valuation "),t.k0s(),t.j41(12,"th",31),t.EFF(13," Score "),t.k0s(),t.j41(14,"th",31),t.EFF(15," Actions "),t.k0s()()(),t.j41(16,"tbody",32),t.DNE(17,eo,27,9,"tr",33),t.k0s()()()),2&a){const e=t.XpG();t.R7$(17),t.Y8G("ngForOf",e.filteredEvaluations)}}function oo(a,o){1&a&&(t.j41(0,"div",46)(1,"p",47),t.EFF(2,"Aucune \xe9valuation trouv\xe9e"),t.k0s()())}function ao(a,o){1&a&&(t.j41(0,"div",5),t.nrm(1,"div",6),t.k0s())}function ro(a,o){if(1&a&&(t.j41(0,"div",7),t.EFF(1),t.k0s()),2&a){const e=t.XpG();t.R7$(1),t.SpI(" ",e.error," ")}}function so(a,o){if(1&a){const e=t.RV6();t.j41(0,"div")(1,"div",8)(2,"h2",9),t.EFF(3,"Informations sur le rendu"),t.k0s(),t.j41(4,"p")(5,"span",10),t.EFF(6,"Projet:"),t.k0s(),t.EFF(7),t.k0s(),t.j41(8,"p")(9,"span",10),t.EFF(10,"\xc9tudiant:"),t.k0s(),t.EFF(11),t.k0s(),t.j41(12,"p")(13,"span",10),t.EFF(14,"Date de soumission:"),t.k0s(),t.EFF(15),t.nI1(16,"date"),t.k0s(),t.j41(17,"p")(18,"span",10),t.EFF(19,"Description:"),t.k0s(),t.EFF(20),t.k0s()(),t.j41(21,"form",11),t.bIt("ngSubmit",function(){t.eBV(e);const n=t.XpG();return t.Njj(n.onSubmit())}),t.j41(22,"div",12)(23,"h3",13),t.EFF(24,"Crit\xe8res d'\xe9valuation (sur 5 points chacun)"),t.k0s(),t.j41(25,"div",14)(26,"div")(27,"label",15),t.EFF(28," Structure et organisation du code "),t.k0s(),t.nrm(29,"input",16),t.k0s()()()()()}if(2&a){const e=t.XpG();t.R7$(7),t.SpI(" ",null==e.rendu.projet?null:e.rendu.projet.titre,""),t.R7$(4),t.Lme(" ",null==e.rendu.etudiant?null:e.rendu.etudiant.nom," ",null==e.rendu.etudiant?null:e.rendu.etudiant.prenom,""),t.R7$(4),t.SpI(" ",t.i5U(16,6,e.rendu.dateSoumission,"dd/MM/yyyy HH:mm"),""),t.R7$(5),t.SpI(" ",e.rendu.description,""),t.R7$(1),t.Y8G("formGroup",e.evaluationForm)}}const lo=[{path:"",component:Ye},{path:"new",component:We},{path:"editProjet/:id",component:Qe},{path:"details/:id",component:di},{path:"rendus",component:yi},{path:"evaluate/:renduId",component:Oi},{path:"evaluation-details/:renduId",component:Yi},{path:"evaluations",component:(()=>{class a{constructor(e,i,n){this.rendusService=e,this.evaluationService=i,this.router=n,this.evaluations=[],this.filteredEvaluations=[],this.isLoading=!0,this.error="",this.searchTerm="",this.filterGroupe="",this.filterProjet="",this.groupes=[],this.projets=[],this.destroy$=new E.B}ngOnInit(){this.loadEvaluations()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}loadEvaluations(){this.isLoading=!0,this.error="",this.evaluationService.getAllEvaluations().pipe((0,it.Q)(this.destroy$),(0,Y.W)(e=>(this.error="Impossible de charger les \xe9valuations. Veuillez r\xe9essayer plus tard.",this.isLoading=!1,(0,bt.of)([]))),(0,Xi.j)(()=>{this.isLoading=!1})).subscribe({next:e=>{Array.isArray(e)?(this.evaluations=e.map(i=>{const n=i;return(!n.projetDetails||!n.projetDetails.titre)&&n.renduDetails&&n.renduDetails.projet&&(n.projetDetails=n.renduDetails.projet),n}),this.extractGroupesAndProjets(),this.applyFilters()):this.error="Format de donn\xe9es incorrect. Veuillez r\xe9essayer plus tard."}})}extractGroupesAndProjets(){const e=new Set;this.evaluations.forEach(n=>{const r=n.etudiant?.groupe;r&&""!==r.trim()&&e.add(r)}),this.groupes=Array.from(e).sort();const i=new Map;this.evaluations.forEach(n=>{n.projetDetails&&n.projetDetails._id&&i.set(n.projetDetails._id,n.projetDetails)}),this.projets=Array.from(i.values())}applyFilters(){let e=this.evaluations;if(""!==this.searchTerm.trim()){const i=this.searchTerm.toLowerCase().trim();e=e.filter(n=>n.etudiant?.nom?.toLowerCase().includes(i)||n.etudiant?.prenom?.toLowerCase().includes(i)||n.projetDetails?.titre?.toLowerCase().includes(i))}this.filterGroupe&&(e=e.filter(i=>i.etudiant?.groupe===this.filterGroupe)),this.filterProjet&&(e=e.filter(i=>i.projetDetails?._id===this.filterProjet)),this.filteredEvaluations=e}onSearchChange(){this.applyFilters()}resetFilters(){this.searchTerm="",this.filterGroupe="",this.filterProjet="",this.applyFilters()}editEvaluation(e){this.router.navigate(["/admin/projects/edit-evaluation",e])}viewEvaluationDetails(e){this.router.navigate(["/admin/projects/evaluation-details",e])}getScoreTotal(e){if(!e.scores)return 0;const i=e.scores;return i.structure+i.pratiques+i.fonctionnalite+i.originalite}getScoreClass(e){return e>=16?"text-green-600 bg-green-100":e>=12?"text-blue-600 bg-blue-100":e>=8?"text-yellow-600 bg-yellow-100":"text-red-600 bg-red-100"}formatDate(e){return e?new Date(e).toLocaleDateString():"Non disponible"}updateMissingGroups(){confirm("Voulez-vous mettre \xe0 jour les groupes manquants des \xe9tudiants?")&&(this.isLoading=!0,this.evaluationService.updateMissingGroups().subscribe({next:e=>{alert(`${e.updatedCount} \xe9tudiants mis \xe0 jour avec leur groupe.`),this.loadEvaluations()},error:e=>{alert("Erreur lors de la mise \xe0 jour des groupes."),this.isLoading=!1}}))}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(T.R),t.rXU(Ui),t.rXU(b.Ix))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-evaluations-list"]],decls:9,vars:6,consts:[[1,"container","mx-auto","px-4","py-6"],[1,"text-2xl","font-bold","mb-6"],["class","flex justify-center items-center py-10",4,"ngIf"],["class","bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",4,"ngIf"],["class","bg-white rounded-lg shadow-md p-4 mb-6",4,"ngIf"],["class","flex justify-end mb-4",4,"ngIf"],["class","bg-white shadow-md rounded-lg overflow-hidden",4,"ngIf"],["class","bg-white shadow-md rounded-lg p-6 text-center",4,"ngIf"],[1,"flex","justify-center","items-center","py-10"],[1,"animate-spin","rounded-full","h-12","w-12","border-t-2","border-b-2","border-indigo-500"],[1,"bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded","mb-4"],[1,"flex","justify-between","items-center"],[1,"bg-red-200","hover:bg-red-300","text-red-800","font-bold","py-1","px-4","rounded","focus:outline-none","focus:shadow-outline",3,"click"],[1,"bg-white","rounded-lg","shadow-md","p-4","mb-6"],[1,"grid","grid-cols-1","md:grid-cols-4","gap-4"],["for","search",1,"block","text-sm","font-medium","text-gray-700","mb-1"],["type","text","id","search","placeholder","Nom, pr\xe9nom, projet...",1,"w-full","px-3","py-2","border","border-gray-300","rounded-md","shadow-sm","focus:outline-none","focus:ring-indigo-500","focus:border-indigo-500",3,"ngModel","ngModelChange","input"],["for","groupe",1,"block","text-sm","font-medium","text-gray-700","mb-1"],["id","groupe",1,"w-full","px-3","py-2","border","border-gray-300","rounded-md","shadow-sm","focus:outline-none","focus:ring-indigo-500","focus:border-indigo-500",3,"ngModel","ngModelChange","change"],["value",""],[3,"value",4,"ngFor","ngForOf"],["for","projet",1,"block","text-sm","font-medium","text-gray-700","mb-1"],["id","projet",1,"w-full","px-3","py-2","border","border-gray-300","rounded-md","shadow-sm","focus:outline-none","focus:ring-indigo-500","focus:border-indigo-500",3,"ngModel","ngModelChange","change"],[1,"flex","items-end"],[1,"w-full","px-4","py-2","bg-gray-200","text-gray-700","rounded-md","hover:bg-gray-300","focus:outline-none","focus:ring-2","focus:ring-gray-500",3,"click"],[3,"value"],[1,"flex","justify-end","mb-4"],[1,"bg-indigo-600","hover:bg-indigo-700","text-white","font-bold","py-2","px-4","rounded","focus:outline-none","focus:shadow-outline",3,"click"],[1,"bg-white","shadow-md","rounded-lg","overflow-hidden"],[1,"min-w-full","divide-y","divide-gray-200"],[1,"bg-gray-50"],["scope","col",1,"px-6","py-3","text-left","text-xs","font-medium","text-gray-500","uppercase","tracking-wider"],[1,"bg-white","divide-y","divide-gray-200"],[4,"ngFor","ngForOf"],[1,"px-6","py-4","whitespace-nowrap"],[1,"flex","items-center"],[1,"text-sm","font-medium","text-gray-900"],[1,"text-sm","text-gray-500"],[1,"text-sm","text-gray-900"],["class","text-xs text-red-500",4,"ngIf"],[1,"px-2","py-1","rounded-full","text-sm","font-medium",3,"ngClass"],[1,"px-6","py-4","whitespace-nowrap","text-sm","font-medium"],[1,"flex","space-x-2"],[1,"text-indigo-600","hover:text-indigo-900",3,"click"],[1,"text-green-600","hover:text-green-900",3,"click"],[1,"text-xs","text-red-500"],[1,"bg-white","shadow-md","rounded-lg","p-6","text-center"],[1,"text-gray-500"]],template:function(i,n){1&i&&(t.j41(0,"div",0)(1,"h1",1),t.EFF(2,"Liste des \xe9valuations"),t.k0s(),t.DNE(3,qi,2,0,"div",2),t.DNE(4,Zi,6,1,"div",3),t.DNE(5,Qi,23,5,"div",4),t.DNE(6,Ji,3,0,"div",5),t.DNE(7,io,18,1,"div",6),t.DNE(8,oo,3,0,"div",7),t.k0s()),2&i&&(t.R7$(3),t.Y8G("ngIf",n.isLoading),t.R7$(1),t.Y8G("ngIf",n.error),t.R7$(1),t.Y8G("ngIf",!n.isLoading&&!n.error),t.R7$(1),t.Y8G("ngIf",!n.isLoading&&!n.error),t.R7$(1),t.Y8G("ngIf",!n.isLoading&&!n.error&&n.filteredEvaluations.length>0),t.R7$(1),t.Y8G("ngIf",!n.isLoading&&!n.error&&0===n.filteredEvaluations.length))},dependencies:[p.YU,p.Sq,p.bT,d.xH,d.y7,d.me,d.wz,d.BC,d.vS],styles:[".evaluations-container[_ngcontent-%COMP%]{padding:20px}.filters[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:15px;margin-bottom:20px;padding:15px;background-color:#f5f5f5;border-radius:5px}.filter-item[_ngcontent-%COMP%]{flex:1;min-width:200px}.evaluations-table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse;margin-top:20px}.evaluations-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .evaluations-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:12px;text-align:left;border-bottom:1px solid #ddd}.evaluations-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:#f2f2f2;font-weight:700}.evaluations-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#f5f5f5}.actions-cell[_ngcontent-%COMP%]{display:flex;gap:10px}.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:50px 0}.no-evaluations[_ngcontent-%COMP%]{text-align:center;margin:50px 0;color:#666}.score-badge[_ngcontent-%COMP%]{padding:5px 10px;border-radius:15px;font-weight:700;display:inline-block}.score-high[_ngcontent-%COMP%]{background-color:#c8e6c9;color:#2e7d32}.score-medium[_ngcontent-%COMP%]{background-color:#fff9c4;color:#f57f17}.score-low[_ngcontent-%COMP%]{background-color:#ffcdd2;color:#c62828}"]})}}return a})()},{path:"edit-evaluation/:renduId",component:(()=>{class a{constructor(e,i,n,r){this.fb=e,this.route=i,this.router=n,this.rendusService=r,this.renduId="",this.rendu=null,this.isLoading=!0,this.isSubmitting=!1,this.error="",this.evaluationForm=this.fb.group({scores:this.fb.group({structure:[0,[d.k0.required,d.k0.min(0),d.k0.max(5)]],pratiques:[0,[d.k0.required,d.k0.min(0),d.k0.max(5)]],fonctionnalite:[0,[d.k0.required,d.k0.min(0),d.k0.max(5)]],originalite:[0,[d.k0.required,d.k0.min(0),d.k0.max(5)]]}),commentaires:["",d.k0.required]})}ngOnInit(){this.renduId=this.route.snapshot.paramMap.get("renduId")||"",this.renduId?this.loadRendu():(this.error="ID de rendu manquant",this.isLoading=!1)}loadRendu(){this.isLoading=!0,this.rendusService.getRenduById(this.renduId).subscribe({next:e=>{this.rendu=e,this.rendu.evaluation&&this.rendu.evaluation.scores&&this.evaluationForm.patchValue({scores:{structure:this.rendu.evaluation.scores.structure||0,pratiques:this.rendu.evaluation.scores.pratiques||0,fonctionnalite:this.rendu.evaluation.scores.fonctionnalite||0,originalite:this.rendu.evaluation.scores.originalite||0},commentaires:this.rendu.evaluation.commentaires||""}),this.isLoading=!1},error:e=>{this.error="Erreur lors du chargement du rendu",this.isLoading=!1,console.error(e)}})}onSubmit(){this.evaluationForm.invalid||(this.isSubmitting=!0,this.rendusService.updateEvaluation(this.renduId,this.evaluationForm.value).subscribe({next:()=>{this.router.navigate(["/admin/projects/evaluations"])},error:i=>{this.error="Erreur lors de la mise \xe0 jour de l'\xe9valuation: "+(i.error?.message||i.message||"Erreur inconnue"),this.isSubmitting=!1,console.error(i)}}))}getScoreTotal(){const e=this.evaluationForm.get("scores")?.value;return e?e.structure+e.pratiques+e.fonctionnalite+e.originalite:0}getScoreMaximum(){return 20}annuler(){this.router.navigate(["/admin/projects/evaluations"])}getFileUrl(e){if(!e)return"";let i=e;if(e.includes("/")||e.includes("\\")){const n=e.split(/[\/\\]/);i=n[n.length-1]}return`${D.c.urlBackend}projets/telecharger/${i}`}getFileName(e){if(!e)return"Fichier";if(e.includes("/")||e.includes("\\")){const i=e.split(/[\/\\]/);return i[i.length-1]}return e}static{this.\u0275fac=function(i){return new(i||a)(t.rXU(d.ok),t.rXU(b.nX),t.rXU(b.Ix),t.rXU(T.R))}}static{this.\u0275cmp=t.VBU({type:a,selectors:[["app-edit-evaluation"]],decls:6,vars:3,consts:[[1,"container","mx-auto","px-4","py-6"],[1,"text-2xl","font-bold","mb-6"],["class","flex justify-center items-center py-10",4,"ngIf"],["class","bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",4,"ngIf"],[4,"ngIf"],[1,"flex","justify-center","items-center","py-10"],[1,"animate-spin","rounded-full","h-12","w-12","border-t-2","border-b-2","border-indigo-500"],[1,"bg-red-100","border","border-red-400","text-red-700","px-4","py-3","rounded","mb-4"],[1,"mb-6","p-4","bg-gray-50","rounded-lg"],[1,"text-xl","font-semibold","mb-2"],[1,"font-medium"],[1,"bg-white","rounded-lg","shadow-md","p-6",3,"formGroup","ngSubmit"],["formGroupName","scores",1,"mb-6"],[1,"text-lg","font-semibold","mb-4"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-4"],["for","structure",1,"block","text-gray-700","text-sm","font-bold","mb-2"],["id","structure","type","number","formControlName","structure","min","0","max","5",1,"shadow","appearance-none","border","rounded","w-full","py-2","px-3","text-gray-700","leading-tight","focus:outline-none","focus:shadow-outline"]],template:function(i,n){1&i&&(t.j41(0,"div",0)(1,"h1",1),t.EFF(2,"Modifier l'\xe9valuation"),t.k0s(),t.DNE(3,ao,2,0,"div",2),t.DNE(4,ro,2,1,"div",3),t.DNE(5,so,30,9,"div",4),t.k0s()),2&i&&(t.R7$(3),t.Y8G("ngIf",n.isLoading),t.R7$(1),t.Y8G("ngIf",n.error),t.R7$(1),t.Y8G("ngIf",n.rendu&&!n.isLoading))},dependencies:[p.bT,d.qT,d.me,d.Q0,d.BC,d.cb,d.VZ,d.zX,d.j4,d.JD,d.$R,p.vh],styles:[".evaluation-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:20px}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}.score-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:20px;margin-bottom:20px}.score-item[_ngcontent-%COMP%]{display:flex;flex-direction:column}.score-total[_ngcontent-%COMP%]{margin-top:20px;font-weight:700;font-size:1.2em}.actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-top:30px}.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:50px 0}.error-message[_ngcontent-%COMP%]{color:#f44336;margin:10px 0}"]})}}return a})()}];let co=(()=>{class a{static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({imports:[b.iI.forChild(lo),b.iI]})}}return a})(),po=(()=>{class a{static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({imports:[R,fe,R]})}}return a})(),ho=(()=>{class a{static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275mod=t.$C({type:a})}static{this.\u0275inj=t.G2t({imports:[p.MD,co,d.YN,d.X1,b.iI,Me,po]})}}return a})()}}]);