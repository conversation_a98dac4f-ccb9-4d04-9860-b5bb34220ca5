{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"angular-calendar\";\nexport let PlanningsModule = /*#__PURE__*/(() => {\n  class PlanningsModule {\n    static {\n      this.ɵfac = function PlanningsModule_Factory(t) {\n        return new (t || PlanningsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: PlanningsModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, CalendarModule.forRoot({\n          provide: DateAdapter,\n          useFactory: adapterFactory\n        }), PipesModule]\n      });\n    }\n  }\n  return PlanningsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}