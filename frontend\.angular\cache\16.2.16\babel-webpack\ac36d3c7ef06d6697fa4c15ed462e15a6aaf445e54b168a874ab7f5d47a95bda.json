{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/file.service\";\nimport * as i4 from \"@angular/common\";\nfunction DetailProjectComponent_div_18_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"a\", 42);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 43);\n    i0.ɵɵelement(3, \"path\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", ctx_r4.getFileUrl(file_r5), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailProjectComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h4\", 35);\n    i0.ɵɵtext(2, \"Fichiers joints\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39);\n    i0.ɵɵtemplate(4, DetailProjectComponent_div_18_div_4_Template, 6, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projet.fichiers);\n  }\n}\nfunction DetailProjectComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \" Aucun fichier joint \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailProjectComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 48)(4, \"div\", 49);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 32);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const etudiant_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", (etudiant_r6.nom == null ? null : etudiant_r6.nom.charAt(0)) || \"\", \"\", (etudiant_r6.prenom == null ? null : etudiant_r6.prenom.charAt(0)) || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", etudiant_r6.prenom, \" \", etudiant_r6.nom, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(etudiant_r6.dateRendu));\n  }\n}\nfunction DetailProjectComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \" Aucun rendu pour le moment \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c1 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c2 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nconst _c3 = function () {\n  return [];\n};\nexport let DetailProjectComponent = /*#__PURE__*/(() => {\n  class DetailProjectComponent {\n    constructor(route, router, projectService, fileService) {\n      this.route = route;\n      this.router = router;\n      this.projectService = projectService;\n      this.fileService = fileService;\n      this.projet = null;\n    }\n    ngOnInit() {\n      const id = this.route.snapshot.paramMap.get('id');\n      if (id) {\n        this.projectService.getProjetById(id).subscribe(data => {\n          this.projet = data;\n        });\n      }\n    }\n    getFileUrl(filePath) {\n      return this.fileService.getDownloadUrl(filePath);\n    }\n    deleteProjet(id) {\n      if (!id) return;\n      if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n        this.projectService.deleteProjet(id).subscribe({\n          next: () => {\n            alert('Projet supprimé avec succès');\n            this.router.navigate(['/admin/projects']);\n          },\n          error: err => {\n            console.error('Erreur lors de la suppression du projet', err);\n            alert('Erreur lors de la suppression du projet');\n          }\n        });\n      }\n    }\n    formatDate(date) {\n      const d = new Date(date);\n      return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;\n    }\n    static {\n      this.ɵfac = function DetailProjectComponent_Factory(t) {\n        return new (t || DetailProjectComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.FileService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DetailProjectComponent,\n        selectors: [[\"app-detail-project\"]],\n        decls: 63,\n        vars: 24,\n        consts: [[1, \"max-w-6xl\", \"mx-auto\", \"my-8\", \"space-y-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"md:col-span-2\", \"bg-white\", \"rounded-2xl\", \"shadow-md\", \"hover:shadow-xl\", \"transition-shadow\", \"duration-300\", \"overflow-hidden\", \"border\", \"border-[#e4e7ec]\"], [1, \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#C77DFF]\", \"p-6\", \"text-white\", \"rounded-t-2xl\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-2xl\", \"font-bold\"], [1, \"mt-1\", \"text-sm\", \"text-[#e4dbf8]\"], [1, \"px-3\", \"py-1\", \"bg-white/20\", \"rounded-full\", \"text-xs\", \"font-semibold\"], [1, \"p-6\", \"space-y-6\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-600\", \"mb-2\"], [1, \"text-gray-700\"], [4, \"ngIf\"], [\"class\", \"text-sm text-gray-500 italic\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-3\", \"pt-4\", \"border-t\", \"border-gray-100\"], [1, \"flex-1\", \"bg-gradient-to-r\", \"from-[#3CAEA3]\", \"to-[#20BF55]\", \"hover:from-[#2d9b91]\", \"hover:to-[#18a046]\", \"text-white\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"text-center\", \"shadow-sm\", \"hover:shadow-md\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\", 3, \"routerLink\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"flex-1\", \"bg-gradient-to-r\", \"from-[#F5576C]\", \"to-[#F093FB]\", \"hover:from-[#e04054]\", \"hover:to-[#d87fe0]\", \"text-white\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"text-center\", \"shadow-sm\", \"hover:shadow-md\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"flex-1\", \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#8E2DE2]\", \"hover:from-[#5046e5]\", \"hover:to-[#7816c7]\", \"text-white\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"text-center\", \"shadow-sm\", \"hover:shadow-md\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"bg-white\", \"rounded-2xl\", \"shadow-md\", \"border\", \"border-[#e4e7ec]\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#C77DFF]\", \"p-4\", \"text-white\"], [1, \"font-semibold\"], [1, \"flex\", \"justify-between\", \"mb-2\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [1, \"text-sm\", \"font-medium\", \"text-[#6C63FF]\"], [1, \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-2.5\"], [1, \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#C77DFF]\", \"h-2.5\", \"rounded-full\"], [1, \"grid\", \"grid-cols-2\", \"gap-4\"], [1, \"bg-[#f0f7ff]\", \"p-4\", \"rounded-lg\", \"border\", \"border-[#e4e7ec]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4A00E0]\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"bg-[#fff5f5]\", \"p-4\", \"rounded-lg\", \"border\", \"border-[#e4e7ec]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#E02D6D]\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-600\", \"mb-3\"], [1, \"space-y-3\"], [\"class\", \"flex items-center space-x-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-sm text-gray-500 italic text-center py-2\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-3\"], [\"class\", \"flex items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\"], [\"download\", \"\", 1, \"flex-1\", \"text-center\", \"text-sm\", \"text-white\", \"bg-gradient-to-r\", \"from-[#8E2DE2]\", \"to-[#4A00E0]\", \"hover:from-[#7c22d2]\", \"hover:to-[#3f00cc]\", \"rounded-lg\", \"py-2\", \"px-4\", \"transition-all\", \"hover:scale-[1.02]\", \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", 3, \"href\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"text-sm\", \"text-gray-500\", \"italic\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#6C63FF]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-xs\", \"font-bold\"], [1, \"text-sm\"], [1, \"font-medium\"], [1, \"text-sm\", \"text-gray-500\", \"italic\", \"text-center\", \"py-2\"]],\n        template: function DetailProjectComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\")(6, \"h3\", 5);\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\", 6);\n            i0.ɵɵtext(9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"span\", 7);\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\")(14, \"h4\", 9);\n            i0.ɵɵtext(15, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"p\", 10);\n            i0.ɵɵtext(17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(18, DetailProjectComponent_div_18_Template, 5, 1, \"div\", 11);\n            i0.ɵɵtemplate(19, DetailProjectComponent_div_19_Template, 2, 0, \"div\", 12);\n            i0.ɵɵelementStart(20, \"div\", 13)(21, \"a\", 14);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(22, \"svg\", 15);\n            i0.ɵɵelement(23, \"path\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(24, \" Modifier \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(25, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function DetailProjectComponent_Template_button_click_25_listener() {\n              return ctx.deleteProjet(ctx.projet == null ? null : ctx.projet._id);\n            });\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(26, \"svg\", 15);\n            i0.ɵɵelement(27, \"path\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(28, \" Supprimer \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(29, \"a\", 19);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(30, \"svg\", 15);\n            i0.ɵɵelement(31, \"path\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(32, \" Voir les rendus \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(33, \"div\", 21)(34, \"div\", 22)(35, \"h3\", 23);\n            i0.ɵɵtext(36, \"Statistiques de rendu\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"div\", 8)(38, \"div\")(39, \"div\", 24)(40, \"span\", 25);\n            i0.ɵɵtext(41, \"Progression\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"span\", 26);\n            i0.ɵɵtext(43);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"div\", 27);\n            i0.ɵɵelement(45, \"div\", 28);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"div\", 29)(47, \"div\", 30)(48, \"div\", 31);\n            i0.ɵɵtext(49);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"div\", 32);\n            i0.ɵɵtext(51, \"Rendus\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"div\", 33)(53, \"div\", 34);\n            i0.ɵɵtext(54);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"div\", 32);\n            i0.ɵɵtext(56, \"En attente\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(57, \"div\")(58, \"h4\", 35);\n            i0.ɵɵtext(59, \"Derniers rendus\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"div\", 36);\n            i0.ɵɵtemplate(61, DetailProjectComponent_div_61_Template, 8, 5, \"div\", 37);\n            i0.ɵɵtemplate(62, DetailProjectComponent_div_62_Template, 2, 0, \"div\", 38);\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.projet == null ? null : ctx.projet.titre);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate2(\" Groupe: \", ctx.projet == null ? null : ctx.projet.groupe, \" \\u2022 Date limite: \", (ctx.projet == null ? null : ctx.projet.dateLimite) ? ctx.formatDate(ctx.projet == null ? null : ctx.projet.dateLimite) : \"\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.statut) || \"En cours\", \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.description) || \"Aucune description fournie\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.fichiers) || ctx.projet.fichiers.length === 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(18, _c0, ctx.projet == null ? null : ctx.projet._id));\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(20, _c1))(\"queryParams\", i0.ɵɵpureFunction1(21, _c2, ctx.projet == null ? null : ctx.projet._id));\n            i0.ɵɵadvance(14);\n            i0.ɵɵtextInterpolate2(\"\", (ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0, \"/\", (ctx.projet == null ? null : ctx.projet.totalEtudiants) || 0, \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"width\", ((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0) / ((ctx.projet == null ? null : ctx.projet.totalEtudiants) || 1) * 100, \"%\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(((ctx.projet == null ? null : ctx.projet.totalEtudiants) || 0) - ((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngForOf\", (ctx.projet == null ? null : ctx.projet.derniersRendus) || i0.ɵɵpureFunction0(23, _c3));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.derniersRendus) || ctx.projet.derniersRendus.length === 0);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i1.RouterLink]\n      });\n    }\n  }\n  return DetailProjectComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}