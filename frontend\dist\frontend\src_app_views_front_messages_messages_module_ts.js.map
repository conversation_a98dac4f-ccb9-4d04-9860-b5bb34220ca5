{"version": 3, "file": "src_app_views_front_messages_messages_module_ts.js", "mappings": ";;;;;;;;;;;;;;;AACuC;;AAKjC,MAAOC,YAAY;EAKvBC,YAAA;IAJQ,KAAAC,aAAa,GAAG,IAAIH,iDAAe,CAAU,EAAE,CAAC;IACxD,KAAAI,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;IACnC,KAAAC,SAAS,GAAG,CAAC;EAEL;EAEhBC,IAAIA,CAACC,OAAe,EAAEC,IAAA,GAAiD,MAAM,EAAEC,QAAQ,GAAG,IAAI;IAC5F,MAAMC,EAAE,GAAG,IAAI,CAACL,SAAS,EAAE;IAC3B,MAAMM,KAAK,GAAU;MAAED,EAAE;MAAEF,IAAI;MAAED,OAAO;MAAEE;IAAQ,CAAE;IACpD,MAAMG,aAAa,GAAG,IAAI,CAACV,aAAa,CAACW,KAAK;IAC9C,IAAI,CAACX,aAAa,CAACY,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAED,KAAK,CAAC,CAAC;IAElD,IAAIF,QAAQ,GAAG,CAAC,EAAE;MAChBM,UAAU,CAAC,MAAM,IAAI,CAACC,OAAO,CAACN,EAAE,CAAC,EAAED,QAAQ,CAAC;;EAEhD;EAEAQ,WAAWA,CAACV,OAAe,EAAEE,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACH,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEE,QAAQ,CAAC;EACzC;EAEAS,SAASA,CAACX,OAAe,EAAEE,QAAQ,GAAG,IAAI;IACxC,IAAI,CAACH,IAAI,CAACC,OAAO,EAAE,OAAO,EAAEE,QAAQ,CAAC;EACvC;EAEAU,WAAWA,CAACZ,OAAe,EAAEE,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACH,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEE,QAAQ,CAAC;EACzC;EAEAW,QAAQA,CAACb,OAAe,EAAEE,QAAQ,GAAG,IAAI;IACvC,IAAI,CAACH,IAAI,CAACC,OAAO,EAAE,MAAM,EAAEE,QAAQ,CAAC;EACtC;EAEAO,OAAOA,CAACN,EAAU;IAChB,MAAME,aAAa,GAAG,IAAI,CAACV,aAAa,CAACW,KAAK,CAACQ,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKA,EAAE,CAAC;IACvE,IAAI,CAACR,aAAa,CAACY,IAAI,CAACF,aAAa,CAAC;EACxC;EAEAW,KAAKA,CAAA;IACH,IAAI,CAACrB,aAAa,CAACY,IAAI,CAAC,EAAE,CAAC;EAC7B;;;uBAzCWd,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAwB,OAAA,EAAZxB,YAAY,CAAAyB,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACOgD;AACpB;AAQV;AAEmC;;;;;;;;;;;;;;;;ICMjEO,uDAAA,eAGQ;;;;;IAIVA,4DAAA,cAA4D;IAExDA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,eAA8B;IAC5BA,oDAAA,GAKF;IAAAA,0DAAA,EAAO;;;;IARLA,uDAAA,GACF;IADEA,gEAAA,MAAAO,MAAA,CAAAC,gBAAA,CAAAC,QAAA,MACF;IAEET,uDAAA,GAKF;IALEA,gEAAA,MAAAO,MAAA,CAAAC,gBAAA,CAAAE,QAAA,gBAAAH,MAAA,CAAAI,gBAAA,CAAAJ,MAAA,CAAAC,gBAAA,CAAAI,UAAA,OAKF;;;;;;;;;;IAiBEZ,4DAAA,eAOC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IAPLA,wDAAA,iKAAAc,UAAA,CAAAC,KAAA,CAAAC,KAAA,CAGC;IACDhB,wDAAA,YAAAA,6DAAA,IAAAmB,GAAA,EAAAL,UAAA,CAAAC,KAAA,CAAAK,OAAA,EAAqD;IAErDpB,uDAAA,GACF;IADEA,gEAAA,MAAAc,UAAA,CAAAC,KAAA,CAAAM,KAAA,gBAAAP,UAAA,CAAAC,KAAA,CAAAM,KAAA,MACF;;;;;;;;;IApBJrB,qEAAA,GAAwD;IACtDA,4DAAA,iBAOC;IADCA,wDAAA,mBAAAwB,sEAAA;MAAA,MAAAC,WAAA,GAAAzB,2DAAA,CAAA2B,IAAA;MAAA,MAAAb,UAAA,GAAAW,WAAA,CAAAG,SAAA;MAAA,OAAS5B,yDAAA,CAAAc,UAAA,CAAAgB,OAAA,EAAgB;IAAA,EAAC;IAE1B9B,uDAAA,QAA6B;IAE7BA,wDAAA,IAAAgC,oDAAA,mBASO;IACThC,0DAAA,EAAS;IACXA,mEAAA,EAAe;;;;IApBXA,uDAAA,GAAkD;IAAlDA,wDAAA,6BAAAc,UAAA,CAAAE,KAAA,CAAkD;IAClDhB,wDAAA,YAAAc,UAAA,CAAAoB,WAAA,IAAApB,UAAA,CAAAqB,QAAA,GAAArB,UAAA,CAAAoB,WAAA,GAAAlC,6DAAA,IAAAqC,GAAA,EAEC,UAAAvB,UAAA,CAAAwB,KAAA;IAIEtC,uDAAA,GAAqB;IAArBA,wDAAA,CAAAc,UAAA,CAAAyB,IAAA,CAAqB;IAGrBvC,uDAAA,GAA4C;IAA5CA,wDAAA,SAAAc,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,CAAAM,KAAA,KAA4C;;;;;IA2B/CrB,uDAAA,eAGQ;;;;;;;;;;;IAwBNA,4DAAA,iBASC;IAPCA,wDAAA,mBAAAwC,sEAAA;MAAA,MAAAf,WAAA,GAAAzB,2DAAA,CAAAyC,IAAA;MAAA,MAAAC,UAAA,GAAAjB,WAAA,CAAAG,SAAA;MAAA,MAAAe,OAAA,GAAA3C,2DAAA;MAAS2C,OAAA,CAAAE,gBAAA,CAAAH,UAAA,CAAAI,GAAA,CAA4B;MAAA,OAAE9C,yDAAA,CAAA2C,OAAA,CAAAI,oBAAA,EAAsB;IAAA,EAAC;IAQ9D/C,4DAAA,cAA+B;IAC7BA,uDAAA,QAEK;IACLA,4DAAA,UAAK;IACsBA,oDAAA,GAAkB;IAAAA,0DAAA,EAAM;IACjDA,4DAAA,cAAwD;IACtDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;;IAfVA,wDAAA,aAAAgD,OAAA,CAAAC,gBAAA,CAA6B,YAAAjD,6DAAA,IAAAkD,GAAA,EAAAF,OAAA,CAAAG,iBAAA,KAAAT,UAAA,CAAAI,GAAA;IASzB9C,uDAAA,GAA4D;IAA5DA,wDAAA,CAAA0C,UAAA,CAAAH,IAAA,SAAAG,UAAA,CAAAU,KAAA,mBAA4D;IAGnCpD,uDAAA,GAAkB;IAAlBA,+DAAA,CAAA0C,UAAA,CAAAY,KAAA,CAAkB;IAEzCtD,uDAAA,GACF;IADEA,gEAAA,MAAA0C,UAAA,CAAAa,WAAA,MACF;;;;;;IAtCVvD,4DAAA,cAGC;IAKWA,oDAAA,oBAAa;IAAAA,0DAAA,EAAO;IAC1BA,4DAAA,eAGC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IAIXA,4DAAA,cAAiB;IAEfA,wDAAA,IAAAwD,6CAAA,qBAqBS;IACXxD,0DAAA,EAAM;IAENA,4DAAA,cAAoE;IAEhEA,wDAAA,mBAAAyD,8DAAA;MAAAzD,2DAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAA3D,2DAAA;MAAS2D,OAAA,CAAAC,qBAAA,EAAuB;MAAA,OAAE5D,yDAAA,CAAA2D,OAAA,CAAAZ,oBAAA,EAAsB;IAAA,EAAC;IAGzD/C,4DAAA,eAA+B;IAC7BA,uDAAA,aAEK;IACLA,4DAAA,WAAK;IACsBA,oDAAA,kCAA0B;IAAAA,0DAAA,EAAM;IACzDA,4DAAA,eAAwD;IACtDA,oDAAA,IACF;IAAAA,0DAAA,EAAM;;;;IA9CRA,uDAAA,GAA6C;IAA7CA,wDAAA,YAAA6D,MAAA,CAAAC,cAAA,CAAAD,MAAA,CAAAV,iBAAA,EAA6C;IAE7CnD,uDAAA,GACF;IADEA,gEAAA,MAAA6D,MAAA,CAAAE,aAAA,CAAAF,MAAA,CAAAV,iBAAA,OACF;IAOmBnD,uDAAA,GAAqB;IAArBA,wDAAA,YAAA6D,MAAA,CAAAG,gBAAA,GAAqB;IAmClChE,uDAAA,GACF;IADEA,gEAAA,MAAA6D,MAAA,CAAAI,mBAAA,iBACF;;;;;;IA6BNjE,4DAAA,YAYC;IATCA,wDAAA,mBAAAkE,4DAAA;MAAA,MAAAzC,WAAA,GAAAzB,2DAAA,CAAAmE,IAAA;MAAA,MAAAC,SAAA,GAAA3C,WAAA,CAAAG,SAAA;MAAA,MAAAyC,OAAA,GAAArE,2DAAA;MAAA,OAASA,yDAAA,CAAAqE,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAAtB,GAAA,CAAsB;IAAA,EAAC;IAUhC9C,4DAAA,cAA+B;IAC7BA,uDAAA,cAEO;IACPA,4DAAA,UAAK;IAAAA,oDAAA,GAAiB;IAAAA,0DAAA,EAAM;;;;IAZ9BA,wDAAA,0BAAAoE,SAAA,CAAAG,UAAA,0BAAAH,SAAA,CAAAG,UAAA,SAMC;IAIGvE,uDAAA,GAA8D;IAA9DA,wDAAA,yCAAAoE,SAAA,CAAAhB,KAAA,WAA8D;IAE3DpD,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAoE,SAAA,CAAAd,KAAA,CAAiB;;;;;IA5B9BtD,4DAAA,cAGC;IAIGA,oDAAA,8BACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,cAAiB;IAEfA,wDAAA,IAAAwE,wCAAA,gBAmBI;IACNxE,0DAAA,EAAM;;;;IAnBgBA,uDAAA,GAAoB;IAApBA,wDAAA,YAAAyE,MAAA,CAAAC,eAAA,GAAoB;;;;;;IAkC5C1E,4DAAA,cAGC;IAIGA,oDAAA,gCACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,cAAiB;IAEbA,wDAAA,mBAAA2E,6DAAA;MAAA3E,2DAAA,CAAA4E,IAAA;MAAA,MAAAC,OAAA,GAAA7E,2DAAA;MAAA,OAASA,yDAAA,CAAA6E,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAG7B9E,4DAAA,cAA+B;IAC7BA,uDAAA,YAAyC;IACzCA,4DAAA,UAAK;IAAAA,oDAAA,4BAAqB;IAAAA,0DAAA,EAAM;IAGpCA,4DAAA,iBAGC;IAFCA,wDAAA,mBAAA+E,6DAAA;MAAA/E,2DAAA,CAAA4E,IAAA;MAAA,MAAAI,OAAA,GAAAhF,2DAAA;MAAA,OAASA,yDAAA,CAAAgF,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BjF,4DAAA,eAA+B;IAC7BA,uDAAA,aAA4C;IAC5CA,4DAAA,WAAK;IAAAA,oDAAA,gCAAwB;IAAAA,0DAAA,EAAM;IAGvCA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAkF,8DAAA;MAAAlF,2DAAA,CAAA4E,IAAA;MAAA,MAAAO,OAAA,GAAAnF,2DAAA;MAAA,OAASA,yDAAA,CAAAmF,OAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAGlCpF,4DAAA,eAA+B;IAC7BA,uDAAA,aAA+C;IAC/CA,4DAAA,WAAK;IAAAA,oDAAA,oBAAY;IAAAA,0DAAA,EAAM;IAG3BA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAqF,8DAAA;MAAArF,2DAAA,CAAA4E,IAAA;MAAA,MAAAU,OAAA,GAAAtF,2DAAA;MAAA,OAASA,yDAAA,CAAAsF,OAAA,CAAAC,0BAAA,EAA4B;IAAA,EAAC;IAGtCvF,4DAAA,eAA+B;IAC7BA,uDAAA,aAAuC;IACvCA,4DAAA,WAAK;IAAAA,oDAAA,uBAAU;IAAAA,0DAAA,EAAM;;;;;IAiC7BA,4DAAA,eAGC;IACCA,uDAAA,eAEO;IACTA,0DAAA,EAAM;;;;;;IAGNA,4DAAA,kBAIC;IAFCA,wDAAA,mBAAAwF,sEAAA;MAAAxF,2DAAA,CAAAyF,IAAA;MAAA,MAAAC,OAAA,GAAA1F,2DAAA;MAAA,OAASA,yDAAA,CAAA0F,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvB3F,uDAAA,aAAoC;IACtCA,0DAAA,EAAS;;;;;;IAuBTA,4DAAA,kBAIC;IAFCA,wDAAA,mBAAA4F,6EAAA;MAAA,MAAAnE,WAAA,GAAAzB,2DAAA,CAAA6F,IAAA;MAAA,MAAAC,UAAA,GAAArE,WAAA,CAAAG,SAAA;MAAA,MAAAmE,OAAA,GAAA/F,2DAAA;MAAA,OAASA,yDAAA,CAAA8F,UAAA,CAAArH,EAAA,IAAasH,OAAA,CAAAC,iBAAA,CAAAF,UAAA,CAAArH,EAAA,CAA4B;IAAA,EAAC;IAGnDuB,4DAAA,eAAwC;IAIpCA,uDAAA,aAEK;IACPA,0DAAA,EAAM;IACNA,4DAAA,eAA4B;IAExBA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,uDAAA,eAKO;IACTA,0DAAA,EAAM;;;;;IARFA,uDAAA,GACF;IADEA,gEAAA,MAAAiG,OAAA,CAAAC,iBAAA,CAAAJ,UAAA,CAAAK,SAAA,OACF;IAGEnG,uDAAA,GAEC;IAFDA,wDAAA,cAAAiG,OAAA,CAAAG,oBAAA,CAAAN,UAAA,CAAAO,OAAA,QAAAJ,OAAA,CAAAK,WAAA,GAAAtG,4DAAA,CAEC;;;;;IA/BbA,4DAAA,eAGC;IAIGA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAAsC;IACpCA,wDAAA,IAAAwG,oDAAA,sBAyBS;IACXxG,0DAAA,EAAM;;;;IA7BJA,uDAAA,GACF;IADEA,gEAAA,MAAAyG,OAAA,CAAAC,aAAA,CAAAC,MAAA,sCACF;IAGuB3G,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAyG,OAAA,CAAAC,aAAA,CAAgB;;;;;IA6BzC1G,4DAAA,eAQC;IACCA,uDAAA,aAEK;IACLA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,uCAAA4G,OAAA,CAAAN,WAAA,QACF;;;;;;IAzGFtG,4DAAA,cAGC;IAIKA,uDAAA,YAAqC;IACvCA,0DAAA,EAAM;IAGNA,4DAAA,cAA6B;IAGzBA,wDAAA,2BAAA6G,oEAAAC,MAAA;MAAA9G,2DAAA,CAAA+G,IAAA;MAAA,MAAAC,OAAA,GAAAhH,2DAAA;MAAA,OAAAA,yDAAA,CAAAgH,OAAA,CAAAV,WAAA,GAAAQ,MAAA;IAAA,EAAyB,mBAAAG,4DAAAH,MAAA;MAAA9G,2DAAA,CAAA+G,IAAA;MAAA,MAAAG,OAAA,GAAAlH,2DAAA;MAAA,OAChBA,yDAAA,CAAAkH,OAAA,CAAAC,aAAA,CAAAL,MAAA,CAAqB;IAAA,EADL,qBAAAM,8DAAAN,MAAA;MAAA9G,2DAAA,CAAA+G,IAAA;MAAA,MAAAM,OAAA,GAAArH,2DAAA;MAAA,OAEdA,yDAAA,CAAAqH,OAAA,CAAAC,gBAAA,CAAAR,MAAA,CAAwB;IAAA,EAFV;IAF3B9G,0DAAA,EAQE;IAGFA,wDAAA,IAAAuH,0CAAA,kBAOM;IAGNvH,wDAAA,IAAAwH,6CAAA,qBAMS;IACXxH,0DAAA,EAAM;IAGNA,4DAAA,iBAGC;IAFCA,wDAAA,mBAAAyH,6DAAA;MAAAzH,2DAAA,CAAA+G,IAAA;MAAA,MAAAW,OAAA,GAAA1H,2DAAA;MAAA,OAASA,yDAAA,CAAA0H,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAG3B3H,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;IAIXA,wDAAA,KAAA4H,2CAAA,mBAqCM;IAGN5H,wDAAA,KAAA6H,2CAAA,mBAaM;IACR7H,0DAAA,EAAM;;;;IA5FEA,uDAAA,GAAyB;IAAzBA,wDAAA,YAAA8H,MAAA,CAAAxB,WAAA,CAAyB;IAUxBtG,uDAAA,GAAiB;IAAjBA,wDAAA,SAAA8H,MAAA,CAAAC,WAAA,CAAiB;IAUjB/H,uDAAA,GAAiC;IAAjCA,wDAAA,SAAA8H,MAAA,CAAAxB,WAAA,KAAAwB,MAAA,CAAAC,WAAA,CAAiC;IAmBrC/H,uDAAA,GAA4C;IAA5CA,wDAAA,SAAA8H,MAAA,CAAAE,UAAA,IAAAF,MAAA,CAAApB,aAAA,CAAAC,MAAA,KAA4C;IAwC5C3G,uDAAA,GAMP;IANOA,wDAAA,SAAA8H,MAAA,CAAAE,UAAA,IAAAF,MAAA,CAAApB,aAAA,CAAAC,MAAA,WAAAmB,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAxB,WAAA,CAAAK,MAAA,MAMP;;;;;IAmCM3G,4DAAA,eAAiE;IAE7DA,uDAAA,aAA+D;IAC/DA,4DAAA,eAAqB;IAAAA,oDAAA,sCAAqB;IAAAA,0DAAA,EAAM;;;;;IA8C1CA,4DAAA,WAAoC;IAAAA,oDAAA,GAElC;IAAAA,0DAAA,EAAO;;;;IAF2BA,uDAAA,GAElC;IAFkCA,+DAAA,CAAAiI,iBAAA,CAAA5B,OAAA,CAElC;;;;;IACFrG,4DAAA,gBAGC;IACCA,uDAAA,aAAiC;IACjCA,oDAAA,cACF;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,gBAGC;IACCA,uDAAA,aAAsC;IACtCA,oDAAA,sBACF;IAAAA,0DAAA,EAAO;;;;;;IArDfA,4DAAA,kBAIC;IAFCA,wDAAA,mBAAAkI,6EAAA;MAAA,MAAAzG,WAAA,GAAAzB,2DAAA,CAAAmI,IAAA;MAAA,MAAAF,iBAAA,GAAAxG,WAAA,CAAAG,SAAA;MAAA,MAAAwG,OAAA,GAAApI,2DAAA;MAAA,OAASA,yDAAA,CAAAoI,OAAA,CAAAC,qBAAA,CAAAJ,iBAAA,CAAAxJ,EAAA,CAAwC;IAAA,EAAC;IAGlDuB,4DAAA,eAAwC;IAGpCA,uDAAA,eAQE;IACJA,0DAAA,EAAM;IAGNA,4DAAA,eAA4B;IAMtBA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,gBAA+D;IAC7DA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IAITA,4DAAA,gBAEC;IACCA,wDAAA,KAAAsI,4DAAA,oBAES;IACTtI,wDAAA,KAAAuI,4DAAA,oBAMO;IACPvI,wDAAA,KAAAwI,4DAAA,oBAMO;IACTxI,0DAAA,EAAM;IAGNA,4DAAA,gBAAoC;IAClCA,uDAAA,cAEK;IACLA,4DAAA,iBAAyD;IACvDA,oDAAA,IAMF;IAAAA,0DAAA,EAAO;IAKXA,4DAAA,gBAAoE;IAClEA,uDAAA,cAA4C;IAC9CA,0DAAA,EAAM;;;;;IAlEFA,uDAAA,GAGC;IAHDA,wDAAA,SAAAiI,iBAAA,CAAAQ,MAAA,kBAAAR,iBAAA,CAAAQ,MAAA,CAAAC,KAAA,yCAAA1I,2DAAA,CAGC,SAAAiI,iBAAA,CAAAQ,MAAA,kBAAAR,iBAAA,CAAAQ,MAAA,CAAAhI,QAAA;IAcCT,uDAAA,GACF;IADEA,gEAAA,OAAAiI,iBAAA,CAAAQ,MAAA,kBAAAR,iBAAA,CAAAQ,MAAA,CAAAhI,QAAA,gCACF;IAEET,uDAAA,GACF;IADEA,gEAAA,MAAA4I,OAAA,CAAA1C,iBAAA,CAAA+B,iBAAA,CAAA9B,SAAA,OACF;IAOOnG,uDAAA,GAA2B;IAA3BA,wDAAA,SAAAiI,iBAAA,CAAA5B,OAAA,CAA2B;IAI/BrG,uDAAA,GAA6B;IAA7BA,wDAAA,SAAA4I,OAAA,CAAAC,QAAA,CAAAZ,iBAAA,EAA6B;IAO7BjI,uDAAA,GAAmC;IAAnCA,wDAAA,SAAA4I,OAAA,CAAAE,cAAA,CAAAb,iBAAA,EAAmC;IAcpCjI,uDAAA,GAMF;IANEA,gEAAA,wBAAAiI,iBAAA,CAAAc,QAAA,WAAAH,OAAA,CAAAI,iBAAA,CAAAf,iBAAA,CAAAc,QAAA,YAMF;;;;;IAxEV/I,4DAAA,eAGC;IACCA,wDAAA,IAAAiJ,oDAAA,uBA6ES;IACXjJ,0DAAA,EAAM;;;;IA7EwBA,uDAAA,GAAiB;IAAjBA,wDAAA,YAAAkJ,OAAA,CAAAC,cAAA,CAAiB;;;;;;IAtCnDnJ,4DAAA,eAGC;IAMKA,uDAAA,aAAmE;IACnEA,4DAAA,cAAmE;IACjEA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IAEPA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAoJ,6DAAA;MAAApJ,2DAAA,CAAAqJ,IAAA;MAAA,MAAAC,OAAA,GAAAtJ,2DAAA;MAAA,OAASA,yDAAA,CAAAsJ,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCvJ,uDAAA,aAAoC;IACtCA,0DAAA,EAAS;IAIXA,4DAAA,eAAsC;IAEpCA,wDAAA,IAAAwJ,0CAAA,mBAKM;IAGNxJ,wDAAA,KAAAyJ,2CAAA,mBAkFM;IACRzJ,0DAAA,EAAM;;;;IAzGAA,uDAAA,GACF;IADEA,gEAAA,mCAAA0J,MAAA,CAAAC,sBAAA,SACF;IAaI3J,uDAAA,GAAiC;IAAjCA,wDAAA,SAAA0J,MAAA,CAAAP,cAAA,CAAAxC,MAAA,OAAiC;IASpC3G,uDAAA,GAA+B;IAA/BA,wDAAA,SAAA0J,MAAA,CAAAP,cAAA,CAAAxC,MAAA,KAA+B;;;;;IA6FpC3G,4DAAA,eAAqE;IAG/DA,uDAAA,eAEO;IAKTA,0DAAA,EAAM;IACNA,4DAAA,eAEC;IACCA,oDAAA,sCACF;IAAAA,0DAAA,EAAM;;;;;IAKVA,4DAAA,eAGC;IAKKA,uDAAA,eAEO;IASTA,0DAAA,EAAM;IACNA,4DAAA,cAAwD;IACtDA,oDAAA,2BACF;IAAAA,0DAAA,EAAM;;;;;IAKVA,4DAAA,eAGC;IAEGA,uDAAA,eAEO;IACPA,4DAAA,eAEC;IACCA,oDAAA,kCACF;IAAAA,0DAAA,EAAM;IACNA,uDAAA,eAEO;IACTA,0DAAA,EAAM;;;;;IAIRA,4DAAA,eAGC;IAGKA,uDAAA,aAA2C;IAK7CA,0DAAA,EAAM;IACNA,4DAAA,UAAK;IAEDA,oDAAA,4CACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,aAAsD;IAAAA,oDAAA,GAAW;IAAAA,0DAAA,EAAI;;;;IAAfA,uDAAA,GAAW;IAAXA,+DAAA,CAAA4J,OAAA,CAAAC,KAAA,CAAW;;;;;IAanE7J,4DAAA,eAAuE;IACrEA,uDAAA,eAAwC;IACxCA,4DAAA,eAAkC;IAChCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,uDAAA,eAAwC;IAC1CA,0DAAA,EAAM;;;;;IAHFA,uDAAA,GACF;IADEA,gEAAA,MAAA8J,OAAA,CAAAd,iBAAA,CAAAe,WAAA,kBAAAA,WAAA,CAAA5D,SAAA,OACF;;;;;;IAoBAnG,4DAAA,kBAKC;IAHCA,wDAAA,mBAAAgK,qFAAA;MAAAhK,2DAAA,CAAAiK,IAAA;MAAA,MAAAF,WAAA,GAAA/J,2DAAA,GAAA4B,SAAA;MAAA,MAAAsI,OAAA,GAAAlK,2DAAA;MAAA,OAASA,yDAAA,CAAAkK,OAAA,CAAAC,oBAAA,CAAAJ,WAAA,CAAAtL,EAAA,CAAgC;IAAA,EAAC;IAI1CuB,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;;;IAQLA,4DAAA,kBAKC;IAHCA,wDAAA,mBAAAoK,2FAAA;MAAA,MAAA3I,WAAA,GAAAzB,2DAAA,CAAAqK,KAAA;MAAA,MAAAC,UAAA,GAAA7I,WAAA,CAAAG,SAAA;MAAA,MAAAmI,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAA2I,QAAA,GAAAvK,2DAAA;MAAA,OAASA,yDAAA,CAAAuK,QAAA,CAAAC,cAAA,CAAAT,WAAA,CAAAtL,EAAA,EAAA6L,UAAA,CAAiC;IAAA,EAAC;IAI3CtK,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IAHPA,wDAAA,gCAAAsK,UAAA,CAAgC;IAEhCtK,uDAAA,GACF;IADEA,gEAAA,MAAAsK,UAAA,MACF;;;;;IAZJtK,4DAAA,eAGC;IAEGA,wDAAA,IAAAyK,kEAAA,sBAOS;IACXzK,0DAAA,EAAM;;;;IAPgBA,uDAAA,GAAqB;IAArBA,wDAAA,YAAA0K,OAAA,CAAAC,kBAAA,CAAqB;;;;;IAU7C3K,4DAAA,eASC;IACCA,uDAAA,eAME;IACJA,0DAAA,EAAM;;;;IANFA,uDAAA,GAEC;IAFDA,wDAAA,SAAA+J,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAAC,KAAA,yCAAA1I,2DAAA,CAEC;;;;;;IA4BDA,4DAAA,kBAMC;IAFCA,wDAAA,mBAAA4K,2FAAA;MAAA5K,2DAAA,CAAA6K,KAAA;MAAA,MAAAd,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAAkJ,QAAA,GAAA9K,2DAAA;MAAA,OAASA,yDAAA,CAAA8K,QAAA,CAAAC,oBAAA,CAAAhB,WAAA,CAAAtL,EAAA,CAAgC;IAAA,EAAC;IAG1CuB,uDAAA,aAAyC;IAC3CA,0DAAA,EAAS;;;;;;IA4BPA,4DAAA,kBAIC;IAFCA,wDAAA,mBAAAgL,kGAAA;MAAAhL,2DAAA,CAAAiL,KAAA;MAAA,MAAAlB,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAAsJ,QAAA,GAAAlL,2DAAA;MAAA,OAASA,yDAAA,CAAAkL,QAAA,CAAAC,gBAAA,CAAApB,WAAA,CAAyB;IAAA,EAAC;IAGnC/J,uDAAA,aAAmC;IACnCA,4DAAA,WAAM;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAO;;;;;;IAEvBA,4DAAA,kBAIC;IAFCA,wDAAA,mBAAAoL,kGAAA;MAAApL,2DAAA,CAAAqL,KAAA;MAAA,MAAAtB,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAA0J,QAAA,GAAAtL,2DAAA;MAAA,OAASA,yDAAA,CAAAsL,QAAA,CAAAC,sBAAA,CAAAxB,WAAA,CAAAtL,EAAA,CAAkC;IAAA,EAAC;IAG5CuB,uDAAA,aAAoC;IACpCA,4DAAA,WAAM;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAO;;;;;;IAvC1BA,4DAAA,eAGC;IAEGA,wDAAA,mBAAAwL,wFAAA;MAAAxL,2DAAA,CAAAyL,KAAA;MAAA,MAAA1B,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAA8J,QAAA,GAAA1L,2DAAA;MAAA,OAASA,yDAAA,CAAA0L,QAAA,CAAAC,mBAAA,CAAA5B,WAAA,CAA4B;IAAA,EAAC;IAGtC/J,uDAAA,aAAoC;IACpCA,4DAAA,WAAM;IAAAA,oDAAA,oBAAQ;IAAAA,0DAAA,EAAO;IAEvBA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAA4L,wFAAA;MAAA5L,2DAAA,CAAAyL,KAAA;MAAA,MAAA1B,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAAiK,QAAA,GAAA7L,2DAAA;MAAA,OAASA,yDAAA,CAAA6L,QAAA,CAAAC,gBAAA,CAAA/B,WAAA,CAAyB;IAAA,EAAC;IAGnC/J,uDAAA,aAAoC;IACpCA,4DAAA,WAAM;IAAAA,oDAAA,sBAAU;IAAAA,0DAAA,EAAO;IAEzBA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAA+L,wFAAA;MAAA/L,2DAAA,CAAAyL,KAAA;MAAA,MAAA1B,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAAoK,QAAA,GAAAhM,2DAAA;MAAA,OAASA,yDAAA,CAAAgM,QAAA,CAAAC,mBAAA,CAAAlC,WAAA,CAAAtL,EAAA,CAA+B;IAAA,EAAC;IAGzCuB,uDAAA,cAAqD;IACrDA,4DAAA,YAAM;IAAAA,oDAAA,IAAgC;IAAAA,0DAAA,EAAO;IAE/CA,wDAAA,KAAAkM,yEAAA,sBAOS;IACTlM,wDAAA,KAAAmM,yEAAA,sBAOS;IACXnM,0DAAA,EAAM;;;;;IAnBCA,uDAAA,IAA6B;IAA7BA,wDAAA,CAAAoM,QAAA,CAAAC,UAAA,CAAAtC,WAAA,EAA6B;IAC1B/J,uDAAA,GAAgC;IAAhCA,+DAAA,CAAAoM,QAAA,CAAAE,iBAAA,CAAAvC,WAAA,EAAgC;IAGrC/J,uDAAA,GAA6B;IAA7BA,wDAAA,SAAAoM,QAAA,CAAAG,cAAA,CAAAxC,WAAA,EAA6B;IAQ7B/J,uDAAA,GAA6B;IAA7BA,wDAAA,SAAAoM,QAAA,CAAAG,cAAA,CAAAxC,WAAA,EAA6B;;;;;;IAUlC/J,4DAAA,eAGC;IAEGA,oDAAA,+BACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAA4B;IAExBA,wDAAA,mBAAAwM,wFAAA;MAAAxM,2DAAA,CAAAyM,KAAA;MAAA,MAAA1C,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAA8K,QAAA,GAAA1M,2DAAA;MAAA,OAASA,yDAAA,CAAA0M,QAAA,CAAAC,mBAAA,CAAA5C,WAAA,CAAAtL,EAAA,CAA+B;IAAA,EAAC;IAGzCuB,oDAAA,gBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAA4M,wFAAA;MAAA5M,2DAAA,CAAAyM,KAAA;MAAA,MAAA1C,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAAiL,QAAA,GAAA7M,2DAAA;MAAA,OAASA,yDAAA,CAAA6M,QAAA,CAAAC,oBAAA,CAAA/C,WAAA,CAAAtL,EAAA,CAAgC;IAAA,EAAC;IAG1CuB,oDAAA,kBACF;IAAAA,0DAAA,EAAS;;;;;IA4BPA,uDAAA,aAGK;;;;;;IA1BXA,4DAAA,eAGC;IAEGA,oDAAA,GAKF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAA4B;IAExBA,wDAAA,mBAAA+M,wFAAA;MAAA/M,2DAAA,CAAAgN,KAAA;MAAA,MAAAjD,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAAqL,QAAA,GAAAjN,2DAAA;MAAA,OAASA,yDAAA,CAAAiN,QAAA,CAAAC,qBAAA,CAAAnD,WAAA,CAAAtL,EAAA,CAAiC;IAAA,EAAC;IAG3CuB,oDAAA,gBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAIC;IAHCA,wDAAA,mBAAAmN,wFAAA;MAAAnN,2DAAA,CAAAgN,KAAA;MAAA,MAAAjD,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAAwL,QAAA,GAAApN,2DAAA;MAAA,OAASA,yDAAA,CAAAoN,QAAA,CAAAC,gBAAA,CAAAtD,WAAA,CAAyB;IAAA,EAAC;IAInC/J,wDAAA,IAAAsN,mEAAA,iBAGK;IACLtN,4DAAA,WAAM;IAAAA,oDAAA,GAEJ;IAAAA,0DAAA,EAAO;;;;;IAxBXA,uDAAA,GAKF;IALEA,gEAAA,MAAAuN,QAAA,CAAAC,eAAA,CAAAzD,WAAA,6EAKF;IAUI/J,uDAAA,GAAkC;IAAlCA,wDAAA,aAAAuN,QAAA,CAAAE,SAAA,CAAA1D,WAAA,CAAAtL,EAAA,EAAkC;IAI/BuB,uDAAA,GAA2B;IAA3BA,wDAAA,SAAAuN,QAAA,CAAAE,SAAA,CAAA1D,WAAA,CAAAtL,EAAA,EAA2B;IAGxBuB,uDAAA,GAEJ;IAFIA,+DAAA,CAAAuN,QAAA,CAAAC,eAAA,CAAAzD,WAAA,8CAEJ;;;;;IAWN/J,4DAAA,eAGC;IACCA,uDAAA,aAEK;IACLA,4DAAA,gBAEG;IAAAA,oDAAA,wBAAO;IAAAA,0DAAA,EACT;;;;;IAIHA,4DAAA,eAGC;IAIGA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAA6D;IAC3DA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IAJJA,uDAAA,GACF;IADEA,gEAAA,OAAA+J,WAAA,CAAA2D,OAAA,CAAAjF,MAAA,kBAAAsB,WAAA,CAAA2D,OAAA,CAAAjF,MAAA,CAAAhI,QAAA,wBACF;IAEET,uDAAA,GACF;IADEA,gEAAA,MAAA+J,WAAA,CAAA2D,OAAA,CAAArH,OAAA,mBACF;;;;;IAGFrG,4DAAA,WAAiC;IAAAA,oDAAA,GAAqB;IAAAA,0DAAA,EAAO;;;;IAA5BA,uDAAA,GAAqB;IAArBA,+DAAA,CAAA+J,WAAA,CAAA1D,OAAA,CAAqB;;;;;IACtDrG,4DAAA,gBAGC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAA+J,WAAA,CAAA1D,OAAA,MACF;;;;;IACArG,4DAAA,gBAGC;IACCA,oDAAA,uBACF;IAAAA,0DAAA,EAAO;;;;;IA7CTA,4DAAA,eAGC;IAECA,wDAAA,IAAA2N,qEAAA,mBAWM;IAGN3N,wDAAA,IAAA4N,qEAAA,mBAYM;IAEN5N,wDAAA,IAAA6N,sEAAA,oBAA6D;IAC7D7N,wDAAA,IAAA8N,sEAAA,oBAKO;IACP9N,wDAAA,IAAA+N,sEAAA,oBAKO;IACT/N,0DAAA,EAAM;;;;;IAxCDA,uDAAA,GAA8B;IAA9BA,wDAAA,SAAAgO,QAAA,CAAAR,eAAA,CAAAzD,WAAA,EAA8B;IAc9B/J,uDAAA,GAAqB;IAArBA,wDAAA,SAAA+J,WAAA,CAAA2D,OAAA,CAAqB;IAajB1N,uDAAA,GAAwB;IAAxBA,wDAAA,UAAA+J,WAAA,CAAAkE,SAAA,CAAwB;IAE5BjO,uDAAA,GAAuB;IAAvBA,wDAAA,SAAA+J,WAAA,CAAAkE,SAAA,CAAuB;IAMvBjO,uDAAA,GAA4C;IAA5CA,wDAAA,SAAA+J,WAAA,CAAAmE,QAAA,KAAAnE,WAAA,CAAAkE,SAAA,CAA4C;;;;;;IAQjDjO,4DAAA,eAGC;IAEGA,wDAAA,2BAAAmO,kGAAArH,MAAA;MAAA9G,2DAAA,CAAAoO,KAAA;MAAA,MAAAC,QAAA,GAAArO,2DAAA;MAAA,OAAAA,yDAAA,CAAAqO,QAAA,CAAAC,cAAA,GAAAxH,MAAA;IAAA,EAA4B,qBAAAyH,4FAAAzH,MAAA;MAAA9G,2DAAA,CAAAoO,KAAA;MAAA,MAAArE,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAA4M,QAAA,GAAAxO,2DAAA;MAAA,OACjBA,yDAAA,CAAAwO,QAAA,CAAAC,cAAA,CAAA3H,MAAA,EAAAiD,WAAA,CAAAtL,EAAA,CAAkC;IAAA,EADjB;IAM7BuB,0DAAA,EAAW;IACZA,4DAAA,eAA6C;IAEzCA,wDAAA,mBAAA0O,wFAAA;MAAA1O,2DAAA,CAAAoO,KAAA;MAAA,MAAAO,QAAA,GAAA3O,2DAAA;MAAA,OAASA,yDAAA,CAAA2O,QAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAG7B5O,oDAAA,gBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAA6O,wFAAA;MAAA7O,2DAAA,CAAAoO,KAAA;MAAA,MAAArE,WAAA,GAAA/J,2DAAA,IAAA4B,SAAA;MAAA,MAAAkN,QAAA,GAAA9O,2DAAA;MAAA,OAASA,yDAAA,CAAA8O,QAAA,CAAAC,eAAA,CAAAhF,WAAA,CAAAtL,EAAA,CAA2B;IAAA,EAAC;IAGrCuB,oDAAA,oBACF;IAAAA,0DAAA,EAAS;;;;IAnBTA,uDAAA,GAA4B;IAA5BA,wDAAA,YAAAgP,QAAA,CAAAV,cAAA,CAA4B;;;;;IAwBhCtO,gEAAA,GAKgB;;;;;IAGhBA,gEAAA,GAKgB;;;;;;;;;;;;;;;;;;;IAhOlBA,4DAAA,eAiBC;IAFCA,wDAAA,wBAAAkP,oFAAA;MAAAlP,2DAAA,CAAAmP,KAAA;MAAA,MAAApF,WAAA,GAAA/J,2DAAA,GAAA4B,SAAA;MAAA,MAAAwN,QAAA,GAAApP,2DAAA;MAAA,OAAcA,yDAAA,CAAA+J,WAAA,CAAAtL,EAAA,IAAc2Q,QAAA,CAAA7C,cAAA,CAAAxC,WAAA,CAAuB,GAAG,IAAI,GAAG,IAAI;IAAA,EAAC,wBAAAsF,oFAAA;MAAArP,2DAAA,CAAAmP,KAAA;MAAA,MAAApF,WAAA,GAAA/J,2DAAA,GAAA4B,SAAA;MAAA,MAAA0N,QAAA,GAAAtP,2DAAA;MAAA,OACpDA,yDAAA,CAAA+J,WAAA,CAAAtL,EAAA,IAAc6Q,QAAA,CAAA/C,cAAA,CAAAxC,WAAA,CAAuB,GAAG,IAAI,GAAG,IAAI;IAAA,EADC;IAIlE/J,wDAAA,IAAAuP,kEAAA,sBAQS;IAGTvP,wDAAA,IAAAwP,+DAAA,oBAyCM;IAGNxP,wDAAA,IAAAyP,+DAAA,mBAqBM;IAGNzP,wDAAA,IAAA0P,+DAAA,oBAgCM;IAGN1P,wDAAA,IAAA2P,+DAAA,mBA8CM;IAGN3P,wDAAA,IAAA4P,+DAAA,mBA0BM;IAGN5P,wDAAA,IAAA6P,wEAAA,4BAKgB;IAGhB7P,wDAAA,IAAA8P,wEAAA,4BAKgB;IAClB9P,0DAAA,EAAM;;;;;;;IA1NJA,wDAAA,YAAAA,6DAAA,KAAAgQ,GAAA,EAAAjG,WAAA,CAAAkG,SAAA,EAAAlG,WAAA,CAAAkG,SAAA,KAAAlG,WAAA,CAAAmG,OAAA,EAAAnG,WAAA,CAAAmG,OAAA,EAAAC,OAAA,CAAA3C,eAAA,CAAAzD,WAAA,GAME,oBAAAA,WAAA,CAAAtL,EAAA;IAOCuB,uDAAA,GAGjB;IAHiBA,wDAAA,SAAA+J,WAAA,CAAAtL,EAAA,IAAA0R,OAAA,CAAA5D,cAAA,CAAAxC,WAAA,MAAAA,WAAA,CAAAkE,SAAA,CAGjB;IAQiBjO,uDAAA,GAAkD;IAAlDA,wDAAA,SAAA+J,WAAA,CAAAtL,EAAA,IAAA0R,OAAA,CAAAC,kBAAA,CAAArG,WAAA,CAAAtL,EAAA,EAAkD;IA4ClDuB,uDAAA,GAAiD;IAAjDA,wDAAA,SAAA+J,WAAA,CAAAtL,EAAA,IAAA0R,OAAA,CAAAE,iBAAA,CAAAtG,WAAA,CAAAtL,EAAA,EAAiD;IAwBjDuB,uDAAA,GAA8C;IAA9CA,wDAAA,SAAA+J,WAAA,CAAAtL,EAAA,IAAA0R,OAAA,CAAAG,cAAA,CAAAvG,WAAA,CAAAtL,EAAA,EAA8C;IAmC9CuB,uDAAA,GAAqC;IAArCA,wDAAA,SAAAmQ,OAAA,CAAAI,gBAAA,KAAAxG,WAAA,CAAAtL,EAAA,CAAqC;IAiDrCuB,uDAAA,GAAqC;IAArCA,wDAAA,SAAAmQ,OAAA,CAAAI,gBAAA,KAAAxG,WAAA,CAAAtL,EAAA,CAAqC;IA6BrCuB,uDAAA,GAEoB;IAFpBA,wDAAA,qBAAAwQ,IAAA,CAEoB,4BAAAxQ,6DAAA,KAAAyQ,GAAA,EAAA1G,WAAA;IAMpB/J,uDAAA,GAEoB;IAFpBA,wDAAA,qBAAA0Q,IAAA,CAEoB,4BAAA1Q,6DAAA,KAAAyQ,GAAA,EAAA1G,WAAA;;;;;IA8BrB/J,uDAAA,eASO;;;;;IADLA,yDAAA,WAAA4Q,QAAA,CAAAC,iBAAA,CAAAC,MAAA,QAAwC;;;;;IAiB5C9Q,gEAAA,GAKgB;;;;;IAGhBA,gEAAA,GAKgB;;;;;;;;;;;;;;;;IA9DlBA,4DAAA,eAgBC;IAGGA,uDAAA,aAA2B;IAC7BA,0DAAA,EAAS;IAGTA,4DAAA,eAAmC;IACjCA,wDAAA,IAAA+Q,+DAAA,mBASO;IACT/Q,0DAAA,EAAM;IAGNA,4DAAA,eAAmC;IACjCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IAGNA,uDAAA,oCAI4B;IAG5BA,wDAAA,IAAAgR,wEAAA,4BAKgB;IAGhBhR,wDAAA,IAAAiR,wEAAA,4BAKgB;IAClBjR,0DAAA,EAAM;;;;;;;IA5DJA,wDAAA,YAAAA,6DAAA,IAAAkR,GAAA,GAAAnH,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAAhK,EAAA,MAAA0S,OAAA,CAAAC,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAA4I,GAAA,MAAAF,OAAA,CAAAC,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAuH,QAAA,MAAAH,OAAA,CAAAC,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAAhK,EAAA,MAAA0S,OAAA,CAAAC,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAA4I,GAAA,MAAAF,OAAA,CAAAC,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAuH,QAAA,MAAAH,OAAA,CAAAC,aAAA,GAAArH,WAAA,CAAAkG,SAAA,EAAAlG,WAAA,CAAAmG,OAAA,EAYE;IAYAlQ,uDAAA,GAIhB;IAJgBA,wDAAA,YAAAA,6DAAA,KAAAuR,GAAA,EAIhB;IAOgBvR,uDAAA,GACF;IADEA,gEAAA,MAAAmR,OAAA,CAAAK,mBAAA,CAAAL,OAAA,CAAAM,uBAAA,CAAA1H,WAAA,QACF;IAIE/J,uDAAA,GAAwC;IAAxCA,wDAAA,aAAAmR,OAAA,CAAAO,kBAAA,CAAA3H,WAAA,EAAwC,aAAAoH,OAAA,CAAAM,uBAAA,CAAA1H,WAAA;IAOvC/J,uDAAA,GAEoB;IAFpBA,wDAAA,qBAAAwQ,IAAA,CAEoB,4BAAAxQ,6DAAA,KAAAyQ,GAAA,EAAA1G,WAAA;IAMpB/J,uDAAA,GAEoB;IAFpBA,wDAAA,qBAAA0Q,IAAA,CAEoB,4BAAA1Q,6DAAA,KAAAyQ,GAAA,EAAA1G,WAAA;;;;;IAkCvB/J,gEAAA,GAKgB;;;;;IAGhBA,gEAAA,GAKgB;;;;;;;;;;;;IAzClBA,4DAAA,eASC;IAOKA,uDAAA,eAIE;IACJA,0DAAA,EAAI;IACJA,4DAAA,eAAsC;IACpCA,uDAAA,aAA6B;IAC/BA,0DAAA,EAAM;IAIRA,wDAAA,IAAA2R,wEAAA,4BAKgB;IAGhB3R,wDAAA,IAAA4R,wEAAA,4BAKgB;IAClB5R,0DAAA,EAAM;;;;;;;IAvCJA,wDAAA,YAAAA,6DAAA,IAAA8R,GAAA,EAAA/H,WAAA,CAAAkG,SAAA,EAAAlG,WAAA,CAAAkG,SAAA,KAAAlG,WAAA,CAAAmG,OAAA,EAAAnG,WAAA,CAAAmG,OAAA,EAKE;IAIElQ,uDAAA,GAA6B;IAA7BA,wDAAA,SAAA+R,OAAA,CAAAC,WAAA,CAAAjI,WAAA,GAAA/J,2DAAA,CAA6B;IAK3BA,uDAAA,GAA4B;IAA5BA,wDAAA,QAAA+R,OAAA,CAAAC,WAAA,CAAAjI,WAAA,GAAA/J,2DAAA,CAA4B;IAY/BA,uDAAA,GAEoB;IAFpBA,wDAAA,qBAAAwQ,IAAA,CAEoB,4BAAAxQ,6DAAA,KAAAyQ,GAAA,EAAA1G,WAAA;IAMpB/J,uDAAA,GAEoB;IAFpBA,wDAAA,qBAAA0Q,IAAA,CAEoB,4BAAA1Q,6DAAA,KAAAyQ,GAAA,EAAA1G,WAAA;;;;;;;;;;;IA3Z/B/J,4DAAA,eAIC;IAECA,wDAAA,IAAAiS,yDAAA,mBAMM;IAGNjS,4DAAA,eAaC;IAECA,wDAAA,IAAAkS,4DAAA,sBAOS;IAGTlS,wDAAA,IAAAmS,yDAAA,mBAcM;IAENnS,wDAAA,IAAAoS,yDAAA,mBAiBM;IAGNpS,4DAAA,eAAwC;IAEtCA,wDAAA,IAAAqS,yDAAA,oBAiOM;IAGNrS,wDAAA,IAAAsS,yDAAA,qBA+DM;IAGNtS,wDAAA,IAAAuS,yDAAA,oBA0CM;IACRvS,0DAAA,EAAM;;;;;;IA5ZRA,yDAAA,oBAAA+J,WAAA,CAAAtL,EAAA,CAAmC;IAG7BuB,uDAAA,GAA6B;IAA7BA,wDAAA,SAAAyS,OAAA,CAAAC,oBAAA,CAAAC,KAAA,EAA6B;IAWjC3S,uDAAA,GAUE;IAVFA,wDAAA,YAAAA,6DAAA,IAAA6S,IAAA,GAAA9I,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAAhK,EAAA,MAAAgU,OAAA,CAAArB,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAA4I,GAAA,MAAAoB,OAAA,CAAArB,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAuH,QAAA,MAAAmB,OAAA,CAAArB,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAAhK,EAAA,MAAAgU,OAAA,CAAArB,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAA4I,GAAA,MAAAoB,OAAA,CAAArB,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAuH,QAAA,MAAAmB,OAAA,CAAArB,aAAA,GAUE;IAICpR,uDAAA,GAAsC;IAAtCA,wDAAA,SAAA+J,WAAA,CAAAtL,EAAA,KAAAsL,WAAA,CAAAkE,SAAA,CAAsC;IAUtCjO,uDAAA,GAAkD;IAAlDA,wDAAA,SAAA+J,WAAA,CAAAtL,EAAA,IAAAgU,OAAA,CAAAK,kBAAA,CAAA/I,WAAA,CAAAtL,EAAA,EAAkD;IAgBlDuB,uDAAA,GAOb;IAPaA,wDAAA,YAAA+J,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAAhK,EAAA,MAAAgU,OAAA,CAAArB,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAtB,MAAA,kBAAAsB,WAAA,CAAAtB,MAAA,CAAA4I,GAAA,MAAAoB,OAAA,CAAArB,aAAA,KAAArH,WAAA,kBAAAA,WAAA,CAAAuH,QAAA,MAAAmB,OAAA,CAAArB,aAAA,EAOb;IAeepR,uDAAA,GAKf;IALeA,wDAAA,UAAA+J,WAAA,kBAAAA,WAAA,CAAA1D,OAAA,MAAAoM,OAAA,CAAA5J,QAAA,CAAAkB,WAAA,MAAA0I,OAAA,CAAA3J,cAAA,CAAAiB,WAAA,EAKf;IA+Ne/J,uDAAA,GAA6B;IAA7BA,wDAAA,SAAAyS,OAAA,CAAA3J,cAAA,CAAAiB,WAAA,EAA6B;IAkE7B/J,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAyS,OAAA,CAAA5J,QAAA,CAAAkB,WAAA,EAAuB;;;;;IAtXlC/J,qEAAA,GAAuE;IACrEA,wDAAA,IAAA+S,mDAAA,qBAiaM;IACR/S,mEAAA,EAAe;;;;IAjaSA,uDAAA,GAAa;IAAbA,wDAAA,YAAAgT,OAAA,CAAAC,QAAA,CAAa;;;;;;IAqanCjT,4DAAA,eAA+D;IAE3DA,uDAAA,aAAqC;IACvCA,0DAAA,EAAM;IACNA,4DAAA,eAAyC;IACvCA,oDAAA,+CACA;IAAAA,uDAAA,SAAM;IAAAA,oDAAA,0DACR;IAAAA,0DAAA,EAAM;IACNA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAkT,2EAAA;MAAAlT,2DAAA,CAAAmT,KAAA;MAAA,MAAAC,QAAA,GAAApT,2DAAA;MAAA,IAAAqT,OAAA;MAAA,OAAArT,yDAAA,EAAAqT,OAAA,GAASD,QAAA,CAAAE,WAAA,CAAAC,GAAA,CAAgB,SAAS,CAAC,mBAA1BF,OAAA,CAAAG,QAAA,CAAqC,UAAU,CAAC;IAAA,EAAC;IAG1DxT,uDAAA,aAAkC;IAClCA,oDAAA,qCACF;IAAAA,0DAAA,EAAS;;;;;IAdXA,wDAAA,IAAAyT,kDAAA,oBAeM;;;;IAfAzT,wDAAA,UAAA0T,OAAA,CAAAC,OAAA,KAAAD,OAAA,CAAA7J,KAAA,CAAwB;;;;;IAmBhC7J,4DAAA,eAA0D;IAEtDA,uDAAA,cAGE;IACJA,0DAAA,EAAM;IACNA,4DAAA,eAAsC;IAElCA,uDAAA,eAAyC;IAS3CA,0DAAA,EAAM;;;;IAfJA,uDAAA,GAAqE;IAArEA,wDAAA,SAAA4T,OAAA,CAAApT,gBAAA,kBAAAoT,OAAA,CAAApT,gBAAA,CAAAkI,KAAA,yCAAA1I,2DAAA,CAAqE;;;;;;IAuB3EA,4DAAA,eAAsD;IACpDA,uDAAA,eAAyD;IACzDA,4DAAA,kBAAoE;IAA5DA,wDAAA,mBAAA6T,6DAAA;MAAA7T,2DAAA,CAAA8T,KAAA;MAAA,MAAAC,QAAA,GAAA/T,2DAAA;MAAA,OAASA,yDAAA,CAAA+T,QAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAClChU,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;IAHJA,uDAAA,GAAkB;IAAlBA,wDAAA,QAAAiU,OAAA,CAAAC,UAAA,EAAAlU,2DAAA,CAAkB;;;;;;IA6BrBA,4DAAA,kBAIC;IADCA,wDAAA,mBAAAmU,uEAAA;MAAA,MAAA1S,WAAA,GAAAzB,2DAAA,CAAAoU,KAAA;MAAA,MAAAC,UAAA,GAAA5S,WAAA,CAAAG,SAAA;MAAA,MAAA0S,QAAA,GAAAtU,2DAAA;MAAA,OAASA,yDAAA,CAAAsU,QAAA,CAAAC,WAAA,CAAAF,UAAA,CAAkB;IAAA,EAAC;IAE5BrU,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IADPA,uDAAA,GACF;IADEA,gEAAA,MAAAqU,UAAA,MACF;;;;;IA5BJrU,4DAAA,eAA2D;IAGrDA,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;IACTA,4DAAA,kBAAwC;IACtCA,uDAAA,aAA0B;IAC5BA,0DAAA,EAAS;IACTA,4DAAA,kBAAwC;IACtCA,uDAAA,aAAgC;IAClCA,0DAAA,EAAS;IACTA,4DAAA,kBAAwC;IACtCA,uDAAA,aAA6B;IAC/BA,0DAAA,EAAS;IACTA,4DAAA,mBAAwC;IACtCA,uDAAA,cAA0B;IAC5BA,0DAAA,EAAS;IACTA,4DAAA,mBAAwC;IACtCA,uDAAA,cAAgC;IAClCA,0DAAA,EAAS;IAEXA,4DAAA,gBAAiC;IAC/BA,wDAAA,KAAAwU,8CAAA,sBAMS;IACXxU,0DAAA,EAAM;;;;IANgBA,uDAAA,IAAe;IAAfA,wDAAA,YAAAyU,OAAA,CAAAC,YAAA,CAAe;;;;;;IAUvC1U,4DAAA,eAGC;IAMOA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAAqE;IACnEA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IAERA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAA2U,6DAAA;MAAA3U,2DAAA,CAAA4U,KAAA;MAAA,MAAAC,QAAA,GAAA7U,2DAAA;MAAA,OAASA,yDAAA,CAAA6U,QAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvB9U,uDAAA,aAAoC;IACtCA,0DAAA,EAAS;;;;IAXLA,uDAAA,GACF;IADEA,gEAAA,2BAAA+U,OAAA,CAAAC,iBAAA,CAAAvM,MAAA,kBAAAsM,OAAA,CAAAC,iBAAA,CAAAvM,MAAA,CAAAhI,QAAA,wBACF;IAEET,uDAAA,GACF;IADEA,gEAAA,MAAA+U,OAAA,CAAAC,iBAAA,CAAA3O,OAAA,mBACF;;;;;;IA2CJrG,4DAAA,8BAKC;IAHCA,wDAAA,+BAAAiV,oGAAAnO,MAAA;MAAA9G,2DAAA,CAAAkV,KAAA;MAAA,MAAAC,QAAA,GAAAnV,2DAAA;MAAA,OAAqBA,yDAAA,CAAAmV,QAAA,CAAAC,wBAAA,CAAAtO,MAAA,CAAgC;IAAA,EAAC,gCAAAuO,qGAAA;MAAArV,2DAAA,CAAAkV,KAAA;MAAA,MAAAI,QAAA,GAAAtV,2DAAA;MAAA,OAChCA,yDAAA,CAAAsV,QAAA,CAAAC,yBAAA,EAA2B;IAAA,EADK;IAGvDvV,0DAAA,EAAqB;;;IADpBA,wDAAA,mBAAkB;;;;;;IAGpBA,4DAAA,iBAOE;IAFAA,wDAAA,mBAAAwV,8DAAA;MAAAxV,2DAAA,CAAAyV,KAAA;MAAA,MAAAC,QAAA,GAAA1V,2DAAA;MAAA,OAASA,yDAAA,CAAA0V,QAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IALtB3V,0DAAA,EAOE;;;;;;IAEFA,4DAAA,kBAKC;IAFCA,wDAAA,mBAAA4V,gEAAA;MAAA5V,2DAAA,CAAA6V,KAAA;MAAA,MAAAC,QAAA,GAAA9V,2DAAA;MAAA,OAASA,yDAAA,CAAA8V,QAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhC/V,uDAAA,aAAiC;IACnCA,0DAAA,EAAS;;;;;IAQPA,uDAAA,aAAuD;;;;;IACvDA,uDAAA,aAA0D;;;;;IAP5DA,4DAAA,kBAKC;IACCA,wDAAA,IAAAgW,2CAAA,iBAAuD;IACvDhW,wDAAA,IAAAiW,2CAAA,iBAA0D;IAC5DjW,0DAAA,EAAS;;;;IALPA,wDAAA,aAAAkW,OAAA,CAAAC,WAAA,IAAAD,OAAA,CAAA5C,WAAA,CAAA8C,OAAA,KAAAF,OAAA,CAAAG,YAAA,CAAkE;IAG9DrW,uDAAA,GAAkB;IAAlBA,wDAAA,UAAAkW,OAAA,CAAAC,WAAA,CAAkB;IAClBnW,uDAAA,GAAiB;IAAjBA,wDAAA,SAAAkW,OAAA,CAAAC,WAAA,CAAiB;;;;;;IAM3BnW,4DAAA,eAAuE;IAI/DA,uDAAA,eAKE;IACJA,0DAAA,EAAM;IACNA,4DAAA,cAA+B;IAAAA,oDAAA,GAAmC;IAAAA,0DAAA,EAAK;IACvEA,4DAAA,aAAgC;IAC9BA,oDAAA,GAKF;IAAAA,0DAAA,EAAI;IAENA,4DAAA,eAAmC;IACzBA,wDAAA,mBAAAsW,8DAAA;MAAAtW,2DAAA,CAAAuW,KAAA;MAAA,MAAAC,QAAA,GAAAxW,2DAAA;MAAA,OAASA,yDAAA,CAAAwW,QAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAC5BzW,uDAAA,cAAkC;IAClCA,4DAAA,YAAM;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAO;IAEtBA,4DAAA,mBAA4D;IAApDA,wDAAA,mBAAA0W,8DAAA;MAAA1W,2DAAA,CAAAuW,KAAA;MAAA,MAAAI,QAAA,GAAA3W,2DAAA;MAAA,OAASA,yDAAA,CAAA2W,QAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAC5B5W,uDAAA,cAA4B;IAC5BA,4DAAA,YAAM;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAO;;;;IAtBnBA,uDAAA,GAEC;IAFDA,wDAAA,SAAA6W,OAAA,CAAAC,YAAA,CAAAC,MAAA,kBAAAF,OAAA,CAAAC,YAAA,CAAAC,MAAA,CAAArO,KAAA,yCAAA1I,2DAAA,CAEC;IAI0BA,uDAAA,GAAmC;IAAnCA,+DAAA,CAAA6W,OAAA,CAAAC,YAAA,CAAAC,MAAA,kBAAAF,OAAA,CAAAC,YAAA,CAAAC,MAAA,CAAAtW,QAAA,CAAmC;IAEhET,uDAAA,GAKF;IALEA,gEAAA,MAAA6W,OAAA,CAAAC,YAAA,CAAAvY,IAAA,uEAKF;;;;;IAsCFyB,4DAAA,eAAkD;IAChDA,uDAAA,eAA6D;IAC/DA,0DAAA,EAAM;;;IADCA,uDAAA,GAA0C;IAA1CA,wDAAA,4CAAAA,2DAAA,CAA0C;;;;;IAhBnDA,4DAAA,eAAsE;IAEpEA,uDAAA,sBAAsE;IAatEA,wDAAA,IAAAgX,gDAAA,mBAEM;IACRhX,0DAAA,EAAM;;;;IAPFA,uDAAA,GAAmD;IAAnDA,yDAAA,YAAAiX,QAAA,CAAAC,cAAA,oBAAmD;IAI/ClX,uDAAA,GAAqB;IAArBA,wDAAA,UAAAiX,QAAA,CAAAC,cAAA,CAAqB;;;;;;;;;;;;;IAM7BlX,4DAAA,eAAsE;IAGhEA,uDAAA,eAOE;IACJA,0DAAA,EAAM;IACNA,4DAAA,cAAkC;IAChCA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,aAAuB;IAAAA,oDAAA,GAAsC;IAAAA,0DAAA,EAAI;IACjEA,4DAAA,eAAoC;IAClCA,uDAAA,aAQK;IACLA,4DAAA,iBAA2B;IAAAA,oDAAA,IAAiB;IAAAA,0DAAA,EAAO;;;;IAtBjDA,uDAAA,GAIC;IAJDA,wDAAA,SAAAmX,QAAA,CAAAC,UAAA,CAAAL,MAAA,kBAAAI,QAAA,CAAAC,UAAA,CAAAL,MAAA,CAAArO,KAAA,MAAAyO,QAAA,CAAAC,UAAA,CAAAC,SAAA,kBAAAF,QAAA,CAAAC,UAAA,CAAAC,SAAA,CAAA3O,KAAA,yCAAA1I,2DAAA,CAIC;IAKHA,uDAAA,GACF;IADEA,gEAAA,OAAAmX,QAAA,CAAAC,UAAA,CAAAL,MAAA,kBAAAI,QAAA,CAAAC,UAAA,CAAAL,MAAA,CAAAtW,QAAA,MAAA0W,QAAA,CAAAC,UAAA,CAAAC,SAAA,kBAAAF,QAAA,CAAAC,UAAA,CAAAC,SAAA,CAAA5W,QAAA,OACF;IACuBT,uDAAA,GAAsC;IAAtCA,+DAAA,CAAAmX,QAAA,CAAAG,kBAAA,CAAAH,QAAA,CAAAI,YAAA,EAAsC;IAIzDvX,uDAAA,GAKE;IALFA,wDAAA,YAAAA,6DAAA,IAAAwX,IAAA,EAAAL,QAAA,CAAAM,WAAA,kBAAAN,QAAA,CAAAM,WAAA,aAAAN,QAAA,CAAAM,WAAA,aAAAN,QAAA,CAAAM,WAAA,mBAKE;IAEuBzX,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAmX,QAAA,CAAAM,WAAA,CAAiB;;;;;IAYhDzX,4DAAA,eAA2D;IAC7BA,oDAAA,GAE1B;IAAAA,0DAAA,EAAO;IACTA,4DAAA,gBAA+B;IAC7BA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IALqBA,uDAAA,GAE1B;IAF0BA,+DAAA,CAAA0X,QAAA,CAAAJ,kBAAA,CAAAI,QAAA,CAAAH,YAAA,EAE1B;IAEAvX,uDAAA,GACF;IADEA,gEAAA,OAAA0X,QAAA,CAAAN,UAAA,CAAAL,MAAA,kBAAAW,QAAA,CAAAN,UAAA,CAAAL,MAAA,CAAAtW,QAAA,MAAAiX,QAAA,CAAAN,UAAA,CAAAC,SAAA,kBAAAK,QAAA,CAAAN,UAAA,CAAAC,SAAA,CAAA5W,QAAA,OACF;;;;;;;;;;;IAkBAT,4DAAA,kBAKC;IAHCA,wDAAA,mBAAA2X,4EAAA;MAAA3X,2DAAA,CAAA4X,KAAA;MAAA,MAAAC,QAAA,GAAA7X,2DAAA;MAAA,OAASA,yDAAA,CAAA6X,QAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAI3B9X,uDAAA,aAGK;IACPA,0DAAA,EAAS;;;;IANPA,wDAAA,YAAAA,6DAAA,IAAA+X,IAAA,GAAAC,QAAA,CAAAd,cAAA,EAAuC;IAIrClX,uDAAA,GAA0D;IAA1DA,wDAAA,YAAAgY,QAAA,CAAAd,cAAA,iCAA0D;;;;;;;;;;;IAtClElX,4DAAA,eAIC;IAECA,wDAAA,IAAAiY,gDAAA,mBAOM;IAGNjY,4DAAA,eAA6B;IAGzBA,wDAAA,mBAAAkY,mEAAA;MAAAlY,2DAAA,CAAAmY,KAAA;MAAA,MAAAC,QAAA,GAAApY,2DAAA;MAAA,OAASA,yDAAA,CAAAoY,QAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAI1BrY,uDAAA,aAGK;IACPA,0DAAA,EAAS;IAGTA,wDAAA,IAAAsY,mDAAA,sBAUS;IAGTtY,4DAAA,kBAAyD;IAAjDA,wDAAA,mBAAAuY,mEAAA;MAAAvY,2DAAA,CAAAmY,KAAA;MAAA,MAAAK,QAAA,GAAAxY,2DAAA;MAAA,OAASA,yDAAA,CAAAwY,QAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IACzBzY,uDAAA,aAAkC;IACpCA,0DAAA,EAAS;IAGTA,4DAAA,kBAA2D;IAAnDA,wDAAA,mBAAA0Y,mEAAA;MAAA1Y,2DAAA,CAAAmY,KAAA;MAAA,MAAAQ,QAAA,GAAA3Y,2DAAA;MAAA,OAASA,yDAAA,CAAA2Y,QAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IACpC5Y,uDAAA,aAGK;IACPA,0DAAA,EAAS;;;;IAnDXA,wDAAA,YAAAA,6DAAA,IAAA6Y,IAAA,GAAAC,QAAA,CAAAC,gBAAA,EAAyC;IAInC/Y,uDAAA,GAAiC;IAAjCA,wDAAA,SAAA8Y,QAAA,CAAA1B,UAAA,CAAA7Y,IAAA,aAAiC;IAenCyB,uDAAA,GAAmC;IAAnCA,wDAAA,YAAAA,6DAAA,IAAA+X,IAAA,EAAAe,QAAA,CAAAE,WAAA,EAAmC;IAIjChZ,uDAAA,GAAiE;IAAjEA,wDAAA,YAAA8Y,QAAA,CAAAE,WAAA,2CAAiE;IAMlEhZ,uDAAA,GAAiC;IAAjCA,wDAAA,SAAA8Y,QAAA,CAAA1B,UAAA,CAAA7Y,IAAA,aAAiC;IAoBhCyB,uDAAA,GAAyD;IAAzDA,wDAAA,YAAA8Y,QAAA,CAAAG,eAAA,+BAAyD;;;;;;IAOjEjZ,4DAAA,eAA8D;IAEzBA,oDAAA,GAE/B;IAAAA,0DAAA,EAAO;IACTA,4DAAA,gBAAoC;IAClCA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IAETA,4DAAA,eAAgC;IACtBA,wDAAA,mBAAAkZ,mEAAA;MAAAlZ,2DAAA,CAAAmZ,KAAA;MAAA,MAAAC,QAAA,GAAApZ,2DAAA;MAAA,OAASA,yDAAA,CAAAoZ,QAAA,CAAAR,kBAAA,EAAoB;IAAA,EAAC;IACpC5Y,uDAAA,aAA6B;IAC/BA,0DAAA,EAAS;IACTA,4DAAA,kBAA2D;IAAnDA,wDAAA,mBAAAqZ,mEAAA;MAAArZ,2DAAA,CAAAmZ,KAAA;MAAA,MAAAG,QAAA,GAAAtZ,2DAAA;MAAA,OAASA,yDAAA,CAAAsZ,QAAA,CAAAb,OAAA,EAAS;IAAA,EAAC;IACzBzY,uDAAA,cAAkC;IACpCA,0DAAA,EAAS;;;;IAbwBA,uDAAA,GAE/B;IAF+BA,+DAAA,CAAAuZ,QAAA,CAAAjC,kBAAA,CAAAiC,QAAA,CAAAhC,YAAA,EAE/B;IAEAvX,uDAAA,GACF;IADEA,gEAAA,OAAAuZ,QAAA,CAAAnC,UAAA,CAAAL,MAAA,kBAAAwC,QAAA,CAAAnC,UAAA,CAAAL,MAAA,CAAAtW,QAAA,MAAA8Y,QAAA,CAAAnC,UAAA,CAAAC,SAAA,kBAAAkC,QAAA,CAAAnC,UAAA,CAAAC,SAAA,CAAA5W,QAAA,OACF;;;;;;;;;;;IA7HNT,4DAAA,eAKC;IADCA,wDAAA,uBAAAwZ,8DAAA;MAAAxZ,2DAAA,CAAAyZ,KAAA;MAAA,MAAAC,QAAA,GAAA1Z,2DAAA;MAAA,OAAaA,yDAAA,CAAA0Z,QAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAG/B3Z,wDAAA,IAAA4Z,0CAAA,mBAkBM;IAGN5Z,wDAAA,IAAA6Z,0CAAA,qBA6BM;IAGN7Z,wDAAA,IAAA8Z,0CAAA,qBAuDM;IAGN9Z,wDAAA,IAAA+Z,0CAAA,oBAiBM;IACR/Z,0DAAA,EAAM;;;;IArIJA,wDAAA,YAAAA,6DAAA,IAAAga,IAAA,EAAAC,OAAA,CAAAhB,eAAA,EAA0C;IAIpCjZ,uDAAA,GAAiC;IAAjCA,wDAAA,SAAAia,OAAA,CAAA7C,UAAA,CAAA7Y,IAAA,aAAiC;IAqBjCyB,uDAAA,GAAiC;IAAjCA,wDAAA,SAAAia,OAAA,CAAA7C,UAAA,CAAA7Y,IAAA,aAAiC;IAmCpCyB,uDAAA,GAAsB;IAAtBA,wDAAA,UAAAia,OAAA,CAAAhB,eAAA,CAAsB;IAuDnBjZ,uDAAA,GAAqB;IAArBA,wDAAA,SAAAia,OAAA,CAAAhB,eAAA,CAAqB;;;;;IA0DrBjZ,4DAAA,eAAsD;IAElDA,uDAAA,aAAiC;IACjCA,oDAAA,qBACF;IAAAA,0DAAA,EAAM;;;;;IAERA,4DAAA,eAA4D;IAExDA,uDAAA,aAAsC;IACtCA,oDAAA,sBACF;IAAAA,0DAAA,EAAM;;;;;;IAYNA,4DAAA,kBAIC;IAFCA,wDAAA,mBAAAka,uEAAA;MAAAla,2DAAA,CAAAma,KAAA;MAAA,MAAAC,QAAA,GAAApa,2DAAA;MAAA,OAASA,yDAAA,CAAAoa,QAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAGlCra,oDAAA,+BACF;IAAAA,0DAAA,EAAS;;;;;;IACTA,4DAAA,kBAIC;IAFCA,wDAAA,mBAAAsa,uEAAA;MAAAta,2DAAA,CAAAua,KAAA;MAAA,MAAAC,QAAA,GAAAxa,2DAAA;MAAA,OAASA,yDAAA,CAAAwa,QAAA,CAAAC,wBAAA,EAA0B;IAAA,EAAC;IAGpCza,oDAAA,sCACF;IAAAA,0DAAA,EAAS;;;;;IAWbA,4DAAA,eAA4D;IAExDA,uDAAA,eAEO;IACPA,4DAAA,gBACG;IAAAA,oDAAA,sCAA+B;IAAAA,0DAAA,EACjC;;;;;IA0BGA,uDAAA,aAKK;;;;;IAyBLA,4DAAA,WAAwC;IACtCA,oDAAA,GAAuD;IAAAA,0DAAA,EACxD;;;;IADCA,uDAAA,GAAuD;IAAvDA,gEAAA,aAAA0a,iBAAA,CAAAC,YAAA,CAAAhU,MAAA,oBAAuD;;;;;;;;;;;;IAjD/D3G,4DAAA,kBAMC;IAJCA,wDAAA,mBAAA4a,6EAAA;MAAA,MAAAnZ,WAAA,GAAAzB,2DAAA,CAAA6a,KAAA;MAAA,MAAAH,iBAAA,GAAAjZ,WAAA,CAAAG,SAAA;MAAA,MAAAkZ,QAAA,GAAA9a,2DAAA;MAAA,OACiBA,yDAAA,CAAA0a,iBAAA,CAAAjc,EAAA,IAAmBqc,QAAA,CAAAC,2BAAA,CAAAL,iBAAA,CAAAjc,EAAA,CAEhD;IAAA,EADa;IAIDuB,4DAAA,eAA2B;IAUvBA,wDAAA,IAAAgb,wDAAA,iBAKK;IACPhb,0DAAA,EAAM;IAIRA,4DAAA,eAA2B;IACzBA,uDAAA,eAKE;IACJA,0DAAA,EAAM;IAGNA,4DAAA,eAAsC;IAIlCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAEC;IACCA,oDAAA,IACA;IAAAA,wDAAA,KAAAib,4DAAA,oBAEC;IACHjb,0DAAA,EAAM;;;;;IAxCJA,uDAAA,GAKE;IALFA,wDAAA,YAAAA,6DAAA,IAAAkb,IAAA,EAAAR,iBAAA,CAAAjc,EAAA,IAAA0c,QAAA,CAAAC,sBAAA,CAAAV,iBAAA,CAAAjc,EAAA,IAAAic,iBAAA,CAAAjc,EAAA,KAAA0c,QAAA,CAAAC,sBAAA,CAAAV,iBAAA,CAAAjc,EAAA,GAKE;IAGCuB,uDAAA,GAGnB;IAHmBA,wDAAA,SAAA0a,iBAAA,CAAAjc,EAAA,IAAA0c,QAAA,CAAAC,sBAAA,CAAAV,iBAAA,CAAAjc,EAAA,EAGnB;IAQgBuB,uDAAA,GAAiD;IAAjDA,wDAAA,QAAAmb,QAAA,CAAAE,2BAAA,CAAAX,iBAAA,GAAA1a,2DAAA,CAAiD,QAAAmb,QAAA,CAAAG,0BAAA,CAAAZ,iBAAA;IAYjD1a,uDAAA,GACF;IADEA,gEAAA,MAAAmb,QAAA,CAAAG,0BAAA,CAAAZ,iBAAA,OACF;IAIE1a,uDAAA,GACA;IADAA,gEAAA,MAAA0a,iBAAA,CAAAa,OAAA,8CACA;IAAOvb,uDAAA,GAA+B;IAA/BA,wDAAA,SAAA0a,iBAAA,CAAAC,YAAA,CAA+B;;;;;IAnD9C3a,4DAAA,UAEC;IACCA,wDAAA,IAAAwb,oDAAA,wBAqDS;IACXxb,0DAAA,EAAM;;;;IArDuBA,uDAAA,GAAyB;IAAzBA,wDAAA,YAAAyb,QAAA,CAAAC,sBAAA,CAAyB;;;;;IAwDtD1b,4DAAA,eAGC;IAEGA,uDAAA,aAAmD;IACnDA,4DAAA,eAAqB;IAAAA,oDAAA,2CAAoC;IAAAA,0DAAA,EAAM;;;;;IAoBjEA,uDAAA,aAA2D;;;;;IAC3DA,uDAAA,aAAkD;;;;;;IAtL1DA,4DAAA,eAIC;IADCA,wDAAA,mBAAA2b,0DAAA;MAAA3b,2DAAA,CAAA4b,KAAA;MAAA,MAAAC,QAAA,GAAA7b,2DAAA;MAAA,OAASA,yDAAA,CAAA6b,QAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAE7B9b,4DAAA,eAGC;IADCA,wDAAA,mBAAA+b,0DAAAjV,MAAA;MAAA,OAASA,MAAA,CAAAkV,eAAA,EAAwB;IAAA,EAAC;IAGlChc,4DAAA,eAEC;IAEGA,oDAAA,mCACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAic,6DAAA;MAAAjc,2DAAA,CAAA4b,KAAA;MAAA,MAAAM,QAAA,GAAAlc,2DAAA;MAAA,OAASA,yDAAA,CAAAkc,QAAA,CAAAJ,iBAAA,EAAmB;IAAA,EAAC;IAG7B9b,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;IAIXA,4DAAA,eAEC;IAEGA,oDAAA,yCACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,gBAEC;IAEGA,oDAAA,IACF;IAAAA,0DAAA,EAAM;IACNA,wDAAA,KAAAmc,2CAAA,mBAKM;IACNnc,wDAAA,KAAAoc,2CAAA,mBAKM;IACRpc,0DAAA,EAAM;IAIRA,4DAAA,gBAAoE;IAG9DA,oDAAA,6CACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,gBAA4B;IAC1BA,wDAAA,KAAAqc,8CAAA,sBAMS;IACTrc,wDAAA,KAAAsc,8CAAA,sBAMS;IACXtc,0DAAA,EAAM;IAERA,4DAAA,gBAA8D;IAC5DA,oDAAA,IACF;IAAAA,0DAAA,EAAM;IAIRA,4DAAA,gBAAsC;IAEpCA,wDAAA,KAAAuc,2CAAA,mBASM;IAGNvc,wDAAA,KAAAwc,2CAAA,mBAyDM;IAGNxc,wDAAA,KAAAyc,2CAAA,mBAQM;IACRzc,0DAAA,EAAM;IAGNA,4DAAA,gBAEC;IAEGA,wDAAA,mBAAA0c,8DAAA;MAAA1c,2DAAA,CAAA4b,KAAA;MAAA,MAAAe,QAAA,GAAA3c,2DAAA;MAAA,OAASA,yDAAA,CAAA2c,QAAA,CAAAb,iBAAA,EAAmB;IAAA,EAAC;IAG7B9b,oDAAA,iBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,mBAIC;IAHCA,wDAAA,mBAAA4c,8DAAA;MAAA5c,2DAAA,CAAA4b,KAAA;MAAA,MAAAiB,QAAA,GAAA7c,2DAAA;MAAA,OAASA,yDAAA,CAAA6c,QAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAI1B9c,wDAAA,KAAA+c,yCAAA,iBAA2D;IAC3D/c,wDAAA,KAAAgd,yCAAA,iBAAkD;IAClDhd,4DAAA,YAAM;IAAAA,oDAAA,IAAkD;IAAAA,0DAAA,EAAO;;;;IApJ7DA,uDAAA,IACF;IADEA,gEAAA,OAAAid,OAAA,CAAAC,iBAAA,kBAAAD,OAAA,CAAAC,iBAAA,CAAA7W,OAAA,iCACF;IACMrG,uDAAA,GAAiC;IAAjCA,wDAAA,SAAAid,OAAA,CAAApU,QAAA,CAAAoU,OAAA,CAAAC,iBAAA,EAAiC;IAMjCld,uDAAA,GAAuC;IAAvCA,wDAAA,SAAAid,OAAA,CAAAnU,cAAA,CAAAmU,OAAA,CAAAC,iBAAA,EAAuC;IAiBxCld,uDAAA,GAAoC;IAApCA,wDAAA,UAAAid,OAAA,CAAAE,2BAAA,GAAoC;IAOpCnd,uDAAA,GAAmC;IAAnCA,wDAAA,SAAAid,OAAA,CAAAE,2BAAA,GAAmC;IASxCnd,uDAAA,GACF;IADEA,gEAAA,MAAAid,OAAA,CAAAG,qBAAA,CAAAzW,MAAA,gDACF;IAMM3G,uDAAA,GAA4B;IAA5BA,wDAAA,SAAAid,OAAA,CAAAI,sBAAA,CAA4B;IAa/Brd,uDAAA,GAAkE;IAAlEA,wDAAA,UAAAid,OAAA,CAAAI,sBAAA,IAAAJ,OAAA,CAAAvB,sBAAA,CAAA/U,MAAA,KAAkE;IA4DlE3G,uDAAA,GAAoE;IAApEA,wDAAA,UAAAid,OAAA,CAAAI,sBAAA,IAAAJ,OAAA,CAAAvB,sBAAA,CAAA/U,MAAA,OAAoE;IAsBrE3G,uDAAA,GAA+D;IAA/DA,wDAAA,aAAAid,OAAA,CAAAG,qBAAA,CAAAzW,MAAA,UAAAsW,OAAA,CAAAK,YAAA,CAA+D;IAG3Dtd,uDAAA,GAAkB;IAAlBA,wDAAA,SAAAid,OAAA,CAAAK,YAAA,CAAkB;IAClBtd,uDAAA,GAAmB;IAAnBA,wDAAA,UAAAid,OAAA,CAAAK,YAAA,CAAmB;IACjBtd,uDAAA,GAAkD;IAAlDA,+DAAA,CAAAid,OAAA,CAAAK,YAAA,sCAAkD;;;;;IAe1Dtd,4DAAA,gBAAqE;IACnEA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAAud,QAAA,CAAAC,uBAAA,MACF;;;;;;IA0EAxd,4DAAA,eAAiE;IAE7DA,wDAAA,mBAAAyd,0EAAA;MAAAzd,2DAAA,CAAA0d,KAAA;MAAA,MAAAC,QAAA,GAAA3d,2DAAA;MAAA,OAASA,yDAAA,CAAA2d,QAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAI9B5d,uDAAA,aAA4B;IAC5BA,oDAAA,yBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAIC;IAHCA,wDAAA,mBAAA6d,0EAAA;MAAA7d,2DAAA,CAAA0d,KAAA;MAAA,MAAAI,QAAA,GAAA9d,2DAAA;MAAA,OAASA,yDAAA,CAAA8d,QAAA,CAAAC,8BAAA,EAAgC;IAAA,EAAC;IAI1C/d,uDAAA,aAA4B;IAC5BA,oDAAA,kBACF;IAAAA,0DAAA,EAAS;;;;IAbPA,uDAAA,GAA4B;IAA5BA,wDAAA,aAAAge,QAAA,CAAAC,eAAA,CAA4B;IAQ5Bje,uDAAA,GAAoC;IAApCA,wDAAA,aAAAge,QAAA,CAAAE,uBAAA,CAAoC;;;;;;IA3B1Cle,4DAAA,eAGC;IAMOA,wDAAA,oBAAAme,oEAAA;MAAAne,2DAAA,CAAAoe,KAAA;MAAA,MAAAC,QAAA,GAAAre,2DAAA;MAAA,OAAUA,yDAAA,CAAAqe,QAAA,CAAAC,4BAAA,EAA8B;IAAA,EAAC;IAH3Cte,0DAAA,EAIE;IACFA,uDAAA,gBAA+B;IAC/BA,4DAAA,gBAA0B;IAAAA,oDAAA,6BAAiB;IAAAA,0DAAA,EAAO;IAItDA,wDAAA,IAAAue,iDAAA,mBAiBM;IACRve,0DAAA,EAAM;;;;IA1BEA,uDAAA,GAAyC;IAAzCA,wDAAA,YAAAwe,QAAA,CAAAC,2BAAA,GAAyC;IAQpBze,uDAAA,GAAoC;IAApCA,wDAAA,SAAAwe,QAAA,CAAAE,qBAAA,CAAAC,IAAA,KAAoC;;;;;;IAsB/D3e,4DAAA,kBAKC;IAHCA,wDAAA,mBAAA4e,uEAAA;MAAA5e,2DAAA,CAAA6e,KAAA;MAAA,MAAAC,QAAA,GAAA9e,2DAAA;MAAA,OAASA,yDAAA,CAAA8e,QAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAIzB/e,uDAAA,aAAmC;IACnCA,oDAAA,8BACF;IAAAA,0DAAA,EAAS;;;;IALPA,wDAAA,aAAAgf,QAAA,CAAAf,eAAA,CAA4B;;;;;;;;;;;IAM9Bje,4DAAA,kBAKC;IAHCA,wDAAA,mBAAAif,uEAAA;MAAAjf,2DAAA,CAAAkf,KAAA;MAAA,MAAAC,QAAA,GAAAnf,2DAAA;MAAA,OAASA,yDAAA,CAAAmf,QAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAIlCpf,uDAAA,aAGK;IACLA,oDAAA,uBACF;IAAAA,0DAAA,EAAS;;;;IARPA,wDAAA,aAAAqf,QAAA,CAAAnB,uBAAA,CAAoC;IAKlCle,uDAAA,GAAkD;IAAlDA,wDAAA,YAAAA,6DAAA,IAAAsf,IAAA,EAAAD,QAAA,CAAAnB,uBAAA,EAAkD;;;;;IASxDle,4DAAA,eAGC;IACCA,uDAAA,aAAsC;IACtCA,4DAAA,WAAM;IAAAA,oDAAA,sCAA+B;IAAAA,0DAAA,EAAO;;;;;IAG9CA,4DAAA,eAKC;IACCA,uDAAA,aAAiC;IACjCA,4DAAA,WAAM;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAO;;;;;IAmC1BA,4DAAA,gBAAgE;IAC9DA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAAuf,iBAAA,CAAAjO,QAAA,CAAA7Q,QAAA,MACF;;;;;IAUAT,4DAAA,gBAAyD;IACvDA,uDAAA,aAA4B;IAC9BA,0DAAA,EAAO;;;;;;IAMTA,4DAAA,kBASC;IAPCA,wDAAA,mBAAAwf,8EAAA;MAAAxf,2DAAA,CAAAyf,KAAA;MAAA,MAAAF,iBAAA,GAAAvf,2DAAA,GAAA4B,SAAA;MAAA,MAAA8d,QAAA,GAAA1f,2DAAA;MACmB0f,QAAA,CAAA9B,kBAAA,EAClB;MAAkB8B,QAAA,CAAAhB,qBAAA,CAAApf,KAAA,EAClB;MAAA,OAAkBU,yDAAA,CAAA0f,QAAA,CAAAhB,qBAAA,CAAAiB,GAAA,CAAAJ,iBAAA,CAAA9gB,EAAA,CAEjC;IAAA,EADe;IAIDuB,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;;;;;;;;;IA/DfA,4DAAA,eAUC;IAMKA,wDAAA,oBAAA4f,oEAAA;MAAA,MAAAne,WAAA,GAAAzB,2DAAA,CAAA6f,KAAA;MAAA,MAAAN,iBAAA,GAAA9d,WAAA,CAAAG,SAAA;MAAA,MAAAke,QAAA,GAAA9f,2DAAA;MAAA,OAAUA,yDAAA,CAAA8f,QAAA,CAAAC,2BAAA,CAAAR,iBAAA,CAAA9gB,EAAA,CAA4C;IAAA,EAAC;IAHzDuB,0DAAA,EAIE;IACFA,uDAAA,gBAA+B;IACjCA,0DAAA,EAAM;IAGNA,4DAAA,eAAkC;IAK9BA,uDAAA,QAAwD;IAC1DA,0DAAA,EAAM;IAENA,4DAAA,eAA+B;IAE3BA,wDAAA,IAAAggB,kDAAA,oBAEO;IACPhgB,4DAAA,iBAAmC;IAAAA,oDAAA,IAEjC;IAAAA,0DAAA,EAAO;IAGXA,4DAAA,gBAA+B;IAE3BA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IACPA,wDAAA,KAAAigB,mDAAA,oBAEO;IACTjgB,0DAAA,EAAM;IAIRA,4DAAA,gBAAuC;IACrCA,wDAAA,KAAAkgB,qDAAA,sBAWS;IACTlgB,4DAAA,mBAIC;IAHCA,wDAAA,mBAAAmgB,qEAAA;MAAA,MAAA1e,WAAA,GAAAzB,2DAAA,CAAA6f,KAAA;MAAA,MAAAN,iBAAA,GAAA9d,WAAA,CAAAG,SAAA;MAAA,MAAAwe,QAAA,GAAApgB,2DAAA;MAAA,OAASA,yDAAA,CAAAogB,QAAA,CAAAC,kBAAA,CAAAd,iBAAA,CAAA9gB,EAAA,CAAmC;IAAA,EAAC;IAI7CuB,uDAAA,cAA4B;IAC9BA,0DAAA,EAAS;;;;;IAhEbA,wDAAA,YAAAA,6DAAA,KAAAsgB,IAAA,GAAAf,iBAAA,CAAAgB,MAAA,EAAAC,QAAA,CAAA9B,qBAAA,CAAA+B,GAAA,CAAAlB,iBAAA,CAAA9gB,EAAA,GAGE;IAMEuB,uDAAA,GAAsD;IAAtDA,wDAAA,YAAAwgB,QAAA,CAAA9B,qBAAA,CAAA+B,GAAA,CAAAlB,iBAAA,CAAA9gB,EAAA,EAAsD;IAUtDuB,uDAAA,GAAmD;IAAnDA,wDAAA,YAAAwgB,QAAA,CAAAE,oBAAA,CAAAnB,iBAAA,CAAAhhB,IAAA,EAAmD;IAEhDyB,uDAAA,GAAgD;IAAhDA,wDAAA,CAAAwgB,QAAA,CAAAG,mBAAA,CAAApB,iBAAA,CAAAhhB,IAAA,EAAgD;IAKdyB,uDAAA,GAA2B;IAA3BA,wDAAA,SAAAuf,iBAAA,CAAAjO,QAAA,CAA2B;IAG3BtR,uDAAA,GAEjC;IAFiCA,+DAAA,CAAAuf,iBAAA,CAAAlZ,OAAA,CAEjC;IAKArG,uDAAA,GACF;IADEA,gEAAA,MAAAwgB,QAAA,CAAAI,sBAAA,CAAArB,iBAAA,CAAApZ,SAAA,OACF;IACOnG,uDAAA,GAAyB;IAAzBA,wDAAA,SAAAuf,iBAAA,CAAAgB,MAAA,CAAyB;IAS/BvgB,uDAAA,GAA0B;IAA1BA,wDAAA,UAAAuf,iBAAA,CAAAgB,MAAA,CAA0B;;;;;;IAuBnCvgB,4DAAA,eAA8D;IAE1DA,wDAAA,mBAAA6gB,oEAAA;MAAA7gB,2DAAA,CAAA8gB,KAAA;MAAA,MAAAC,QAAA,GAAA/gB,2DAAA;MAAA,OAASA,yDAAA,CAAA+gB,QAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IAIjChhB,uDAAA,aAGK;IACLA,oDAAA,qBACF;IAAAA,0DAAA,EAAS;;;;IARPA,uDAAA,GAAmC;IAAnCA,wDAAA,aAAAihB,QAAA,CAAAC,sBAAA,CAAmC;IAKjClhB,uDAAA,GAAiD;IAAjDA,wDAAA,YAAAA,6DAAA,IAAAsf,IAAA,EAAA2B,QAAA,CAAAC,sBAAA,EAAiD;;;;;;IAQzDlhB,4DAAA,eAAoE;IAE5DA,oDAAA,sCAA0B;IAAAA,0DAAA,EAAK;IACnCA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAmhB,oEAAA;MAAAnhB,2DAAA,CAAAohB,KAAA;MAAA,MAAAC,QAAA,GAAArhB,2DAAA;MAAA,OAASA,yDAAA,CAAAqhB,QAAA,CAAAC,0BAAA,EAA4B;IAAA,EAAC;IAGtCthB,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;IAGXA,4DAAA,eAA8B;IAKtBA,wDAAA,2BAAAuhB,2EAAAza,MAAA;MAAA9G,2DAAA,CAAAohB,KAAA;MAAA,MAAAI,QAAA,GAAAxhB,2DAAA;MAAA,OAAAA,yDAAA,CAAAwhB,QAAA,CAAAC,kBAAA,GAAA3a,MAAA;IAAA,EAAgC,oBAAA4a,oEAAA;MAAA1hB,2DAAA,CAAAohB,KAAA;MAAA,MAAAO,QAAA,GAAA3hB,2DAAA;MAAA,OACtBA,yDAAA,CAAA2hB,QAAA,CAAAC,wBAAA,EAA0B;IAAA,EADJ;IAFlC5hB,0DAAA,EAIE;IACFA,uDAAA,iBAAuC;IACvCA,4DAAA,iBAA2B;IAAAA,oDAAA,4BAAoB;IAAAA,0DAAA,EAAO;IAI1DA,4DAAA,gBAA0B;IAIpBA,wDAAA,2BAAA6hB,4EAAA/a,MAAA;MAAA9G,2DAAA,CAAAohB,KAAA;MAAA,MAAAU,QAAA,GAAA9hB,2DAAA;MAAA,OAAAA,yDAAA,CAAA8hB,QAAA,CAAAC,mBAAA,GAAAjb,MAAA;IAAA,EAAiC,oBAAAkb,qEAAA;MAAAhiB,2DAAA,CAAAohB,KAAA;MAAA,MAAAa,QAAA,GAAAjiB,2DAAA;MAAA,OACvBA,yDAAA,CAAAiiB,QAAA,CAAAL,wBAAA,EAA0B;IAAA,EADH;IAFnC5hB,0DAAA,EAIE;IACFA,uDAAA,iBAAuC;IACvCA,4DAAA,iBAA2B;IAAAA,oDAAA,qCAAwB;IAAAA,0DAAA,EAAO;IAI9DA,4DAAA,gBAA0B;IAIpBA,wDAAA,2BAAAkiB,4EAAApb,MAAA;MAAA9G,2DAAA,CAAAohB,KAAA;MAAA,MAAAe,QAAA,GAAAniB,2DAAA;MAAA,OAAAA,yDAAA,CAAAmiB,QAAA,CAAAC,cAAA,GAAAtb,MAAA;IAAA,EAA4B,oBAAAub,qEAAA;MAAAriB,2DAAA,CAAAohB,KAAA;MAAA,MAAAkB,QAAA,GAAAtiB,2DAAA;MAAA,OAClBA,yDAAA,CAAAsiB,QAAA,CAAAV,wBAAA,EAA0B;IAAA,EADR;IAF9B5hB,0DAAA,EAIE;IACFA,uDAAA,iBAAuC;IACvCA,4DAAA,iBAA2B;IAAAA,oDAAA,wCAAgC;IAAAA,0DAAA,EAAO;;;;IA5BhEA,uDAAA,GAAgC;IAAhCA,wDAAA,YAAAuiB,QAAA,CAAAd,kBAAA,CAAgC;IAYhCzhB,uDAAA,GAAiC;IAAjCA,wDAAA,YAAAuiB,QAAA,CAAAR,mBAAA,CAAiC;IAYjC/hB,uDAAA,GAA4B;IAA5BA,wDAAA,YAAAuiB,QAAA,CAAAH,cAAA,CAA4B;;;;;;IAzR1CpiB,4DAAA,eAAsE;IAK9DA,uDAAA,aAA2B;IAC3BA,4DAAA,WAAM;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAO;IAC1BA,wDAAA,IAAAwiB,2CAAA,oBAEO;IACTxiB,0DAAA,EAAM;IACNA,4DAAA,eAAkC;IAE9BA,wDAAA,mBAAAyiB,6DAAA;MAAAziB,2DAAA,CAAA0iB,KAAA;MAAA,MAAAC,QAAA,GAAA3iB,2DAAA;MAAA,OAASA,yDAAA,CAAA2iB,QAAA,CAAArB,0BAAA,EAA4B;IAAA,EAAC;IAItCthB,uDAAA,cAA0B;IAC5BA,0DAAA,EAAS;IACTA,4DAAA,mBAKC;IAJCA,wDAAA,mBAAA4iB,8DAAA;MAAA5iB,2DAAA,CAAA0iB,KAAA;MAAA,MAAAG,QAAA,GAAA7iB,2DAAA;MAAA,OAASA,yDAAA,CAAA6iB,QAAA,CAAAC,iBAAA,CAAkB,IAAI,CAAC;IAAA,EAAC;IAKjC9iB,uDAAA,cAGK;IACPA,0DAAA,EAAS;IACTA,4DAAA,mBAIC;IAHCA,wDAAA,mBAAA+iB,8DAAA;MAAA/iB,2DAAA,CAAA0iB,KAAA;MAAA,MAAAM,QAAA,GAAAhjB,2DAAA;MAAA,OAASA,yDAAA,CAAAgjB,QAAA,CAAAC,uBAAA,EAAyB;IAAA,EAAC;IAInCjjB,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;IAKbA,4DAAA,gBAAkC;IAG5BA,wDAAA,mBAAAkjB,8DAAA;MAAAljB,2DAAA,CAAA0iB,KAAA;MAAA,MAAAS,QAAA,GAAAnjB,2DAAA;MAAA,OAASA,yDAAA,CAAAmjB,QAAA,CAAAC,qBAAA,CAAsB,KAAK,CAAC;IAAA,EAAC;IAItCpjB,oDAAA,gBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,mBAIC;IAHCA,wDAAA,mBAAAqjB,8DAAA;MAAArjB,2DAAA,CAAA0iB,KAAA;MAAA,MAAAY,QAAA,GAAAtjB,2DAAA;MAAA,OAASA,yDAAA,CAAAsjB,QAAA,CAAAF,qBAAA,CAAsB,QAAQ,CAAC;IAAA,EAAC;IAIzCpjB,oDAAA,kBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,mBAIC;IAHCA,wDAAA,mBAAAujB,8DAAA;MAAAvjB,2DAAA,CAAA0iB,KAAA;MAAA,MAAAc,QAAA,GAAAxjB,2DAAA;MAAA,OAASA,yDAAA,CAAAwjB,QAAA,CAAAJ,qBAAA,CAAsB,MAAM,CAAC;IAAA,EAAC;IAIvCpjB,oDAAA,cACF;IAAAA,0DAAA,EAAS;IAIXA,wDAAA,KAAAyjB,2CAAA,mBAkCM;IAGNzjB,4DAAA,gBAA4B;IAC1BA,wDAAA,KAAA0jB,8CAAA,sBAQS;IACT1jB,wDAAA,KAAA2jB,8CAAA,sBAWS;IACX3jB,0DAAA,EAAM;IAIRA,4DAAA,gBAA+B;IAC7BA,wDAAA,KAAA4jB,2CAAA,mBAMM;IAEN5jB,wDAAA,KAAA6jB,2CAAA,mBAQM;IAEN7jB,wDAAA,KAAA8jB,2CAAA,qBAyEM;IAGN9jB,wDAAA,KAAA+jB,2CAAA,mBAYM;IACR/jB,0DAAA,EAAM;IAGNA,wDAAA,KAAAgkB,2CAAA,oBAgDM;IACRhkB,0DAAA,EAAM;;;;IA3ROA,uDAAA,GAAiC;IAAjCA,wDAAA,SAAAikB,OAAA,CAAAzG,uBAAA,KAAiC;IAetCxd,uDAAA,GAAmC;IAAnCA,wDAAA,aAAAikB,OAAA,CAAA/C,sBAAA,CAAmC;IAKjClhB,uDAAA,GAAiD;IAAjDA,wDAAA,YAAAA,6DAAA,KAAAsf,IAAA,EAAA2E,OAAA,CAAA/C,sBAAA,EAAiD;IAkBnDlhB,uDAAA,GAAoD;IAApDA,wDAAA,YAAAA,6DAAA,KAAA+X,IAAA,EAAAkM,OAAA,CAAAC,kBAAA,YAAoD;IAOpDlkB,uDAAA,GAAuD;IAAvDA,wDAAA,YAAAA,6DAAA,KAAA+X,IAAA,EAAAkM,OAAA,CAAAC,kBAAA,eAAuD;IAOvDlkB,uDAAA,GAAqD;IAArDA,wDAAA,YAAAA,6DAAA,KAAA+X,IAAA,EAAAkM,OAAA,CAAAC,kBAAA,aAAqD;IAUtDlkB,uDAAA,GAA2C;IAA3CA,wDAAA,SAAAikB,OAAA,CAAAE,wBAAA,GAAAxd,MAAA,KAA2C;IAqCzC3G,uDAAA,GAAiC;IAAjCA,wDAAA,SAAAikB,OAAA,CAAAzG,uBAAA,KAAiC;IASjCxd,uDAAA,GAA8B;IAA9BA,wDAAA,SAAAikB,OAAA,CAAAG,aAAA,CAAAzd,MAAA,KAA8B;IAiBhC3G,uDAAA,GAA0D;IAA1DA,wDAAA,SAAAikB,OAAA,CAAA/C,sBAAA,IAAA+C,OAAA,CAAAG,aAAA,CAAAzd,MAAA,OAA0D;IAQ1D3G,uDAAA,GAGT;IAHSA,wDAAA,UAAAikB,OAAA,CAAA/C,sBAAA,IAAA+C,OAAA,CAAAE,wBAAA,GAAAxd,MAAA,OAGT;IAQsC3G,uDAAA,GACjB;IADiBA,wDAAA,YAAAikB,OAAA,CAAAE,wBAAA,GACjB,iBAAAF,OAAA,CAAAI,qBAAA;IAyETrkB,uDAAA,GAA0B;IAA1BA,wDAAA,SAAAikB,OAAA,CAAAK,oBAAA,CAA0B;IAgB5BtkB,uDAAA,GAA8B;IAA9BA,wDAAA,SAAAikB,OAAA,CAAAM,wBAAA,CAA8B;;;;;IA8DhCvkB,4DAAA,WAAyC;IACvCA,oDAAA,GAAsC;IAAAA,0DAAA,EACvC;;;;IADCA,uDAAA,GAAsC;IAAtCA,gEAAA,OAAAwkB,QAAA,CAAAvgB,mBAAA,iBAAsC;;;;;;IAVhDjE,4DAAA,eAIC;IADCA,wDAAA,mBAAAykB,0DAAA;MAAA,MAAAhjB,WAAA,GAAAzB,2DAAA,CAAA0kB,KAAA;MAAA,MAAAC,UAAA,GAAAljB,WAAA,CAAAG,SAAA;MAAA,OAAS5B,yDAAA,CAAA2kB,UAAA,CAAAC,WAAA,EAAmB;IAAA,EAAC;IAE7B5kB,4DAAA,eAAkE;IAAnCA,wDAAA,mBAAA6kB,0DAAA/d,MAAA;MAAA,OAASA,MAAA,CAAAkV,eAAA,EAAwB;IAAA,EAAC;IAC/Dhc,4DAAA,eAAiC;IAE7BA,uDAAA,QAA4B;IAACA,oDAAA,GAC7B;IAAAA,wDAAA,IAAA8kB,2CAAA,oBAEC;IACH9kB,0DAAA,EAAK;IACLA,4DAAA,kBAAyE;IAAjEA,wDAAA,mBAAA+kB,6DAAA;MAAA,MAAAtjB,WAAA,GAAAzB,2DAAA,CAAA0kB,KAAA;MAAA,MAAAC,UAAA,GAAAljB,WAAA,CAAAG,SAAA;MAAA,OAAS5B,yDAAA,CAAA2kB,UAAA,CAAAC,WAAA,EAAmB;IAAA,EAAC;IACnC5kB,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;IAGXA,4DAAA,eAA8B;IAE1BA,uDAAA,SAA4B;IAC5BA,4DAAA,YAAM;IAAAA,oDAAA,0DAAwC;IAAAA,0DAAA,EAAO;;;;IAblDA,uDAAA,GAAoB;IAApBA,wDAAA,CAAA2kB,UAAA,CAAApiB,IAAA,CAAoB;IAAMvC,uDAAA,GAC7B;IAD6BA,gEAAA,MAAA2kB,UAAA,CAAAriB,KAAA,MAC7B;IAAOtC,uDAAA,GAAgC;IAAhCA,wDAAA,SAAA2kB,UAAA,CAAA7hB,GAAA,kBAAgC;IAWpC9C,uDAAA,GAAoB;IAApBA,wDAAA,CAAA2kB,UAAA,CAAApiB,IAAA,CAAoB;;;;;;IAQ/BvC,4DAAA,eAAiE;IAG3DA,uDAAA,aAA2C;IAC3CA,4DAAA,SAAI;IAAAA,oDAAA,+BAAwB;IAAAA,0DAAA,EAAK;IAEnCA,4DAAA,eAAiC;IAE7BA,oDAAA,GAEF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,aAAwB;IAAAA,oDAAA,2CAA8B;IAAAA,0DAAA,EAAI;IAE5DA,4DAAA,gBAAoC;IAC1BA,wDAAA,mBAAAglB,8DAAA;MAAAhlB,2DAAA,CAAAilB,KAAA;MAAA,MAAAC,QAAA,GAAAllB,2DAAA;MAAA,OAASA,yDAAA,CAAAklB,QAAA,CAAAC,yBAAA,EAA2B;IAAA,EAAC;IAC3CnlB,oDAAA,iBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,mBAIC;IAHCA,wDAAA,mBAAAolB,8DAAA;MAAAplB,2DAAA,CAAAilB,KAAA;MAAA,MAAAI,QAAA,GAAArlB,2DAAA;MAAA,OAASA,yDAAA,CAAAqlB,QAAA,CAAAC,2BAAA,EAA6B;IAAA,EAAC;IAIvCtlB,uDAAA,cAGK;IACLA,oDAAA,mBACF;IAAAA,0DAAA,EAAS;;;;IAnBPA,uDAAA,GAEF;IAFEA,gEAAA,mDAAAulB,OAAA,CAAA7G,qBAAA,CAAAC,IAAA,wBAEF;IASE3e,uDAAA,GAAoC;IAApCA,wDAAA,aAAAulB,OAAA,CAAArH,uBAAA,CAAoC;IAKlCle,uDAAA,GAAkD;IAAlDA,wDAAA,YAAAA,6DAAA,IAAAsf,IAAA,EAAAiG,OAAA,CAAArH,uBAAA,EAAkD;;;;;IAsBtDle,uDAAA,aAA2D;;;;;IAC3DA,uDAAA,aAAqD;;;;;IATvDA,4DAAA,gBAOC;IACCA,wDAAA,IAAAwlB,uDAAA,iBAA2D;IAC3DxlB,wDAAA,IAAAylB,uDAAA,iBAAqD;IACvDzlB,0DAAA,EAAO;;;;IAFDA,uDAAA,GAAqB;IAArBA,wDAAA,SAAA0lB,YAAA,kBAAAA,YAAA,CAAAnF,MAAA,CAAqB;IACrBvgB,uDAAA,GAAsB;IAAtBA,wDAAA,WAAA0lB,YAAA,kBAAAA,YAAA,CAAAnF,MAAA,EAAsB;;;;;IAb9BvgB,4DAAA,eAAqC;IAEjCA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IACPA,wDAAA,IAAA2lB,mDAAA,oBAUO;IACT3lB,0DAAA,EAAM;;;;;IAbFA,uDAAA,GACF;IADEA,gEAAA,MAAA4lB,OAAA,CAAA1f,iBAAA,CAAAwf,YAAA,kBAAAA,YAAA,CAAAvf,SAAA,OACF;IAEGnG,uDAAA,GAKP;IALOA,wDAAA,UAAA0lB,YAAA,kBAAAA,YAAA,CAAAjd,MAAA,kBAAAid,YAAA,CAAAjd,MAAA,CAAAhK,EAAA,MAAAmnB,OAAA,CAAAxU,aAAA,KAAAsU,YAAA,kBAAAA,YAAA,CAAAjd,MAAA,kBAAAid,YAAA,CAAAjd,MAAA,CAAA4I,GAAA,MAAAuU,OAAA,CAAAxU,aAAA,KAAAsU,YAAA,kBAAAA,YAAA,CAAApU,QAAA,MAAAsU,OAAA,CAAAxU,aAAA,CAKP;;;;;;;;;;;;IAaIpR,4DAAA,kBAWC;IATCA,wDAAA,mBAAA6lB,oFAAA;MAAA,MAAApkB,WAAA,GAAAzB,2DAAA,CAAA8lB,KAAA;MAAA,MAAAC,aAAA,GAAAtkB,WAAA,CAAAG,SAAA;MAAA,MAAAokB,YAAA,GAAAhmB,2DAAA,IAAA1B,OAAA;MAAA,MAAA2nB,QAAA,GAAAjmB,2DAAA;MAAA,OAASA,yDAAA,CAAAgmB,YAAA,CAAAvnB,EAAA,IAAcwnB,QAAA,CAAAC,eAAA,CAAAF,YAAA,CAAAvnB,EAAA,EAAAsnB,aAAA,CAAAI,KAAA,CAA2C;IAAA,EAAC;IAUnEnmB,4DAAA,WAAM;IAAAA,oDAAA,GAAoB;IAAAA,0DAAA,EAAO;IACjCA,4DAAA,eAA0B;IAAAA,oDAAA,GAAoB;IAAAA,0DAAA,EAAO;;;;;;IATrDA,wDAAA,YAAAA,6DAAA,IAAAomB,IAAA,EAAAC,QAAA,CAAAC,cAAA,CAAAN,YAAA,EAAAD,aAAA,CAAAI,KAAA,IAAAE,QAAA,CAAAC,cAAA,CAAAN,YAAA,EAAAD,aAAA,CAAAI,KAAA,GAKE,UAAAJ,aAAA,CAAA1kB,KAAA;IAGIrB,uDAAA,GAAoB;IAApBA,+DAAA,CAAA+lB,aAAA,CAAAI,KAAA,CAAoB;IACAnmB,uDAAA,GAAoB;IAApBA,+DAAA,CAAA+lB,aAAA,CAAA1kB,KAAA,CAAoB;;;;;IAjBlDrB,4DAAA,eAGC;IACCA,wDAAA,IAAAumB,2DAAA,sBAcS;IACXvmB,0DAAA,EAAM;;;;;IAdmBA,uDAAA,GAA8B;IAA9BA,wDAAA,YAAAwmB,QAAA,CAAAC,kBAAA,CAAAT,YAAA,EAA8B;;;;;IALvDhmB,wDAAA,IAAA0mB,kDAAA,mBAmBM;;;;IAlBH1mB,wDAAA,SAAAgmB,YAAA,CAAAW,SAAA,IAAAX,YAAA,CAAAW,SAAA,CAAAhgB,MAAA,KAAuD;;;;;;IA6B1D3G,4DAAA,kBAIC;IAHCA,wDAAA,mBAAA4mB,qEAAA;MAAA,MAAAnlB,WAAA,GAAAzB,2DAAA,CAAA6mB,KAAA;MAAA,MAAAC,WAAA,GAAArlB,WAAA,CAAAslB,MAAA;MAAA,OAAS/mB,yDAAA,CAAA8mB,WAAA,EAAQ;IAAA,EAAC;IAIlB9mB,uDAAA,QAAsB;IACxBA,0DAAA,EAAS;;;;;;IAJPA,wDAAA,uBAAAgnB,UAAA,QAA4C;IAC5ChnB,wDAAA,UAAAinB,UAAA,CAAe;IAEZjnB,uDAAA,GAAc;IAAdA,wDAAA,CAAAknB,SAAA,CAAc;;;;;;;;;;;;;ADv5Df,MAAOC,oBAAoB;EAoH/B;EACA,IAAIzS,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC0S,cAAc,CAACC,eAAe,EAAE;EAC9C;EAEA;;;EAGAC,gBAAgBA,CAAA;IACd,OAAO,CACL;MACEtmB,KAAK,EAAE,gBAAgB;MACvBuB,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE,aAAa;MACpBR,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACylB,YAAY,CAAC,OAAO,CAAC;MACzCplB,QAAQ,EAAE;KACX,EACD;MACEnB,KAAK,EAAE,gBAAgB;MACvBuB,IAAI,EAAE,cAAc;MACpBD,KAAK,EAAE,aAAa;MACpBR,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACylB,YAAY,CAAC,OAAO,CAAC;MACzCplB,QAAQ,EAAE;KACX,EACD;MACEnB,KAAK,EAAE,YAAY;MACnBuB,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE,YAAY;MACnBR,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC6F,eAAe,EAAE;MACrCxF,QAAQ,EAAE,IAAI,CAACqlB,aAAa;MAC5BtlB,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACElB,KAAK,EAAE,qBAAqB;MAC5BuB,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE,sBAAsB,IAAI,CAACqH,sBAAsB,EAAE,GAAG;MAC7D7H,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACyH,oBAAoB,EAAE;MAC1CpH,QAAQ,EAAE,IAAI,CAACslB,kBAAkB;MACjCvlB,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DnB,KAAK,EACH,IAAI,CAAC4I,sBAAsB,EAAE,GAAG,CAAC,GAC7B;QACEtI,KAAK,EAAE,IAAI,CAACsI,sBAAsB,EAAE;QACpC3I,KAAK,EAAE,gCAAgC;QACvCI,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEJ,KAAK,EAAE,4BAA4B;MACnCuB,IAAI,EAAE,aAAa;MACnBD,KAAK,EAAE,eAAe;MACtBR,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACmhB,uBAAuB,EAAE;MAC7C9gB,QAAQ,EAAE,IAAI,CAACulB,qBAAqB;MACpCxlB,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DnB,KAAK,EACH,IAAI,CAACyc,uBAAuB,GAAG,CAAC,GAC5B;QACEnc,KAAK,EAAE,IAAI,CAACmc,uBAAuB;QACnCxc,KAAK,EAAE,8CAA8C;QACrDI,OAAO,EAAE;OACV,GACD;KACP,EACD;MACEJ,KAAK,EAAE,sBAAsB;MAC7BuB,IAAI,EAAE,gBAAgB;MACtBD,KAAK,EAAE,uBAAuB;MAC9BR,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC6lB,sBAAsB,EAAE;MAC5CxlB,QAAQ,EAAE,IAAI,CAACylB,oBAAoB;MACnC1lB,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACElB,KAAK,EAAE,oBAAoB;MAC3BuB,IAAI,EAAE,kBAAkB;MACxBD,KAAK,EAAE,uBAAuB;MAC9BR,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC+lB,oBAAoB,EAAE;MAC1C1lB,QAAQ,EAAE,IAAI,CAAC2lB,kBAAkB;MACjC5lB,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI;KAC1D,EACD;MACElB,KAAK,EAAE,6BAA6B;MACpCuB,IAAI,EAAE,mBAAmB;MACzBD,KAAK,EAAE,iBAAiB;MACxBR,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACimB,wBAAwB,EAAE;MAC9C5lB,QAAQ,EAAE,IAAI,CAAC6lB,sBAAsB;MACrC9lB,WAAW,EAAE;QAAE,oCAAoC,EAAE;MAAI,CAAE;MAC3DnB,KAAK,EACH,IAAI,CAACknB,aAAa,CAACthB,MAAM,GAAG,CAAC,GACzB;QACEtF,KAAK,EAAE,IAAI,CAAC4mB,aAAa,CAACthB,MAAM;QAChC3F,KAAK,EAAE,cAAc;QACrBI,OAAO,EAAE;OACV,GACD;KACP,CACF;EACH;EAEApD,YACUopB,cAA8B,EAC/Bc,KAAqB,EACpBC,WAA4B,EAC5BC,EAAe,EAChBC,aAAgC,EAChCC,MAAc,EACbC,YAA0B,EAC1BC,MAAqB,EACrBC,GAAsB;IARtB,KAAArB,cAAc,GAAdA,cAAc;IACf,KAAAc,KAAK,GAALA,KAAK;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IAzNb,KAAAxV,QAAQ,GAAc,EAAE;IAExB,KAAAyV,YAAY,GAAwB,IAAI;IACxC,KAAA/U,OAAO,GAAG,IAAI;IAEd,KAAAvC,aAAa,GAAkB,IAAI;IACnC,KAAAuX,eAAe,GAAW,KAAK;IAC/B,KAAAnoB,gBAAgB,GAAgB,IAAI;IACpC,KAAA6V,YAAY,GAAgB,IAAI;IAChC,KAAAnC,UAAU,GAAgC,IAAI;IAC9C,KAAAiC,WAAW,GAAG,KAAK;IACnB,KAAAyS,QAAQ,GAAG,KAAK;IAEhB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAET,KAAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;IAC3B,KAAAC,oBAAoB,GAAG,EAAE,CAAC,CAAC;IAC3B,KAAAC,kBAAkB,GAAG,GAAG,CAAC,CAAC;IACnC,KAAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IACzB,KAAAC,aAAa,GAAG,KAAK,CAAC,CAAC;IACvB,KAAAC,eAAe,GAAG,IAAI,CAAC,CAAC;IAChB,KAAAC,aAAa,GAAiB,IAAI1pB,8CAAY,EAAE;IAExD;IACA,KAAA2pB,aAAa,GAAW,eAAe;IACvC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,eAAe,GAAY,KAAK;IAEhC;IACA,KAAA3S,YAAY,GAAQ,IAAI;IACxB,KAAAM,UAAU,GAAQ,IAAI;IACtB,KAAAsS,aAAa,GAAG,KAAK;IACrB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAA3Q,WAAW,GAAG,KAAK;IACnB,KAAA9B,cAAc,GAAG,IAAI;IACrB,KAAAK,YAAY,GAAG,CAAC;IAChB,KAAAqS,SAAS,GAAQ,IAAI;IAErB;IACA,KAAAxF,aAAa,GAAU,EAAE;IACzB,KAAAsD,qBAAqB,GAAY,KAAK;IACtC,KAAAlK,uBAAuB,GAAW,CAAC;IACnC,KAAAkB,qBAAqB,GAAgB,IAAImL,GAAG,EAAE;IAC9C,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAA1Z,kBAAkB,GAA+B,EAAE;IACnD,KAAAG,gBAAgB,GAAkB,IAAI;IACtC,KAAAjC,cAAc,GAAG,EAAE;IACnB,KAAA0G,iBAAiB,GAAQ,IAAI;IAE7B;IACA,KAAAwS,aAAa,GAAG,KAAK;IACrB,KAAAlhB,WAAW,GAAG,EAAE;IAChB,KAAAyB,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAtB,aAAa,GAAU,EAAE;IAEzB;IACA,KAAA+gB,kBAAkB,GAAG,KAAK;IAC1B,KAAAte,cAAc,GAAU,EAAE;IAC1B,KAAA4gB,kBAAkB,GAAG,KAAK;IAC1B,KAAAjX,kBAAkB,GAA+B,EAAE;IACnD,KAAAnI,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzD,KAAA0F,iBAAiB,GAA+B,EAAE;IAClD,KAAAC,cAAc,GAA+B,EAAE;IAC/C,KAAA7C,SAAS,GAA+B,EAAE;IAE1C;IACA,KAAAuc,gBAAgB,GAAG,KAAK;IACxB,KAAA9M,iBAAiB,GAAQ,IAAI;IAC7B,KAAAE,qBAAqB,GAAa,EAAE;IACpC,KAAA1B,sBAAsB,GAAU,EAAE;IAClC,KAAA4B,YAAY,GAAG,KAAK;IACpB,KAAAD,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAA6G,kBAAkB,GAAG,KAAK;IAC1B,KAAAhD,sBAAsB,GAAG,KAAK;IAC9B,KAAAjD,eAAe,GAAG,KAAK;IACvB,KAAAC,uBAAuB,GAAG,KAAK;IAC/B,KAAAoG,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAA9C,kBAAkB,GAAG,IAAI;IACzB,KAAAM,mBAAmB,GAAG,IAAI;IAC1B,KAAAK,cAAc,GAAG,IAAI;IAErB;IACA,KAAAnJ,eAAe,GAAG,KAAK;IACvB,KAAAxB,WAAW,GAAG,YAAY;IAC1B,KAAAsB,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAkR,WAAW,GAAsB,IAAIC,GAAG,EAAE;IAC1C,KAAA/mB,iBAAiB,GAAW,QAAQ;IACpC,KAAAgnB,gBAAgB,GAAS,IAAIC,IAAI,EAAE;IACnC,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAApnB,gBAAgB,GAAG,KAAK;IACxB,KAAAqnB,mBAAmB,GAAG,KAAK;IAC3B,KAAA1C,oBAAoB,GAAG,KAAK;IAC5B,KAAAE,kBAAkB,GAAG,KAAK;IAC1B,KAAAE,sBAAsB,GAAG,KAAK;IAC9B,KAAAuC,WAAW,GAAU,EAAE;IACvB,KAAAtC,aAAa,GAAU,EAAE;IACzB,KAAAuC,aAAa,GAAW,MAAM,CAAC,CAAC;IAChC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,gBAAgB,GAAG,KAAK;IA6XhB,KAAAC,iBAAiB,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,GAAG,CAAC,CAAC;IACpB,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IAoExC;IACS,KAAAC,aAAa,GAAG;MACvBC,aAAa,EAAEA,CAAA,KAAY,IAAI,CAACC,WAAW,CAAC,OAAO,CAAC;MACpDC,QAAQ,EAAEA,CAAA,KAAY,IAAI,CAACD,WAAW,CAAC,MAAM,CAAC;MAC9CE,WAAW,EAAEA,CAAA,KAAY,IAAI,CAACF,WAAW,CAAC,OAAO,CAAC;MAClD3mB,WAAW,EAAG8mB,KAAa,IAAU;QACnC,IAAI,CAAC9B,aAAa,GAAG8B,KAAK;QAC1B,IAAI,CAAC7B,iBAAiB,GAAG,KAAK;QAC9B8B,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEF,KAAK,CAAC;MAC3C,CAAC;MACDjiB,cAAc,EAAEA,CAAA,KAAW;QACzB,IAAI,CAACse,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;MACpD,CAAC;MACD8D,SAAS,EAAEA,CAAA,KAAW;QACpB,IAAI,CAACN,WAAW,CAAC,QAAQ,CAAC;QAC1B,IAAI,CAAC,IAAI,CAACzD,aAAa,EAAE,IAAI,CAAC7hB,WAAW,EAAE;MAC7C,CAAC;MACD6lB,cAAc,EAAEA,CAAA,KAAY,IAAI,CAACP,WAAW,CAAC,QAAQ,CAAC;MACtDQ,oBAAoB,EAAEA,CAAA,KAAW;QAC/B,IAAI,CAAClH,wBAAwB,GAAG,CAAC,IAAI,CAACA,wBAAwB;MAChE,CAAC;MACDmH,eAAe,EAAEA,CAAA,KAAW;QAC1B,IAAI,CAACpB,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;MACtD,CAAC;MACDqB,YAAY,EAAEA,CAAA,KAAW;QACvB,IAAI,CAAC1S,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;MAC9C,CAAC;MACD2S,gBAAgB,EAAEA,CAAA,KAAW;QAC3B,IAAI,CAAChE,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;MACxD,CAAC;MACDiE,cAAc,EAAEA,CAAA,KAAW;QACzB,IAAI,CAAC/D,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;MACpD,CAAC;MACDgE,kBAAkB,EAAEA,CAAA,KAAW;QAC7B,IAAI,CAAC9D,sBAAsB,GAAG,CAAC,IAAI,CAACA,sBAAsB;MAC5D;KACD;IAED;IACA,KAAA+D,mBAAmB,GAAG,IAAI,CAAChB,aAAa,CAACC,aAAa;IACtD,KAAAgB,cAAc,GAAG,IAAI,CAACjB,aAAa,CAACG,QAAQ;IAC5C,KAAAe,iBAAiB,GAAG,IAAI,CAAClB,aAAa,CAACI,WAAW;IAClD,KAAA7mB,WAAW,GAAG,IAAI,CAACymB,aAAa,CAACzmB,WAAW;IAC5C,KAAAiF,oBAAoB,GAAG,IAAI,CAACwhB,aAAa,CAAC5hB,cAAc;IACxD,KAAAxB,eAAe,GAAG,IAAI,CAACojB,aAAa,CAACQ,SAAS;IAC9C,KAAAxoB,oBAAoB,GAAG,IAAI,CAACgoB,aAAa,CAACS,cAAc;IACxD,KAAAlK,0BAA0B,GAAG,IAAI,CAACyJ,aAAa,CAACU,oBAAoB;IACpE,KAAA7nB,qBAAqB,GAAG,IAAI,CAACmnB,aAAa,CAACW,eAAe;IAC1D,KAAA9S,kBAAkB,GAAG,IAAI,CAACmS,aAAa,CAACY,YAAY;IACpD,KAAAhE,sBAAsB,GAAG,IAAI,CAACoD,aAAa,CAACa,gBAAgB;IAC5D,KAAA/D,oBAAoB,GAAG,IAAI,CAACkD,aAAa,CAACc,cAAc;IACxD,KAAA9D,wBAAwB,GAAG,IAAI,CAACgD,aAAa,CAACe,kBAAkB;IAqEhE;IACS,KAAAI,mBAAmB,GAAG;MAC7B/sB,QAAQ,EAAEA,CAAA,KAAW;QACnB,IAAI,CAACqqB,YAAY,GAAG,KAAK;QACzB,IAAI,CAACjB,YAAY,CAACppB,QAAQ,CAAC,0CAA0C,CAAC;MACxE,CAAC;MACDgtB,YAAY,EAAEA,CAAA,KAAW;QACvB,IAAI,CAAC3C,YAAY,GAAG,KAAK;QACzB,IAAI,CAACjB,YAAY,CAACppB,QAAQ,CAAC,0CAA0C,CAAC;MACxE;KACD;IAED;IACA,KAAAiG,sBAAsB,GAAG,IAAI,CAAC8mB,mBAAmB,CAAC/sB,QAAQ;IAC1D,KAAAoG,0BAA0B,GAAG,IAAI,CAAC2mB,mBAAmB,CAACC,YAAY;IA2GlE;IACS,KAAAC,cAAc,GAAG;MACxBlmB,iBAAiB,EAAGC,SAAoC,IACtD,IAAI,CAACihB,cAAc,CAAClhB,iBAAiB,CAACC,SAAS,CAAC;MAClDxF,gBAAgB,EAAGC,UAAqC,IACtD,IAAI,CAACwmB,cAAc,CAACzmB,gBAAgB,CAACC,UAAU,CAAC;MAClDoI,iBAAiB,EAAG7C,SAAoC,IACtD,IAAI,CAACihB,cAAc,CAACpe,iBAAiB,CAAC7C,SAAS,CAAC;MAClDuM,oBAAoB,EAAG2Z,KAAa,IAClC,IAAI,CAACjF,cAAc,CAAC1U,oBAAoB,CAAC,IAAI,CAACO,QAAQ,EAAEoZ,KAAK,CAAC;MAChEC,cAAc,EAAGhuB,OAAmC,IAClD,IAAI,CAAC8oB,cAAc,CAACkF,cAAc,CAAChuB,OAAO,CAAC;MAC7CuK,QAAQ,EAAGvK,OAAmC,IAC5C,IAAI,CAAC8oB,cAAc,CAACve,QAAQ,CAACvK,OAAO,CAAC;MACvCwK,cAAc,EAAGxK,OAAmC,IAClD,IAAI,CAAC8oB,cAAc,CAACte,cAAc,CAACxK,OAAO,CAAC;MAC7CoT,kBAAkB,EAAGpT,OAAmC,IACtD,IAAI,CAAC8oB,cAAc,CAAC1V,kBAAkB,CAACpT,OAAO,CAAC;MACjDmT,uBAAuB,EAAGnT,OAAmC,IAC3D,IAAI,CAAC8oB,cAAc,CAAC3V,uBAAuB,CAACnT,OAAO,CAAC;MACtDuS,iBAAiB,EAAGwb,KAAa,IAC/B,IAAI,CAACjF,cAAc,CAACvW,iBAAiB,CAACwb,KAAK,CAAC;MAC9C7a,mBAAmB,EAAG+a,OAAe,IACnC,IAAI,CAACnF,cAAc,CAAC5V,mBAAmB,CAAC+a,OAAO,CAAC;MAClDva,WAAW,EAAG1T,OAAmC,IAC/C,IAAI,CAAC8oB,cAAc,CAACpV,WAAW,CAAC1T,OAAO,CAAC;MAC1CkuB,mBAAmB,EAAGluB,OAAmC,IACvD,IAAI,CAAC8oB,cAAc,CAACoF,mBAAmB,CAACluB,OAAO,EAAE,IAAI,CAAC8S,aAAa;KACtE;IAED;IACA,KAAAlL,iBAAiB,GAAG,IAAI,CAACkmB,cAAc,CAAClmB,iBAAiB;IACzD,KAAAvF,gBAAgB,GAAG,IAAI,CAACyrB,cAAc,CAACzrB,gBAAgB;IACvD,KAAAqI,iBAAiB,GAAG,IAAI,CAACojB,cAAc,CAACpjB,iBAAiB;IACzD,KAAA0J,oBAAoB,GAAG,IAAI,CAAC0Z,cAAc,CAAC1Z,oBAAoB;IAC/D,KAAA4Z,cAAc,GAAG,IAAI,CAACF,cAAc,CAACE,cAAc;IACnD,KAAAzjB,QAAQ,GAAG,IAAI,CAACujB,cAAc,CAACvjB,QAAQ;IACvC,KAAAC,cAAc,GAAG,IAAI,CAACsjB,cAAc,CAACtjB,cAAc;IACnD,KAAA4I,kBAAkB,GAAG,IAAI,CAAC0a,cAAc,CAAC1a,kBAAkB;IAC3D,KAAAD,uBAAuB,GAAG,IAAI,CAAC2a,cAAc,CAAC3a,uBAAuB;IACrE,KAAAZ,iBAAiB,GAAG,IAAI,CAACub,cAAc,CAACvb,iBAAiB;IACzD,KAAAW,mBAAmB,GAAG,IAAI,CAAC4a,cAAc,CAAC5a,mBAAmB;IAC7D,KAAAQ,WAAW,GAAG,IAAI,CAACoa,cAAc,CAACpa,WAAW;IAC7C,KAAAwa,mBAAmB,GAAG,IAAI,CAACJ,cAAc,CAACI,mBAAmB;IAijB7D;IACS,KAAAC,WAAW,GAAG;MACrBC,UAAU,EAAEA,CAAA,KAAW;QACrB,IAAI,CAAC1T,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;QACpC,IAAI,CAACyT,WAAW,CAACE,WAAW,EAAE;QAC9B,IAAI,CAACpE,YAAY,CAACppB,QAAQ,CACxB,IAAI,CAAC6Z,WAAW,GAAG,sBAAsB,GAAG,mBAAmB,CAChE;MACH,CAAC;MACD4T,WAAW,EAAEA,CAAA,KAAW;QACtB,IAAI,CAAC1V,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;QAC1C,IAAI,CAACuV,WAAW,CAACE,WAAW,EAAE;QAC9B,IAAI,CAACpE,YAAY,CAACppB,QAAQ,CACxB,IAAI,CAAC+X,cAAc,GAAG,gBAAgB,GAAG,mBAAmB,CAC7D;MACH,CAAC;MACD2V,cAAc,EAAEA,CAAA,KAAW;QACzB,IAAI,CAAC5T,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;MAC9C,CAAC;MACD0T,WAAW,EAAEA,CAAA,KAAW;QACtB,IAAI,CAACvF,cAAc,CAAC0F,WAAW,CAC7B,IAAI,CAAC1V,UAAU,EAAE3Y,EAAE,EACnB,CAAC,IAAI,CAACua,WAAW,EACjB,IAAI,CAAC9B,cAAc,CACpB,CAAC6V,SAAS,CAAC;UACVluB,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;UACdgL,KAAK,EAAEA,CAAA,KAAK,CAAE;SACf,CAAC;MACJ;KACD;IAED;IACA,KAAAwO,cAAc,GAAG,IAAI,CAACoU,WAAW,CAACC,UAAU;IAC5C,KAAA5U,eAAe,GAAG,IAAI,CAAC2U,WAAW,CAACG,WAAW;IACtC,KAAAI,eAAe,GAAG,IAAI,CAACP,WAAW,CAACE,WAAW;IAEtD;IACS,KAAAM,YAAY,GAAG;MACtBC,SAAS,EAAEA,CAAA,KAAW;QACpB,IAAI,CAAC3V,YAAY,GAAG,CAAC;QACrB,IAAI,CAACqS,SAAS,GAAGuD,WAAW,CAAC,MAAK;UAChC,IAAI,CAAC5V,YAAY,EAAE;UACnB,IAAI,IAAI,CAACA,YAAY,KAAK,CAAC,IAAI,IAAI,CAACE,WAAW,KAAK,YAAY,EAAE;YAChE,IAAI,CAACA,WAAW,GAAG,WAAW;;QAElC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACD2V,QAAQ,EAAEA,CAAA,KAAW;QACnB,IAAI,IAAI,CAACxD,SAAS,EAAE;UAClByD,aAAa,CAAC,IAAI,CAACzD,SAAS,CAAC;UAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;MAEzB,CAAC;MACD0D,SAAS,EAAEA,CAAA,KAAW;QACpB,IAAI,CAAC/V,YAAY,GAAG,CAAC;MACvB;KACD;IAED;IACQ,KAAAgW,oBAAoB,GAAG,IAAI,CAACN,YAAY,CAACC,SAAS;IAClD,KAAAM,mBAAmB,GAAG,IAAI,CAACP,YAAY,CAACG,QAAQ;IAChD,KAAAK,oBAAoB,GAAG,IAAI,CAACR,YAAY,CAACK,SAAS;IA6C1D;IACS,KAAAI,uBAAuB,GAAG;MACjCC,QAAQ,EAAEA,CAAA,KAAW;QACnB,IAAI,CAAC7K,iBAAiB,EAAE;MAC1B,CAAC;MACD8K,WAAW,EAAEA,CAAA,KAAW;QACtB,IAAI,CAACpQ,uBAAuB,GAAG,IAAI,CAAC4G,aAAa,CAAChlB,MAAM,CACrDyuB,CAAC,IAAK,CAACA,CAAC,CAACtN,MAAM,CACjB,CAAC5Z,MAAM;MACV,CAAC;MACDmnB,WAAW,EAAEA,CAAA,KAAY;QACvB,OAAO,IAAI,CAAC1J,aAAa,CAAC,CAAC;MAC7B,CAAC;;MACD2J,SAAS,EAAG3uB,MAAiC,IAAU;QACrD;MAAA;KAEH;IAED;IACA,KAAA4hB,qBAAqB,GAAG,IAAI,CAAC0M,uBAAuB,CAACC,QAAQ;IACrD,KAAAK,uBAAuB,GAAG,IAAI,CAACN,uBAAuB,CAACE,WAAW;IAC1E,KAAAzJ,wBAAwB,GAAG,IAAI,CAACuJ,uBAAuB,CAACI,WAAW;IAmPnE;IACQ,KAAAG,kBAAkB,GAAG;MAC3BC,WAAW,EAAE;QAAE3rB,IAAI,EAAE,gBAAgB;QAAEa,KAAK,EAAE;MAAe,CAAE;MAC/D+qB,cAAc,EAAE;QAAE5rB,IAAI,EAAE,kBAAkB;QAAEa,KAAK,EAAE;MAAgB,CAAE;MACrEgrB,gBAAgB,EAAE;QAAE7rB,IAAI,EAAE,cAAc;QAAEa,KAAK,EAAE;MAAiB,CAAE;MACpEirB,WAAW,EAAE;QAAE9rB,IAAI,EAAE,oBAAoB;QAAEa,KAAK,EAAE;MAAc,CAAE;MAClEkrB,aAAa,EAAE;QAAE/rB,IAAI,EAAE,cAAc;QAAEa,KAAK,EAAE;MAAiB,CAAE;MACjEmrB,MAAM,EAAE;QAAEhsB,IAAI,EAAE,YAAY;QAAEa,KAAK,EAAE;MAAe;KACrD;IAED,KAAAwd,sBAAsB,GAAIza,SAAwB,IAChD,IAAI,CAACihB,cAAc,CAACzmB,gBAAgB,CAACwF,SAAS,CAAC;IAEjD,KAAAwa,mBAAmB,GAAIpiB,IAAY,IACjC,IAAI,CAAC0vB,kBAAkB,CAAC1vB,IAA4C,CAAC,EACjEgE,IAAI,IAAI,aAAa;IAE3B,KAAAme,oBAAoB,GAAIniB,IAAY,IAClC,IAAI,CAAC0vB,kBAAkB,CAAC1vB,IAA4C,CAAC,EACjE6E,KAAK,IAAI,eAAe;IAE9B,KAAAihB,qBAAqB,GAAG,CAACgI,KAAa,EAAEmC,YAAiB,KACvDA,YAAY,CAAC/vB,EAAE;IAqMjB;IACQ,KAAAgwB,YAAY,GAAG;MACrBC,MAAM,EAAE;QACNC,IAAI,EAAE,UAAU;QAChBvrB,KAAK,EAAE,gBAAgB;QACvBb,IAAI,EAAE,eAAe;QACrBe,KAAK,EAAE,UAAU;QACjBC,WAAW,EAAE;OACd;MACDqrB,OAAO,EAAE;QACPD,IAAI,EAAE,YAAY;QAClBvrB,KAAK,EAAE,eAAe;QACtBb,IAAI,EAAE,eAAe;QACrBe,KAAK,EAAE,YAAY;QACnBC,WAAW,EAAE;OACd;MACDsrB,IAAI,EAAE;QACJF,IAAI,EAAE,QAAQ;QACdvrB,KAAK,EAAE,iBAAiB;QACxBb,IAAI,EAAE,cAAc;QACpBe,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE;OACd;MACDurB,IAAI,EAAE;QACJH,IAAI,EAAE,QAAQ;QACdvrB,KAAK,EAAE,cAAc;QACrBb,IAAI,EAAE,qBAAqB;QAC3Be,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE;;KAEhB;IAUD;IACQ,KAAAwrB,WAAW,GAAG,CACpB;MACEjsB,GAAG,EAAE,eAAe;MACpBQ,KAAK,EAAE,YAAY;MACnBF,KAAK,EAAE,WAAW;MAClBmB,UAAU,EAAE;KACb,EACD;MACEzB,GAAG,EAAE,gBAAgB;MACrBQ,KAAK,EAAE,MAAM;MACbF,KAAK,EAAE,WAAW;MAClBmB,UAAU,EAAE;KACb,EACD;MACEzB,GAAG,EAAE,iBAAiB;MACtBQ,KAAK,EAAE,MAAM;MACbF,KAAK,EAAE,WAAW;MAClBmB,UAAU,EAAE;KACb,EACD;MACEzB,GAAG,EAAE,eAAe;MACpBQ,KAAK,EAAE,MAAM;MACbF,KAAK,EAAE,WAAW;MAClBmB,UAAU,EAAE;KACb,CACF;IAOD;IACQ,KAAAyqB,WAAW,GAAG,CACpB;MACElsB,GAAG,EAAE,YAAY;MACjBzE,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACisB,mBAAmB;MACpChoB,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE,cAAc;MACpBqiB,WAAW,EAAEA,CAAA,KAAO,IAAI,CAAC0F,mBAAmB,GAAG;KAChD,EACD;MACExnB,GAAG,EAAE,aAAa;MAClBzE,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACupB,oBAAoB;MACrCtlB,KAAK,EAAE,uBAAuB;MAC9BC,IAAI,EAAE,gBAAgB;MACtBqiB,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACgD,oBAAoB,GAAG;KACjD,EACD;MACE9kB,GAAG,EAAE,WAAW;MAChBzE,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACypB,kBAAkB;MACnCxlB,KAAK,EAAE,uBAAuB;MAC9BC,IAAI,EAAE,kBAAkB;MACxBqiB,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACkD,kBAAkB,GAAG;KAC/C,EACD;MACEhlB,GAAG,EAAE,eAAe;MACpBzE,IAAI,EAAEA,CAAA,KAAM,IAAI,CAAC2pB,sBAAsB;MACvC1lB,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE,mBAAmB;MACzBqiB,WAAW,EAAEA,CAAA,KAAO,IAAI,CAACoD,sBAAsB,GAAG;KACnD,CACF;IAOD;IACS,KAAAiH,aAAa,GAAG;MACvBC,OAAO,EAAGC,MAAc,IACtB,IAAI,CAACV,YAAY,CAACU,MAAwC,CAAC,EAAER,IAAI,IACjE,SAAS;MACXS,QAAQ,EAAGD,MAAc,IACvB,IAAI,CAACV,YAAY,CAACU,MAAwC,CAAC,EAAE/rB,KAAK,IAClE,eAAe;MACjBisB,OAAO,EAAGF,MAAc,IACtB,IAAI,CAACV,YAAY,CAACU,MAAwC,CAAC,EAAE5sB,IAAI,IACjE,wBAAwB;MAC1B+sB,cAAc,EAAG1uB,UAAuB,IAAY;QAClD,IAAI,CAACA,UAAU,EAAE,OAAO,WAAW;QACnC,OAAO,IAAI,CAACwmB,cAAc,CAACzmB,gBAAgB,CAACC,UAAU,CAAC;MACzD,CAAC;MACD2uB,cAAc,EAAEA,CAAA,KACdC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACxF,WAAW,CAACyF,MAAM,EAAE,CAAC,CAACtwB,MAAM,CAAEuwB,IAAI,IAAKA,IAAI,CAACjvB,QAAQ,CAAC,CAClEiG,MAAM;MACXipB,SAAS,EAAEA,CAACvD,KAAa,EAAEsD,IAAU,KACnCA,IAAI,CAAClxB,EAAE,IAAI4tB,KAAK,CAACwD,QAAQ;KAC5B;IAED;IACA,KAAA9rB,aAAa,GAAG,IAAI,CAACkrB,aAAa,CAACC,OAAO;IAC1C,KAAAprB,cAAc,GAAG,IAAI,CAACmrB,aAAa,CAACG,QAAQ;IAC5C,KAAAU,aAAa,GAAG,IAAI,CAACb,aAAa,CAACI,OAAO;IAC1C,KAAAC,cAAc,GAAG,IAAI,CAACL,aAAa,CAACK,cAAc;IAClD,KAAArrB,mBAAmB,GAAG,IAAI,CAACgrB,aAAa,CAACM,cAAc;IACvD,KAAAQ,aAAa,GAAG,IAAI,CAACd,aAAa,CAACW,SAAS;IAiC5C;IACS,KAAAI,cAAc,GAAG;MACxB3jB,UAAU,EAAG/N,OAAY,IACvB,IAAI,CAAC0xB,cAAc,CAACC,QAAQ,CAAC3xB,OAAO,CAAC,GACjC,kBAAkB,GAClB,kBAAkB;MACxBgO,iBAAiB,EAAGhO,OAAY,IAC9B,IAAI,CAAC0xB,cAAc,CAACC,QAAQ,CAAC3xB,OAAO,CAAC,GAAG,aAAa,GAAG,UAAU;MACpE4xB,OAAO,EAAG5xB,OAAY,IACpBA,OAAO,CAACmK,MAAM,EAAEhK,EAAE,KAAK,IAAI,CAAC2S,aAAa;MAC3C6e,QAAQ,EAAG3xB,OAAY,IAAcA,OAAO,CAAC2xB,QAAQ,IAAI,KAAK;MAC9DE,SAAS,EAAG7xB,OAAY,IAAU;QAChC,IAAI,CAACiS,gBAAgB,GAAGjS,OAAO,CAACG,EAAE;QAClC,IAAI,CAAC6P,cAAc,GAAGhQ,OAAO,CAAC+H,OAAO;MACvC,CAAC;MACD+pB,UAAU,EAAEA,CAAA,KAAW;QACrB,IAAI,CAAC7f,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACjC,cAAc,GAAG,EAAE;MAC1B,CAAC;MACD+hB,QAAQ,EAAGC,SAAiB,IAAW,IAAI,CAACN,cAAc,CAACI,UAAU,EAAE;MACvE3hB,cAAc,EAAEA,CAAC8hB,KAAoB,EAAED,SAAiB,KAAU;QAChE,IAAIC,KAAK,CAACztB,GAAG,KAAK,OAAO,IAAI,CAACytB,KAAK,CAACC,QAAQ,EAAE;UAC5CD,KAAK,CAACE,cAAc,EAAE;UACtB,IAAI,CAACT,cAAc,CAACK,QAAQ,CAACC,SAAS,CAAC;SACxC,MAAM,IAAIC,KAAK,CAACztB,GAAG,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACktB,cAAc,CAACI,UAAU,EAAE;;MAEpC,CAAC;MACDM,SAAS,EAAGpyB,OAAY,IAAU;QAChC,IAAI,CAACmP,SAAS,CAACnP,OAAO,CAACG,EAAE,CAAC,GAAG,IAAI;QACjCK,UAAU,CAAC,MAAK;UACd,IAAI,CAAC2O,SAAS,CAACnP,OAAO,CAACG,EAAE,CAAC,GAAG,KAAK;UAClC,IAAI,CAAC6R,cAAc,CAAChS,OAAO,CAACG,EAAE,CAAC,GAAG,KAAK;QACzC,CAAC,EAAE,IAAI,CAAC;MACV;KACD;IAED;IACA,KAAA4N,UAAU,GAAG,IAAI,CAAC2jB,cAAc,CAAC3jB,UAAU;IAC3C,KAAAC,iBAAiB,GAAG,IAAI,CAAC0jB,cAAc,CAAC1jB,iBAAiB;IACzD,KAAAC,cAAc,GAAG,IAAI,CAACyjB,cAAc,CAACE,OAAO;IAC5C,KAAA1iB,eAAe,GAAG,IAAI,CAACwiB,cAAc,CAACC,QAAQ;IAC9C,KAAA9kB,gBAAgB,GAAG,IAAI,CAAC6kB,cAAc,CAACG,SAAS;IAChD,KAAAvhB,iBAAiB,GAAG,IAAI,CAACohB,cAAc,CAACI,UAAU;IAClD,KAAArhB,eAAe,GAAG,IAAI,CAACihB,cAAc,CAACK,QAAQ;IAC9C,KAAA5hB,cAAc,GAAG,IAAI,CAACuhB,cAAc,CAACvhB,cAAc;IACnD,KAAApB,gBAAgB,GAAG,IAAI,CAAC2iB,cAAc,CAACU,SAAS;IAEhD;IACS,KAAAC,SAAS,GAAG;MACnB7sB,cAAc,EAAGqrB,MAAc,IAAY;QACzC,MAAMyB,MAAM,GAAG;UACbC,SAAS,EAAE,gBAAgB;UAC3BC,MAAM,EAAE,cAAc;UACtBC,QAAQ,EAAE;SACX;QACD,OAAOH,MAAM,CAACzB,MAA6B,CAAC,IAAI,eAAe;MACjE,CAAC;MACD6B,WAAW,EAAGzyB,IAAY,IACxBA,IAAI,KAAK,OAAO,GAAG,cAAc,GAAG,cAAc;MACpD0yB,cAAc,EAAGzyB,QAAgB,IAAY;QAC3C,IAAI,CAACA,QAAQ,EAAE,OAAO,OAAO;QAC7B,MAAM0yB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC5yB,QAAQ,GAAG,EAAE,CAAC;QACzC,MAAM+tB,OAAO,GAAG/tB,QAAQ,GAAG,EAAE;QAC7B,OAAO,GAAG0yB,OAAO,CAACrB,QAAQ,EAAE,CAACwB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI9E,OAAO,CACrDsD,QAAQ,EAAE,CACVwB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACvB,CAAC;MACDC,UAAU,EAAGnrB,SAAwB,IACnC,IAAI,CAACihB,cAAc,CAACpe,iBAAiB,CAAC7C,SAAS,CAAC;MAClDypB,SAAS,EAAEA,CAACvD,KAAa,EAAEkF,IAAS,KAClCA,IAAI,CAAC9yB,EAAE,IAAI4tB,KAAK,CAACwD,QAAQ;KAC5B;IAED;IACA,KAAA2B,kBAAkB,GAAG,IAAI,CAACb,SAAS,CAAC7sB,cAAc;IAClD,KAAA2tB,eAAe,GAAG,IAAI,CAACd,SAAS,CAACK,WAAW;IAC5C,KAAA1Z,kBAAkB,GAAG,IAAI,CAACqZ,SAAS,CAACM,cAAc;IAClD,KAAAS,cAAc,GAAG,IAAI,CAACf,SAAS,CAACW,UAAU;IAC1C,KAAAK,aAAa,GAAG,IAAI,CAAChB,SAAS,CAACf,SAAS;IAgBxC;IACS,KAAAgC,cAAc,GAAG;MACxBnL,kBAAkB,EAAGnoB,OAAY,IAAYA,OAAO,CAACqoB,SAAS,IAAI,EAAE;MACpET,eAAe,EAAEA,CAACoK,SAAiB,EAAEnK,KAAa,KAAU,CAAE,CAAC;MAC/DG,cAAc,EAAEA,CAAChoB,OAAY,EAAE6nB,KAAa,KAAc,KAAK;MAC/DhJ,2BAA2B,EAAEA,CAAA,KAC3B,IAAI,CAACC,qBAAqB,CAACzW,MAAM,KAAK,IAAI,CAAC+U,sBAAsB,CAAC/U,MAAM;MAC1E0T,sBAAsB,EAAEA,CAAA,KAAW;QACjC,IAAI,CAAC+C,qBAAqB,GAAG,IAAI,CAAC1B,sBAAsB,CAACmW,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACrzB,EAAE,CAAC;MAC3E,CAAC;MACDgc,wBAAwB,EAAEA,CAAA,KAAW;QACnC,IAAI,CAAC2C,qBAAqB,GAAG,EAAE;MACjC,CAAC;MACD;MACA2U,gBAAgB,EAAEA,CAAC1F,KAAa,EAAE/tB,OAAY,KAC5CA,OAAO,CAACG,EAAE,IAAI4tB,KAAK,CAACwD,QAAQ;KAC/B;IAED;IACA,KAAApJ,kBAAkB,GAAG,IAAI,CAACmL,cAAc,CAACnL,kBAAkB;IAC3D,KAAAP,eAAe,GAAG,IAAI,CAAC0L,cAAc,CAAC1L,eAAe;IACrD,KAAAI,cAAc,GAAG,IAAI,CAACsL,cAAc,CAACtL,cAAc;IACnD,KAAAnJ,2BAA2B,GAAG,IAAI,CAACyU,cAAc,CAACzU,2BAA2B;IAC7E,KAAA9C,sBAAsB,GAAG,IAAI,CAACuX,cAAc,CAACvX,sBAAsB;IACnE,KAAAI,wBAAwB,GAAG,IAAI,CAACmX,cAAc,CAACnX,wBAAwB;IACvE;IACA,KAAAsX,gBAAgB,GAAG,IAAI,CAACH,cAAc,CAACG,gBAAgB;IAEvD;IACS,KAAAC,uBAAuB,GAAG;MACjCC,eAAe,EAAGC,cAAsB,IAAU;QAChD,MAAM7F,KAAK,GAAG,IAAI,CAACjP,qBAAqB,CAAC+U,OAAO,CAACD,cAAc,CAAC;QAChE7F,KAAK,GAAG,CAAC,CAAC,GACN,IAAI,CAACjP,qBAAqB,CAACgV,MAAM,CAAC/F,KAAK,EAAE,CAAC,CAAC,GAC3C,IAAI,CAACjP,qBAAqB,CAACiV,IAAI,CAACH,cAAc,CAAC;MACrD,CAAC;MACDI,UAAU,EAAGJ,cAAsB,IACjC,IAAI,CAAC9U,qBAAqB,CAACmV,QAAQ,CAACL,cAAc,CAAC;MACrDM,eAAe,EAAG9J,YAAiB,IACjCA,YAAY,CAAChgB,KAAK,IAAI,kCAAkC;MAC1D+pB,cAAc,EAAG/J,YAAiB,IAChCA,YAAY,CAACgK,IAAI,IAAI,cAAc;MACrCC,OAAO,EAAEA,CAAA,KAAW;QAClB,IAAI,CAACrV,YAAY,GAAG,IAAI;QACxBxe,UAAU,CAAC,MAAK;UACd,IAAI,CAACwe,YAAY,GAAG,KAAK;UACzB,IAAI,CAACxB,iBAAiB,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV;KACD;IAED;IACA,KAAAf,2BAA2B,GAAG,IAAI,CAACiX,uBAAuB,CAACC,eAAe;IAC1E,KAAA7W,sBAAsB,GAAG,IAAI,CAAC4W,uBAAuB,CAACM,UAAU;IAChE,KAAAjX,2BAA2B,GAAG,IAAI,CAAC2W,uBAAuB,CAACQ,eAAe;IAC1E,KAAAlX,0BAA0B,GAAG,IAAI,CAAC0W,uBAAuB,CAACS,cAAc;IACxE,KAAA3V,cAAc,GAAG,IAAI,CAACkV,uBAAuB,CAACW,OAAO;IAErD;IAEA;IACS,KAAAC,mBAAmB,GAAG;MAC7BjZ,eAAe,EAAEA,CAAA,KAAW;QAC1B,IAAI,CAACZ,gBAAgB,GAAG,IAAI;MAC9B,CAAC;MACD8Z,YAAY,EAAEA,CAAA,KAAW;QACvB;MAAA,CACD;MACD9E,SAAS,EAAG3uB,MAAc,IAAU;QAClC,IAAI,CAAC8kB,kBAAkB,GAAG9kB,MAAM;MAClC,CAAC;MACD0zB,YAAY,EAAEA,CAAA,KAAW;QACvB,IAAI,CAAChJ,sBAAsB,GAAG,KAAK;MACrC;KACD;IAED;IACA,KAAAnQ,eAAe,GAAG,IAAI,CAACiZ,mBAAmB,CAACjZ,eAAe;IAC1D,KAAAiI,wBAAwB,GAAG,IAAI,CAACgR,mBAAmB,CAACC,YAAY;IAChE,KAAAzP,qBAAqB,GAAG,IAAI,CAACwP,mBAAmB,CAAC7E,SAAS;IAC1D;IAEA;IAEA;IACS,KAAAgF,aAAa,GAAG;MACvBC,OAAO,EAAGzC,KAAU,IAAU;QAC5B,IAAI,CAACjqB,WAAW,GAAGiqB,KAAK,CAAC0C,MAAM,CAACr0B,KAAK;QACrC,IAAI,CAAC0H,WAAW,CAACK,MAAM,IAAI,CAAC,GACxB,IAAI,CAACosB,aAAa,CAACG,OAAO,EAAE,GAC5B,IAAI,CAACH,aAAa,CAACzzB,KAAK,EAAE;MAChC,CAAC;MACD6zB,UAAU,EAAG5C,KAAoB,IAAU;QACzCA,KAAK,CAACztB,GAAG,KAAK,OAAO,GACjB,IAAI,CAACiwB,aAAa,CAACG,OAAO,EAAE,GAC5B3C,KAAK,CAACztB,GAAG,KAAK,QAAQ,GACtB,IAAI,CAACiwB,aAAa,CAACzzB,KAAK,EAAE,GAC1B,IAAI;MACV,CAAC;MACD4zB,OAAO,EAAEA,CAAA,KAAW;QAClB,IAAI,CAACnrB,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,UAAU,GAAG,IAAI;QACtBlJ,UAAU,CAAC,MAAK;UACd,IAAI,CAAC4H,aAAa,GAAG,IAAI,CAACuM,QAAQ,CAAC7T,MAAM,CAAEg0B,CAAC,IAC1CA,CAAC,CAAC/sB,OAAO,EAAEgtB,WAAW,EAAE,CAACd,QAAQ,CAAC,IAAI,CAACjsB,WAAW,CAAC+sB,WAAW,EAAE,CAAC,CAClE;UACD,IAAI,CAACtrB,WAAW,GAAG,KAAK;QAC1B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDzI,KAAK,EAAEA,CAAA,KAAW;QAChB,IAAI,CAACgH,WAAW,GAAG,EAAE;QACrB,IAAI,CAACI,aAAa,GAAG,EAAE;QACvB,IAAI,CAACqB,WAAW,GAAG,KAAK;QACxB,IAAI,CAACC,UAAU,GAAG,KAAK;MACzB;KACD;IAED;IACA,KAAAb,aAAa,GAAG,IAAI,CAAC4rB,aAAa,CAACC,OAAO;IAC1C,KAAA1rB,gBAAgB,GAAG,IAAI,CAACyrB,aAAa,CAACI,UAAU;IAChD,KAAAG,aAAa,GAAG,IAAI,CAACP,aAAa,CAACG,OAAO;IAC1C,KAAAvtB,WAAW,GAAG,IAAI,CAACotB,aAAa,CAACzzB,KAAK;IAEtC;IAEA;IACiB,KAAAi0B,mBAAmB,GAAG;MACrCC,OAAO,EAAGlD,SAAiB,IAAU;QACnC,IAAI,CAAChgB,cAAc,CAACggB,SAAS,CAAC,GAAG,IAAI;MACvC,CAAC;MACDmD,SAAS,EAAGnD,SAAiB,IAAU;QACrC,IAAI,CAAChgB,cAAc,CAACggB,SAAS,CAAC,GAAG,KAAK;MACxC,CAAC;MACDoD,UAAU,EAAGpD,SAAiB,IAAU;QACtC,IAAI,CAACjgB,iBAAiB,CAACigB,SAAS,CAAC,GAAG,IAAI;MAC1C,CAAC;MACDwC,YAAY,EAAGxC,SAAiB,IAAU;QACxC,IAAI,CAACjgB,iBAAiB,CAACigB,SAAS,CAAC,GAAG,KAAK;MAC3C,CAAC;MACDqD,aAAa,EAAGrD,SAAiB,IAAU;QACzC,IAAI,CAACjgB,iBAAiB,CAACigB,SAAS,CAAC,GAAG,KAAK;MAC3C;KACD;IAED;IACA,KAAArkB,mBAAmB,GAAG,IAAI,CAACsnB,mBAAmB,CAACC,OAAO;IACtD,KAAAtmB,qBAAqB,GAAG,IAAI,CAACqmB,mBAAmB,CAACE,SAAS;IAC1D,KAAAloB,sBAAsB,GAAG,IAAI,CAACgoB,mBAAmB,CAACG,UAAU;IAC5D,KAAA/mB,mBAAmB,GAAG,IAAI,CAAC4mB,mBAAmB,CAACT,YAAY;IAC3D,KAAAhmB,oBAAoB,GAAG,IAAI,CAACymB,mBAAmB,CAACI,aAAa;IAE7D;IACS,KAAAC,mBAAmB,GAAG;MAC7B5tB,iBAAiB,EAAGsqB,SAAiB,IAAU,CAAE,CAAC;MAClDjoB,qBAAqB,EAAGioB,SAAiB,IAAU,CAAE,CAAC;MACtD3mB,sBAAsB,EAAEA,CAAA,KAAc,IAAI,CAACR,cAAc,CAACxC,MAAM;MAChEP,oBAAoB,EAAEA,CAACC,OAAe,EAAEwtB,KAAa,KAAY;QAC/D,IAAI,CAACA,KAAK,EAAE,OAAOxtB,OAAO;QAC1B,MAAMytB,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIF,KAAK,GAAG,EAAE,IAAI,CAAC;QAC5C,OAAOxtB,OAAO,CAAC2tB,OAAO,CAACF,KAAK,EAAE,iBAAiB,CAAC;MAClD;KACD;IAED;IACA,KAAA9tB,iBAAiB,GAAG,IAAI,CAAC4tB,mBAAmB,CAAC5tB,iBAAiB;IAC9D,KAAAqC,qBAAqB,GAAG,IAAI,CAACurB,mBAAmB,CAACvrB,qBAAqB;IACtE,KAAAsB,sBAAsB,GAAG,IAAI,CAACiqB,mBAAmB,CAACjqB,sBAAsB;IACxE,KAAAvD,oBAAoB,GAAG,IAAI,CAACwtB,mBAAmB,CAACxtB,oBAAoB;IAEpE;IACS,KAAA6tB,eAAe,GAAG;MACzBC,YAAY,EAAG5D,SAAiB,IAAU;QACxC,IAAI,CAACxd,kBAAkB,CAACwd,SAAS,CAAC,GAAG,CAAC,IAAI,CAACxd,kBAAkB,CAACwd,SAAS,CAAC;MAC1E,CAAC;MACD6D,OAAO,EAAEA,CAAC7D,SAAiB,EAAEnK,KAAa,KAAU;QAClD,IAAI,CAACrT,kBAAkB,CAACwd,SAAS,CAAC,GAAG,KAAK;MAC5C,CAAC;MACD8D,aAAa,EAAG9D,SAAiB,IAAU;QACzC,IAAI,CAAClgB,kBAAkB,CAACkgB,SAAS,CAAC,GAAG,CAAC,IAAI,CAAClgB,kBAAkB,CAACkgB,SAAS,CAAC;MAC1E;KACD;IAED;IACA,KAAAnmB,oBAAoB,GAAG,IAAI,CAAC8pB,eAAe,CAACC,YAAY;IACxD,KAAA1pB,cAAc,GAAG,IAAI,CAACypB,eAAe,CAACE,OAAO;IAC7C,KAAAppB,oBAAoB,GAAG,IAAI,CAACkpB,eAAe,CAACG,aAAa;IAhrEvD,IAAI,CAAC9gB,WAAW,GAAG,IAAI,CAAC8U,EAAE,CAACiM,KAAK,CAAC;MAC/BhuB,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC3G,uDAAU,CAAC40B,SAAS,CAAC,IAAI,CAAC,CAAC;KAC3C,CAAC;EACJ;EACAC,QAAQA,CAAA;IACN,IAAI,CAACnjB,aAAa,GAAG,IAAI,CAAC+W,WAAW,CAACqM,gBAAgB,EAAE;IAExD,MAAMC,UAAU,GAAGpJ,YAAY,CAACqJ,OAAO,CAAC,YAAY,CAAC;IACrD,IAAID,UAAU,EAAE;MACd,IAAI,CAACnL,aAAa,GAAGmL,UAAU;;IAGjC;IACA,IAAI,CAACE,wBAAwB,EAAE;IAC/B,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,qBAAqB,EAAE;IAE5BC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEnE,MAAMC,QAAQ,GAAG,IAAI,CAACjN,KAAK,CAACkN,MAAM,CAC/BC,IAAI,CACHj2B,uDAAM,CAAEg2B,MAAM,IAAKA,MAAM,CAAC,IAAI,CAAC,CAAC,EAChCr1B,qEAAoB,EAAE,EACtBD,0DAAS,CAAEs1B,MAAM,IAAI;MACnB,IAAI,CAACzhB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACV,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACiW,WAAW,GAAG,CAAC;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI;MAE3B,OAAO,IAAI,CAAChC,cAAc,CAACkO,eAAe,CACxCF,MAAM,CAAC,IAAI,CAAC,EACZ,IAAI,CAACpM,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB;IACH,CAAC,CAAC,CACH,CACA6D,SAAS,CAAC;MACTluB,IAAI,EAAG6pB,YAAY,IAAI;QACrB,IAAI,CAAC6M,wBAAwB,CAAC7M,YAAY,CAAC;MAC7C,CAAC;MACD7e,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC2rB,WAAW,CAAC,6BAA6B,EAAE3rB,KAAK,CAAC;MACxD;KACD,CAAC;IACJ,IAAI,CAACwf,aAAa,CAAC1J,GAAG,CAACwV,QAAQ,CAAC;EAClC;EAEA;;;EAGQK,WAAWA,CACjBl3B,OAAe,EACfuL,KAAU,EACV4rB,YAAA,GAAwB,IAAI;IAE5B,IAAI,CAACjN,MAAM,CAAC3e,KAAK,CAAC,aAAa,EAAEvL,OAAO,EAAEuL,KAAK,CAAC;IAChD,IAAI4rB,YAAY,EAAE;MAChB,IAAI,CAAC9hB,OAAO,GAAG,KAAK;MACpB,IAAI,CAACwC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACgT,aAAa,GAAG,KAAK;;IAE5B,IAAI,CAACtf,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CAACX,OAAO,CAAC;EACtC;EAEA;;;EAGQo3B,aAAaA,CAACp3B,OAAgB,EAAEq3B,QAAqB;IAC3D,IAAIr3B,OAAO,EAAE;MACX,IAAI,CAACiqB,YAAY,CAACvpB,WAAW,CAACV,OAAO,CAAC;;IAExC,IAAIq3B,QAAQ,EAAE;MACZA,QAAQ,EAAE;;EAEd;EAEA;EACAC,WAAWA,CAACC,QAAiB;IAC3B,OAAO,IAAI,CAACzO,cAAc,CAACwO,WAAW,CAACC,QAAQ,CAAC;EAClD;EAEAC,WAAWA,CAACD,QAAiB;IAC3B,OAAO,IAAI,CAACzO,cAAc,CAAC0O,WAAW,CAACD,QAAQ,CAAC;EAClD;EAEQN,wBAAwBA,CAAC7M,YAA0B;IACzD,IAAI,CAACA,YAAY,GAAGA,YAAY;IAEhC,IAAI,CAACA,YAAY,EAAEzV,QAAQ,IAAIyV,YAAY,CAACzV,QAAQ,CAACtM,MAAM,KAAK,CAAC,EAAE;MACjE,IAAI,CAACnG,gBAAgB,GACnBkoB,YAAY,EAAE/N,YAAY,EAAEob,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAACv3B,EAAE,KAAK,IAAI,CAAC2S,aAAa,IAAI4kB,CAAC,CAAC3kB,GAAG,KAAK,IAAI,CAACD,aAAa,CACnE,IAAI,IAAI;MACX,IAAI,CAAC6B,QAAQ,GAAG,EAAE;KACnB,MAAM;MACL,MAAMgjB,oBAAoB,GAAG,CAAC,IAAIvN,YAAY,EAAEzV,QAAQ,IAAI,EAAE,CAAC,CAAC;MAEhEgjB,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACjC,MAAMC,KAAK,GACTF,CAAC,CAAChwB,SAAS,YAAYikB,IAAI,GACvB+L,CAAC,CAAChwB,SAAS,CAACmwB,OAAO,EAAE,GACrB,IAAIlM,IAAI,CAAC+L,CAAC,CAAChwB,SAAmB,CAAC,CAACmwB,OAAO,EAAE;QAC/C,MAAMC,KAAK,GACTH,CAAC,CAACjwB,SAAS,YAAYikB,IAAI,GACvBgM,CAAC,CAACjwB,SAAS,CAACmwB,OAAO,EAAE,GACrB,IAAIlM,IAAI,CAACgM,CAAC,CAACjwB,SAAmB,CAAC,CAACmwB,OAAO,EAAE;QAC/C,OAAOD,KAAK,GAAGE,KAAK;MACtB,CAAC,CAAC;MAEF,IAAI,CAACtjB,QAAQ,GAAGgjB,oBAAoB;;IAGtC,IAAI,CAACz1B,gBAAgB,GACnBkoB,YAAY,EAAE/N,YAAY,EAAEob,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAACv3B,EAAE,KAAK,IAAI,CAAC2S,aAAa,IAAI4kB,CAAC,CAAC3kB,GAAG,KAAK,IAAI,CAACD,aAAa,CACnE,IAAI,IAAI;IAEX,IAAI,CAACuC,OAAO,GAAG,KAAK;IACpB7U,UAAU,CAAC,MAAM,IAAI,CAAC03B,cAAc,EAAE,EAAE,GAAG,CAAC;IAE5C;IACA,IAAI,CAACC,kBAAkB,EAAE;IAEzB,IAAI,IAAI,CAAC/N,YAAY,EAAEjqB,EAAE,EAAE;MACzB,IAAI,CAACi4B,8BAA8B,CAAC,IAAI,CAAChO,YAAY,CAACjqB,EAAE,CAAC;MACzD,IAAI,CAACk4B,sBAAsB,CAAC,IAAI,CAACjO,YAAY,CAACjqB,EAAE,CAAC;MACjD,IAAI,CAACm4B,2BAA2B,CAAC,IAAI,CAAClO,YAAY,CAACjqB,EAAE,CAAC;;EAE1D;EAEQi4B,8BAA8BA,CAACxE,cAAsB;IAC3D,MAAM2E,GAAG,GAAG,IAAI,CAACzP,cAAc,CAACsP,8BAA8B,CAC5DxE,cAAc,CACf,CAACnF,SAAS,CAAC;MACVluB,IAAI,EAAGi4B,mBAAmB,IAAI;QAC5B,IAAI,CAACpO,YAAY,GAAGoO,mBAAmB;QACvC,IAAI,CAAC7jB,QAAQ,GAAG6jB,mBAAmB,CAAC7jB,QAAQ,GACxC,CAAC,GAAG6jB,mBAAmB,CAAC7jB,QAAQ,CAAC,GACjC,EAAE;QACN,IAAI,CAACujB,cAAc,EAAE;MACvB,CAAC;MACD3sB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;IACF,IAAI,CAACoqB,aAAa,CAAC1J,GAAG,CAACkX,GAAG,CAAC;EAC7B;EAEQF,sBAAsBA,CAACzE,cAAsB;IACnD,MAAM2E,GAAG,GAAG,IAAI,CAACzP,cAAc,CAACuP,sBAAsB,CACpDzE,cAAc,CACf,CAACnF,SAAS,CAAC;MACVluB,IAAI,EAAGk4B,UAAU,IAAI;QACnB,IAAIA,UAAU,EAAE7E,cAAc,KAAK,IAAI,CAACxJ,YAAY,EAAEjqB,EAAE,EAAE;UACxD,IAAI,CAACwU,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE8jB,UAAU,CAAC,CAACb,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YAC3D,MAAMC,KAAK,GACTF,CAAC,CAAChwB,SAAS,YAAYikB,IAAI,GACvB+L,CAAC,CAAChwB,SAAS,CAACmwB,OAAO,EAAE,GACrB,IAAIlM,IAAI,CAAC+L,CAAC,CAAChwB,SAAmB,CAAC,CAACmwB,OAAO,EAAE;YAC/C,MAAMC,KAAK,GACTH,CAAC,CAACjwB,SAAS,YAAYikB,IAAI,GACvBgM,CAAC,CAACjwB,SAAS,CAACmwB,OAAO,EAAE,GACrB,IAAIlM,IAAI,CAACgM,CAAC,CAACjwB,SAAmB,CAAC,CAACmwB,OAAO,EAAE;YAC/C,OAAOD,KAAK,GAAGE,KAAK;UACtB,CAAC,CAAC;UAEFz3B,UAAU,CAAC,MAAM,IAAI,CAAC03B,cAAc,EAAE,EAAE,GAAG,CAAC;UAE5C,IACEO,UAAU,CAACtuB,MAAM,EAAEhK,EAAE,KAAK,IAAI,CAAC2S,aAAa,IAC5C2lB,UAAU,CAACtuB,MAAM,EAAE4I,GAAG,KAAK,IAAI,CAACD,aAAa,EAC7C;YACA,IAAI2lB,UAAU,CAACt4B,EAAE,EAAE;cACjB,IAAI,CAAC2oB,cAAc,CAAC4P,iBAAiB,CAACD,UAAU,CAACt4B,EAAE,CAAC,CAACsuB,SAAS,EAAE;;;;MAIxE,CAAC;MACDljB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;IACF,IAAI,CAACoqB,aAAa,CAAC1J,GAAG,CAACkX,GAAG,CAAC;EAC7B;EAEQD,2BAA2BA,CAAC1E,cAAsB;IACxD,MAAM2E,GAAG,GAAG,IAAI,CAACzP,cAAc,CAAC6P,0BAA0B,CACxD/E,cAAc,CACf,CAACnF,SAAS,CAAC;MACVluB,IAAI,EAAG0xB,KAAK,IAAI;QACd,IAAIA,KAAK,CAAC2G,MAAM,KAAK,IAAI,CAAC9lB,aAAa,EAAE;UACvC,IAAI,CAACwX,QAAQ,GAAG2H,KAAK,CAAC3H,QAAQ;UAC9B,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjBuO,YAAY,CAAC,IAAI,CAACC,aAAa,CAAC;YAChC,IAAI,CAACA,aAAa,GAAGt4B,UAAU,CAAC,MAAK;cACnC,IAAI,CAAC8pB,QAAQ,GAAG,KAAK;YACvB,CAAC,EAAE,IAAI,CAAC;;;MAGd;KACD,CAAC;IACF,IAAI,CAACS,aAAa,CAAC1J,GAAG,CAACkX,GAAG,CAAC;EAC7B;EAEQJ,kBAAkBA,CAAA;IACxB,MAAMY,cAAc,GAAG,IAAI,CAACpkB,QAAQ,CAAC7T,MAAM,CACxCk4B,GAAG,IACF,CAACA,GAAG,CAAC/W,MAAM,KACV+W,GAAG,CAACC,QAAQ,EAAE94B,EAAE,KAAK,IAAI,CAAC2S,aAAa,IACtCkmB,GAAG,CAACC,QAAQ,EAAElmB,GAAG,KAAK,IAAI,CAACD,aAAa,CAAC,CAC9C;IAEDimB,cAAc,CAACG,OAAO,CAAEF,GAAG,IAAI;MAC7B,IAAIA,GAAG,CAAC74B,EAAE,EAAE;QACV,MAAMo4B,GAAG,GAAG,IAAI,CAACzP,cAAc,CAAC4P,iBAAiB,CAACM,GAAG,CAAC74B,EAAE,CAAC,CAACsuB,SAAS,CAAC;UAClEljB,KAAK,EAAGA,KAAK,IAAI;YACf;UAAA;SAEH,CAAC;QACF,IAAI,CAACwf,aAAa,CAAC1J,GAAG,CAACkX,GAAG,CAAC;;IAE/B,CAAC,CAAC;EACJ;EAEAY,cAAcA,CAAClH,KAAU;IACvB,MAAMmH,IAAI,GAAGnH,KAAK,CAAC0C,MAAM,CAAC0E,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACD,IAAI,EAAE;IAEX;IACA,IAAIA,IAAI,CAAC/Y,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/B,IAAI,CAAC4J,YAAY,CAACtpB,SAAS,CAAC,mCAAmC,CAAC;MAChE;;IAGF;IACA,MAAM24B,UAAU,GAAG,CACjB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,oBAAoB,EACpB,yEAAyE,CAC1E;IACD,IAAI,CAACA,UAAU,CAACrF,QAAQ,CAACmF,IAAI,CAACn5B,IAAI,CAAC,EAAE;MACnC,IAAI,CAACgqB,YAAY,CAACtpB,SAAS,CACzB,gEAAgE,CACjE;MACD;;IAGF,IAAI,CAACoX,YAAY,GAAGqhB,IAAI;IACxB,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,IAAI,CAAC7jB,UAAU,GAAG2jB,MAAM,CAACG,MAAM;IACjC,CAAC;IACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;EAC5B;EAEA1jB,gBAAgBA,CAAA;IACd,IAAI,CAACqC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACnC,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAACgkB,SAAS,EAAEC,aAAa,EAAE;MACjC,IAAI,CAACD,SAAS,CAACC,aAAa,CAACv5B,KAAK,GAAG,EAAE;;EAE3C;EAOA;;;;EAIA+W,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC+S,YAAY,EAAEjqB,EAAE,IAAI,CAAC,IAAI,CAAC2S,aAAa,EAAE;MACjD;;IAGF,MAAM8gB,cAAc,GAAG,IAAI,CAACxJ,YAAY,CAACjqB,EAAE;IAC3C04B,YAAY,CAAC,IAAI,CAACiB,WAAW,CAAC;IAE9B,IAAI,CAAC,IAAI,CAACxN,iBAAiB,EAAE;MAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACxD,cAAc,CAACiR,WAAW,CAACnG,cAAc,CAAC,CAACnF,SAAS,CAAC;QACxDluB,IAAI,EAAEA,CAAA,KAAK;UACT;QAAA,CACD;QACDgL,KAAK,EAAGA,KAAK,IAAI;UACf;QAAA;OAEH,CAAC;;IAGJ,IAAI,CAACuuB,WAAW,GAAGt5B,UAAU,CAAC,MAAK;MACjC,IAAI,IAAI,CAAC8rB,iBAAiB,EAAE;QAC1B,IAAI,CAACA,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACxD,cAAc,CAACkR,UAAU,CAACpG,cAAc,CAAC,CAACnF,SAAS,CAAC;UACvDluB,IAAI,EAAEA,CAAA,KAAK;YACT;UAAA,CACD;UACDgL,KAAK,EAAGA,KAAK,IAAI;YACf;UAAA;SAEH,CAAC;;IAEN,CAAC,EAAE,IAAI,CAACihB,cAAc,CAAC;EACzB;EAEA;;;EAGQG,WAAWA,CAACsN,SAAiB,EAAEC,WAAA,GAAuB,IAAI;IAChE,MAAMC,MAAM,GAAG;MACbrN,KAAK,EAAE,mBAAmB;MAC1BsN,IAAI,EAAE,cAAc;MACpBvS,KAAK,EAAE,iBAAiB;MACxBqI,YAAY,EAAE,uBAAuB;MACrCmK,MAAM,EAAE,eAAe;MACvBxJ,MAAM,EAAE;KACT;IAED,MAAMyJ,YAAY,GAAGH,MAAM,CAACF,SAAgC,CAAC;IAC7D,IAAIK,YAAY,EAAE;MACf,IAAY,CAACA,YAAY,CAAC,GAAG,CAAE,IAAY,CAACA,YAAY,CAAC;MAE1D,IAAIJ,WAAW,IAAK,IAAY,CAACI,YAAY,CAAC,EAAE;QAC9CC,MAAM,CAACnJ,MAAM,CAAC+I,MAAM,CAAC,CAACjB,OAAO,CAAEsB,KAAK,IAAI;UACtC,IAAIA,KAAK,KAAKF,YAAY,EAAE;YACzB,IAAY,CAACE,KAAK,CAAC,GAAG,KAAK;;QAEhC,CAAC,CAAC;;;EAGR;EAuDA;EACAh0B,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC4jB,YAAY,EAAEjqB,EAAE,IAAI,IAAI,CAACwU,QAAQ,CAACtM,MAAM,KAAK,CAAC,EAAE;MACxD,IAAI,CAAC4hB,YAAY,CAACrpB,WAAW,CAAC,6BAA6B,CAAC;MAC5D;;IAGF,IACE65B,OAAO,CACL,oHAAoH,CACrH,EACD;MACA,IAAI,CAAC9lB,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACuW,YAAY,GAAG,KAAK;MACzB,IAAI,CAACjB,YAAY,CAACvpB,WAAW,CAAC,gCAAgC,CAAC;;EAEnE;EAEAiG,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACyjB,YAAY,EAAEjqB,EAAE,IAAI,IAAI,CAACwU,QAAQ,CAACtM,MAAM,KAAK,CAAC,EAAE;MACxD,IAAI,CAAC4hB,YAAY,CAACrpB,WAAW,CAAC,gCAAgC,CAAC;MAC/D;;IAGF,MAAM85B,gBAAgB,GAAG,IAAI,CAACtQ,YAAY,CAACnN,OAAO,GAC9C,IAAI,CAACmN,YAAY,CAACuQ,SAAS,IAAI,iBAAiB,GAChD,IAAI,CAACz4B,gBAAgB,EAAEC,QAAQ,IAAI,qBAAqB;IAE5D,MAAMy4B,UAAU,GAAG;MACjBxQ,YAAY,EAAE;QACZjqB,EAAE,EAAE,IAAI,CAACiqB,YAAY,CAACjqB,EAAE;QACxBi0B,IAAI,EAAEsG,gBAAgB;QACtBzd,OAAO,EAAE,IAAI,CAACmN,YAAY,CAACnN,OAAO;QAClCZ,YAAY,EAAE,IAAI,CAAC+N,YAAY,CAAC/N,YAAY;QAC5Cwe,SAAS,EAAE,IAAI,CAACzQ,YAAY,CAACyQ;OAC9B;MACDlmB,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC4e,GAAG,CAAEyF,GAAG,KAAM;QACpC74B,EAAE,EAAE64B,GAAG,CAAC74B,EAAE;QACV4H,OAAO,EAAEixB,GAAG,CAACjxB,OAAO;QACpBoC,MAAM,EAAE6uB,GAAG,CAAC7uB,MAAM;QAClBtC,SAAS,EAAEmxB,GAAG,CAACnxB,SAAS;QACxB5H,IAAI,EAAE+4B,GAAG,CAAC/4B;OACX,CAAC,CAAC;MACH66B,UAAU,EAAE,IAAIhP,IAAI,EAAE,CAACiP,WAAW,EAAE;MACpCC,UAAU,EAAE,IAAI,CAACloB;KAClB;IAED,MAAMmoB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAACR,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;MAC3D36B,IAAI,EAAE;KACP,CAAC;IACF,MAAMo7B,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;IAC5C,MAAMQ,IAAI,GAAGhF,QAAQ,CAACiF,aAAa,CAAC,GAAG,CAAC;IACxCD,IAAI,CAACE,IAAI,GAAGN,GAAG;IAEf,MAAMO,YAAY,GAAGlB,gBAAgB,CAClChF,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAC3BX,WAAW,EAAE;IAChB,MAAM8G,OAAO,GAAG,IAAI/P,IAAI,EAAE,CAACiP,WAAW,EAAE,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACtDL,IAAI,CAACM,QAAQ,GAAG,gBAAgBH,YAAY,IAAIC,OAAO,OAAO;IAE9DJ,IAAI,CAACO,KAAK,EAAE;IACZV,MAAM,CAACC,GAAG,CAACU,eAAe,CAACZ,GAAG,CAAC;IAE/B,IAAI,CAACnQ,YAAY,GAAG,KAAK;IACzB,IAAI,CAACjB,YAAY,CAACvpB,WAAW,CAAC,mCAAmC,CAAC;EACpE;EAkBAw7B,WAAWA,CAAA;IACT,IACG,IAAI,CAAClnB,WAAW,CAAC8C,OAAO,IAAI,CAAC,IAAI,CAACC,YAAY,IAC/C,CAAC,IAAI,CAACjF,aAAa,IACnB,CAAC,IAAI,CAAC5Q,gBAAgB,EAAE/B,EAAE,EAC1B;MACA;;IAGF,IAAI,CAACg8B,mBAAmB,EAAE;IAE1B,MAAMp0B,OAAO,GAAG,IAAI,CAACiN,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC,EAAE3U,KAAK;IAEtD;IACA,MAAM87B,WAAW,GAAY;MAC3Bj8B,EAAE,EAAE,OAAO,GAAG,IAAI2rB,IAAI,EAAE,CAACkM,OAAO,EAAE;MAClCjwB,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtBoC,MAAM,EAAE;QACNhK,EAAE,EAAE,IAAI,CAAC2S,aAAa,IAAI,EAAE;QAC5B3Q,QAAQ,EAAE,IAAI,CAACkoB;OAChB;MACD4O,QAAQ,EAAE;QACR94B,EAAE,EAAE,IAAI,CAAC+B,gBAAgB,CAAC/B,EAAE;QAC5BgC,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC,QAAQ,IAAI;OAC7C;MACD0F,SAAS,EAAE,IAAIikB,IAAI,EAAE;MACrB7J,MAAM,EAAE,KAAK;MACbtQ,SAAS,EAAE,IAAI,CAAE;KAClB;IAED;IACA,IAAI,IAAI,CAACoG,YAAY,EAAE;MACrB;MACA,IAAIskB,QAAQ,GAAG,MAAM;MACrB,IAAI,IAAI,CAACtkB,YAAY,CAAC9X,IAAI,CAACq8B,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC/CD,QAAQ,GAAG,OAAO;QAElB;QACA,IAAI,IAAI,CAACzmB,UAAU,EAAE;UACnBwmB,WAAW,CAACG,WAAW,GAAG,CACxB;YACEp8B,EAAE,EAAE,iBAAiB;YACrBk7B,GAAG,EAAE,IAAI,CAACzlB,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC2b,QAAQ,EAAE,GAAG,EAAE;YACtDtxB,IAAI,EAAEqB,qEAAW,CAACk7B,KAAK;YACvBpI,IAAI,EAAE,IAAI,CAACrc,YAAY,CAACqc,IAAI;YAC5B/T,IAAI,EAAE,IAAI,CAACtI,YAAY,CAACsI;WACzB,CACF;;;MAIL;MACA,IAAIgc,QAAQ,KAAK,OAAO,EAAE;QACxBD,WAAW,CAACn8B,IAAI,GAAGqB,qEAAW,CAACk7B,KAAK;OACrC,MAAM,IAAIH,QAAQ,KAAK,MAAM,EAAE;QAC9BD,WAAW,CAACn8B,IAAI,GAAGqB,qEAAW,CAACm7B,IAAI;;;IAIvC;IACA,IAAI,CAAC9nB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAEynB,WAAW,CAAC;IAE/C;IACA,MAAMM,UAAU,GAAG,IAAI,CAAC3kB,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC/C,WAAW,CAAC2nB,KAAK,EAAE;IACxB,IAAI,CAACjnB,gBAAgB,EAAE;IAEvB;IACAlV,UAAU,CAAC,MAAM,IAAI,CAAC03B,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IAE/C;IACA,IAAI,CAACrgB,WAAW,GAAG,IAAI;IAEvB,MAAM+kB,OAAO,GAAG,IAAI,CAAC9T,cAAc,CAACoT,WAAW,CAC7C,IAAI,CAACh6B,gBAAgB,CAAC/B,EAAE,EACxB4H,OAAO,EACP20B,UAAU,IAAIG,SAAS,EACvBv7B,qEAAW,CAACw7B,IAAI,EAChB,IAAI,CAAC1S,YAAY,EAAEjqB,EAAE,CACtB,CAACsuB,SAAS,CAAC;MACVluB,IAAI,EAAGP,OAAO,IAAI;QAChB,IAAI,CAAC2U,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4e,GAAG,CAAEyF,GAAG,IACpCA,GAAG,CAAC74B,EAAE,KAAKi8B,WAAW,CAACj8B,EAAE,GAAGH,OAAO,GAAGg5B,GAAG,CAC1C;QACD,IAAI,CAACnhB,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDtM,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACoJ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4e,GAAG,CAAEyF,GAAG,IAAI;UACxC,IAAIA,GAAG,CAAC74B,EAAE,KAAKi8B,WAAW,CAACj8B,EAAE,EAAE;YAC7B,OAAO;cACL,GAAG64B,GAAG;cACNrnB,SAAS,EAAE,KAAK;cAChBC,OAAO,EAAE;aACV;;UAEH,OAAOonB,GAAG;QACZ,CAAC,CAAC;QACF,IAAI,CAACnhB,WAAW,GAAG,KAAK;QACxB,IAAI,CAACoS,YAAY,CAACtpB,SAAS,CAAC,wBAAwB,CAAC;MACvD;KACD,CAAC;IAEF,IAAI,CAACoqB,aAAa,CAAC1J,GAAG,CAACub,OAAO,CAAC;EACjC;EA+CA;EAEA;EACAG,QAAQA,CAAC9K,KAAU;IACjB,MAAM+K,SAAS,GAAG/K,KAAK,CAAC0C,MAAM;IAC9B,MAAMsI,SAAS,GAAGD,SAAS,CAACC,SAAS;IAErC;IACA,IACEA,SAAS,GAAG,EAAE,IACd,CAAC,IAAI,CAACpS,aAAa,IACnB,IAAI,CAACT,YAAY,EAAEjqB,EAAE,IACrB,IAAI,CAAC2qB,eAAe,EACpB;MACA;MACA,IAAI,CAACoS,oBAAoB,EAAE;MAE3B;MACA,MAAMC,eAAe,GAAGH,SAAS,CAACI,YAAY;MAC9C,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,EAAE;MAEzD;MACA,IAAI,CAACzS,aAAa,GAAG,IAAI;MAEzB;MACA,IAAI,CAAC0S,gBAAgB,EAAE;MAEvB;MACA;MACAC,qBAAqB,CAAC,MAAK;QACzB,MAAMC,sBAAsB,GAAGA,CAAA,KAAK;UAClC,IAAIJ,mBAAmB,EAAE;YACvB,MAAMK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAC5CN,mBAAmB,CAACl9B,EAAE,CACvB;YACD,IAAIu9B,cAAc,EAAE;cAClB;cACAA,cAAc,CAACE,cAAc,CAAC;gBAAEC,KAAK,EAAE;cAAQ,CAAE,CAAC;aACnD,MAAM;cACL;cACA,MAAMC,eAAe,GAAGd,SAAS,CAACI,YAAY;cAC9C,MAAMW,UAAU,GAAGD,eAAe,GAAGX,eAAe;cACpDH,SAAS,CAACC,SAAS,GAAGA,SAAS,GAAGc,UAAU;;;UAIhD;UACA,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACAx9B,UAAU,CAACi9B,sBAAsB,EAAE,GAAG,CAAC;MACzC,CAAC,CAAC;;EAEN;EAEA;EACQH,sBAAsBA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAACW,iBAAiB,EAAEpE,aAAa,IAAI,CAAC,IAAI,CAACllB,QAAQ,CAACtM,MAAM,EACjE,OAAO,IAAI;IAEb,MAAM20B,SAAS,GAAG,IAAI,CAACiB,iBAAiB,CAACpE,aAAa;IACtD,MAAMqE,eAAe,GAAGlB,SAAS,CAACmB,gBAAgB,CAAC,eAAe,CAAC;IAEnE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,eAAe,CAAC71B,MAAM,EAAE+1B,CAAC,EAAE,EAAE;MAC/C,MAAMC,OAAO,GAAGH,eAAe,CAACE,CAAC,CAAC;MAClC,MAAME,IAAI,GAAGD,OAAO,CAACE,qBAAqB,EAAE;MAE5C;MACA,IAAID,IAAI,CAACE,GAAG,IAAI,CAAC,IAAIF,IAAI,CAACG,MAAM,IAAIzB,SAAS,CAAC0B,YAAY,EAAE;QAC1D,MAAM1M,SAAS,GAAGqM,OAAO,CAACM,YAAY,CAAC,iBAAiB,CAAC;QACzD,OAAO,IAAI,CAAChqB,QAAQ,CAAC8iB,IAAI,CAAE3C,CAAC,IAAKA,CAAC,CAAC30B,EAAE,KAAK6xB,SAAS,CAAC,IAAI,IAAI;;;IAIhE,OAAO,IAAI;EACb;EAEA;EACQ2L,kBAAkBA,CACxB3L,SAA6B;IAE7B,IAAI,CAAC,IAAI,CAACiM,iBAAiB,EAAEpE,aAAa,IAAI,CAAC7H,SAAS,EAAE,OAAO,IAAI;IACrE,OAAO,IAAI,CAACiM,iBAAiB,CAACpE,aAAa,CAAC+E,aAAa,CACvD,qBAAqB5M,SAAS,IAAI,CACnC;EACH;EAEA;EACQkL,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAACzG,QAAQ,CAACoI,cAAc,CAAC,2BAA2B,CAAC,EAAE;MACzD,MAAMC,SAAS,GAAGrI,QAAQ,CAACiF,aAAa,CAAC,KAAK,CAAC;MAC/CoD,SAAS,CAAC3+B,EAAE,GAAG,2BAA2B;MAC1C2+B,SAAS,CAACC,SAAS,GAAG,wCAAwC;MAC9DD,SAAS,CAACE,SAAS,GACjB,uEAAuE;MAEzE,IAAI,IAAI,CAACf,iBAAiB,EAAEpE,aAAa,EAAE;QACzC,IAAI,CAACoE,iBAAiB,CAACpE,aAAa,CAACoF,OAAO,CAACH,SAAS,CAAC;;;EAG7D;EAEA;EACQd,oBAAoBA,CAAA;IAC1B,MAAMc,SAAS,GAAGrI,QAAQ,CAACoI,cAAc,CAAC,2BAA2B,CAAC;IACtE,IAAIC,SAAS,IAAIA,SAAS,CAACI,UAAU,EAAE;MACrCJ,SAAS,CAACI,UAAU,CAACC,WAAW,CAACL,SAAS,CAAC;;EAE/C;EAEA;EACAvB,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC1S,aAAa,IAAI,CAAC,IAAI,CAACT,YAAY,EAAEjqB,EAAE,IAAI,CAAC,IAAI,CAAC2qB,eAAe,EACvE;IAEF;IACA,IAAI,CAACD,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAACD,WAAW,EAAE;IAElB;IACA,IAAI,CAAC9B,cAAc,CAACkO,eAAe,CACjC,IAAI,CAAC5M,YAAY,CAACjqB,EAAE,EACpB,IAAI,CAACuqB,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB,CAAC6D,SAAS,CAAC;MACVluB,IAAI,EAAG6pB,YAAY,IAAI;QACrB,IACEA,YAAY,IACZA,YAAY,CAACzV,QAAQ,IACrByV,YAAY,CAACzV,QAAQ,CAACtM,MAAM,GAAG,CAAC,EAChC;UACA;UACA,MAAM+2B,WAAW,GAAG,CAAC,GAAG,IAAI,CAACzqB,QAAQ,CAAC;UAEtC;UACA,MAAM0qB,WAAW,GAAG,IAAI9T,GAAG,CAAC6T,WAAW,CAAC7L,GAAG,CAAEyF,GAAG,IAAKA,GAAG,CAAC74B,EAAE,CAAC,CAAC;UAE7D;UACA,MAAMm/B,WAAW,GAAGlV,YAAY,CAACzV,QAAQ,CACtC7T,MAAM,CAAEk4B,GAAG,IAAK,CAACqG,WAAW,CAACld,GAAG,CAAC6W,GAAG,CAAC74B,EAAE,CAAC,CAAC,CACzCy3B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACb,MAAMC,KAAK,GAAG,IAAIjM,IAAI,CAAC+L,CAAC,CAAChwB,SAAmB,CAAC,CAACmwB,OAAO,EAAE;YACvD,MAAMC,KAAK,GAAG,IAAInM,IAAI,CAACgM,CAAC,CAACjwB,SAAmB,CAAC,CAACmwB,OAAO,EAAE;YACvD,OAAOD,KAAK,GAAGE,KAAK;UACtB,CAAC,CAAC;UAEJ,IAAIqH,WAAW,CAACj3B,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAI,CAACsM,QAAQ,GAAG,CAAC,GAAG2qB,WAAW,EAAE,GAAGF,WAAW,CAAC;YAEhD;YACA,IAAI,IAAI,CAACzqB,QAAQ,CAACtM,MAAM,GAAG,IAAI,CAACsiB,kBAAkB,EAAE;cAClD,IAAI,CAAChW,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4qB,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5U,kBAAkB,CAAC;;YAGjE;YACA,IAAI,CAACG,eAAe,GAClBwU,WAAW,CAACj3B,MAAM,IAAI,IAAI,CAACqiB,oBAAoB;WAClD,MAAM;YACL;YACA,IAAI,CAACI,eAAe,GAAG,KAAK;;SAE/B,MAAM;UACL,IAAI,CAACA,eAAe,GAAG,KAAK;;QAG9B;QACA;QACAtqB,UAAU,CAAC,MAAK;UACd,IAAI,CAACqqB,aAAa,GAAG,KAAK;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDtf,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC2e,MAAM,CAAC3e,KAAK,CAAC,aAAa,EAAE,8BAA8B,EAAEA,KAAK,CAAC;QACvE,IAAI,CAACsf,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACmT,oBAAoB,EAAE;QAC3B,IAAI,CAAC/T,YAAY,CAACtpB,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACQ6+B,eAAeA,CACrBC,UAAqC,EACrCC,UAAqC;IAErC,IAAI,CAACD,UAAU,IAAI,CAACC,UAAU,EAAE,OAAO,KAAK;IAE5C,IAAI;MACF,MAAMC,KAAK,GACTF,UAAU,YAAY3T,IAAI,GACtB2T,UAAU,CAACzH,OAAO,EAAE,GACpB,IAAIlM,IAAI,CAAC2T,UAAoB,CAAC,CAACzH,OAAO,EAAE;MAC9C,MAAM4H,KAAK,GACTF,UAAU,YAAY5T,IAAI,GACtB4T,UAAU,CAAC1H,OAAO,EAAE,GACpB,IAAIlM,IAAI,CAAC4T,UAAoB,CAAC,CAAC1H,OAAO,EAAE;MAC9C,OAAOnF,IAAI,CAACgN,GAAG,CAACF,KAAK,GAAGC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;KACxC,CAAC,OAAOr0B,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEA2sB,cAAcA,CAAC4H,KAAA,GAAiB,KAAK;IACnC,IAAI;MACF,IAAI,CAAC,IAAI,CAAC7B,iBAAiB,EAAEpE,aAAa,EAAE;MAE5C;MACA2D,qBAAqB,CAAC,MAAK;QACzB,MAAMR,SAAS,GAAG,IAAI,CAACiB,iBAAiB,CAACpE,aAAa;QACtD,MAAMkG,kBAAkB,GACtB/C,SAAS,CAACI,YAAY,GAAGJ,SAAS,CAAC0B,YAAY,IAC/C1B,SAAS,CAACC,SAAS,GAAG,GAAG;QAE3B;QACA;QACA;QACA,IAAI6C,KAAK,IAAIC,kBAAkB,EAAE;UAC/B;UACA/C,SAAS,CAACgD,QAAQ,CAAC;YACjBxB,GAAG,EAAExB,SAAS,CAACI,YAAY;YAC3B6C,QAAQ,EAAE;WACX,CAAC;;MAEN,CAAC,CAAC;KACH,CAAC,OAAOC,GAAG,EAAE;MACZ,IAAI,CAAChW,MAAM,CAAC3e,KAAK,CAAC,aAAa,EAAE,4BAA4B,EAAE20B,GAAG,CAAC;;EAEvE;EAEA;EACA;;;EAGAzoB,oBAAoBA,CAAA;IAClB,IAAI,CAAC8S,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAE9C,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B;MACA,IAAI,CAACC,sBAAsB,GAAG,CAAC;;EAEnC;EAEA;;;;EAIA1T,wBAAwBA,CAACqpB,SAAe;IACtC,IAAI,CAAC,IAAI,CAAC/V,YAAY,EAAEjqB,EAAE,IAAI,CAAC,IAAI,CAAC+B,gBAAgB,EAAE/B,EAAE,EAAE;MACxD,IAAI,CAAC8pB,YAAY,CAACtpB,SAAS,CAAC,uCAAuC,CAAC;MACpE,IAAI,CAAC4pB,gBAAgB,GAAG,KAAK;MAC7B;;IAGF,MAAM6V,UAAU,GAAG,IAAI,CAACl+B,gBAAgB,EAAE/B,EAAE,IAAI,EAAE;IAElD,IAAI,CAAC2oB,cAAc,CAACuX,gBAAgB,CAClCD,UAAU,EACVD,SAAS,EACT,IAAI,CAAC/V,YAAY,EAAEjqB,EAAE,EACrB,IAAI,CAACqqB,sBAAsB,CAC5B,CAACiE,SAAS,CAAC;MACVluB,IAAI,EAAGP,OAAO,IAAI;QAChB,IAAI,CAACuqB,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;QAC/B,IAAI,CAAC0N,cAAc,CAAC,IAAI,CAAC;MAC3B,CAAC;MACD3sB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CAAC,8BAA8B,CAAC;QAC3D,IAAI,CAAC4pB,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC;EACJ;EAEA;;;EAGAtT,yBAAyBA,CAAA;IACvB,IAAI,CAACsT,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;EACjC;EAEA;;;;EAIA8V,mBAAmBA,CAACC,QAAgB;IAClCjF,MAAM,CAACkF,IAAI,CAACD,QAAQ,EAAE,QAAQ,CAAC;EACjC;EAEA;;;;;EAKAE,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACvI,cAAc,EAAE;IAErB;IACA;IACA,IAAI,IAAI,CAACvjB,QAAQ,CAAC+rB,IAAI,CAAE1H,GAAG,IAAKA,GAAG,CAAC/4B,IAAI,KAAKqB,qEAAW,CAACq/B,aAAa,CAAC,EAAE;MACvE;MACAngC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC2pB,GAAG,CAACyW,aAAa,EAAE;MAC1B,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;EAGQzE,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAAC7P,iBAAiB,IAAI,IAAI,CAAClC,YAAY,EAAEjqB,EAAE,EAAE;MACnD,IAAI,CAACmsB,iBAAiB,GAAG,KAAK;MAC9BuM,YAAY,CAAC,IAAI,CAACiB,WAAW,CAAC;MAE9B,MAAMlG,cAAc,GAAG,IAAI,CAACxJ,YAAY,EAAEjqB,EAAE;MAC5C,IAAIyzB,cAAc,EAAE;QAClB,IAAI,CAAC9K,cAAc,CAACkR,UAAU,CAACpG,cAAc,CAAC,CAACnF,SAAS,CAAC;UACvDluB,IAAI,EAAEA,CAAA,KAAK;YACT;UAAA,CACD;UACDgL,KAAK,EAAGA,KAAK,IAAI;YACf;UAAA;SAEH,CAAC;;;EAGR;EAEA;;;EAGAs1B,qBAAqBA,CAAA;IACnB,IAAI,CAAC7W,MAAM,CAAC8W,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEA;EAEA;;;;EAIA7qB,WAAWA,CAAC4R,KAAa;IACvB,MAAMkZ,OAAO,GAAG,IAAI,CAAC/rB,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC;IAC/C,IAAI8rB,OAAO,EAAE;MACX,MAAMC,YAAY,GAAGD,OAAO,CAACzgC,KAAK,IAAI,EAAE;MACxCygC,OAAO,CAAC7rB,QAAQ,CAAC8rB,YAAY,GAAGnZ,KAAK,CAAC;MACtCkZ,OAAO,CAACE,WAAW,EAAE;MACrB;MACAzgC,UAAU,CAAC,MAAK;QACd,MAAM0gC,YAAY,GAAGzK,QAAQ,CAACmI,aAAa,CACzC,uBAAuB,CACJ;QACrB,IAAIsC,YAAY,EAAE;UAChBA,YAAY,CAACC,KAAK,EAAE;;MAExB,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;EAGQ9K,wBAAwBA,CAAA;IAC9B,MAAM+K,eAAe,GACnB,IAAI,CAACtY,cAAc,CAACuY,2BAA2B,EAAE,CAAC5S,SAAS,CAAC;MAC1DluB,IAAI,EAAG2vB,YAAY,IAAI;QACrB,IAAI,CAACpK,aAAa,CAACwb,OAAO,CAACpR,YAAY,CAAC;QACxC,IAAI,CAACR,uBAAuB,EAAE;QAE9B,IAAI,CAAC5G,cAAc,CAACyY,IAAI,CAAC,cAAc,CAAC;QAExC,IACErR,YAAY,CAACjwB,IAAI,KAAK,aAAa,IACnCiwB,YAAY,CAAC0D,cAAc,KAAK,IAAI,CAACxJ,YAAY,EAAEjqB,EAAE,EACrD;UACA,IAAI+vB,YAAY,CAAC/vB,EAAE,EAAE;YACnB,IAAI,CAAC2oB,cAAc,CAAC0Y,UAAU,CAAC,CAACtR,YAAY,CAAC/vB,EAAE,CAAC,CAAC,CAACsuB,SAAS,EAAE;;;MAGnE,CAAC;MACDljB,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;IACJ,IAAI,CAACwf,aAAa,CAAC1J,GAAG,CAAC+f,eAAe,CAAC;IAEvC,MAAMK,oBAAoB,GAAG,IAAI,CAAC3Y,cAAc,CAAC4Y,cAAc,CAACjT,SAAS,CAAC;MACxEluB,IAAI,EAAGulB,aAAa,IAAI;QACtB,IAAI,CAACA,aAAa,GAAGA,aAAa;QAClC,IAAI,CAAC4J,uBAAuB,EAAE;MAChC,CAAC;MACDnkB,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;IACF,IAAI,CAACwf,aAAa,CAAC1J,GAAG,CAACogB,oBAAoB,CAAC;IAE5C,MAAME,oBAAoB,GACxB,IAAI,CAAC7Y,cAAc,CAAC8Y,kBAAkB,CAACnT,SAAS,CAAC;MAC/CluB,IAAI,EAAGwC,KAAK,IAAI;QACd,IAAI,CAACmc,uBAAuB,GAAGnc,KAAK;MACtC;KACD,CAAC;IACJ,IAAI,CAACgoB,aAAa,CAAC1J,GAAG,CAACsgB,oBAAoB,CAAC;IAE5C,MAAME,OAAO,GAAG,IAAI,CAAC/Y,cAAc,CAACgZ,aAAa,CAACrT,SAAS,CAAC;MAC1DluB,IAAI,EAAG0yB,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAACza,YAAY,GAAGya,IAAI;UACxB,IAAI,CAAC7H,aAAa,GAAG,IAAI;UACzB,IAAI,CAACtC,cAAc,CAACyY,IAAI,CAAC,UAAU,CAAC;SACrC,MAAM;UACL,IAAI,CAACnW,aAAa,GAAG,KAAK;UAC1B,IAAI,CAAC5S,YAAY,GAAG,IAAI;;MAE5B;KACD,CAAC;IACF,IAAI,CAACuS,aAAa,CAAC1J,GAAG,CAACwgB,OAAO,CAAC;IAE/B,MAAME,aAAa,GAAG,IAAI,CAACjZ,cAAc,CAACkZ,WAAW,CAACvT,SAAS,CAAC;MAC9DluB,IAAI,EAAG0yB,IAAI,IAAI;QACb,IAAI,CAACna,UAAU,GAAGma,IAAI;QACtB,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC5H,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAAC4D,oBAAoB,EAAE;SAC5B,MAAM;UACL,IAAI,CAAC5D,mBAAmB,GAAG,KAAK;UAChC,IAAI,CAAC6D,mBAAmB,EAAE;UAC1B,IAAI,CAACC,oBAAoB,EAAE;;MAE/B;KACD,CAAC;IACF,IAAI,CAACpE,aAAa,CAAC1J,GAAG,CAAC0gB,aAAa,CAAC;IAErC;IACA,MAAME,cAAc,GAAG,IAAI,CAACnZ,cAAc,CAACoZ,YAAY,CAACzT,SAAS,CAAC;MAChEluB,IAAI,EAAG4hC,MAA0B,IAAI;QACnC,IAAIA,MAAM,IAAI,IAAI,CAAChW,iBAAiB,EAAE;UACpC,IAAI,CAACA,iBAAiB,CAACiW,SAAS,GAAGD,MAAM;;MAE7C;KACD,CAAC;IACF,IAAI,CAACpX,aAAa,CAAC1J,GAAG,CAAC4gB,cAAc,CAAC;IAEtC;IACA,MAAMI,eAAe,GAAG,IAAI,CAACvZ,cAAc,CAACwZ,aAAa,CAAC7T,SAAS,CAAC;MAClEluB,IAAI,EAAG4hC,MAA0B,IAAI;QACnC,IAAIA,MAAM,IAAI,IAAI,CAAC/V,kBAAkB,EAAE;UACrC,IAAI,CAACA,kBAAkB,CAACgW,SAAS,GAAGD,MAAM;;MAE9C;KACD,CAAC;IACF,IAAI,CAACpX,aAAa,CAAC1J,GAAG,CAACghB,eAAe,CAAC;EACzC;EAEA;;;;EAIApZ,YAAYA,CAAChpB,IAAuB;IAClC,IAAI,CAAC,IAAI,CAACiC,gBAAgB,IAAI,CAAC,IAAI,CAACA,gBAAgB,CAAC/B,EAAE,EAAE;MACvD;;IAGF,IAAI,CAAC2oB,cAAc,CAACG,YAAY,CAC9B,IAAI,CAAC/mB,gBAAgB,CAAC/B,EAAE,EACxBF,IAAI,KAAK,OAAO,GAAGsB,kEAAQ,CAACghC,KAAK,GAAGhhC,kEAAQ,CAACihC,KAAK,EAClD,IAAI,CAACpY,YAAY,EAAEjqB,EAAE,CACtB,CAACsuB,SAAS,CAAC;MACVluB,IAAI,EAAG0yB,IAAI,IAAI;QACb;MAAA,CACD;MACD1nB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CACzB,mDAAmD,CACpD;MACH;KACD,CAAC;EACJ;EAEA;;;EAGA2X,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACE,YAAY,EAAE;MACtB;;IAGF,IAAI,CAACsQ,cAAc,CAACxQ,UAAU,CAAC,IAAI,CAACE,YAAY,CAACrY,EAAE,CAAC,CAACsuB,SAAS,CAAC;MAC7DluB,IAAI,EAAG0yB,IAAI,IAAI;QACb,IAAI,CAAC7H,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC5S,YAAY,GAAG,IAAI;QACxB,IAAI,CAACI,cAAc,GAAG,IAAI,CAACJ,YAAY,EAAEvY,IAAI,KAAK,OAAO;QACzD,IAAI,CAACkZ,WAAW,GAAG,YAAY;QAC/B,IAAI,CAAC8Q,YAAY,CAACvpB,WAAW,CAAC,gBAAgB,CAAC;MACjD,CAAC;MACD6K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CACzB,oDAAoD,CACrD;QACD,IAAI,CAACyqB,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC5S,YAAY,GAAG,IAAI;MAC1B;KACD,CAAC;EACJ;EAEA;;;EAGAL,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACK,YAAY,EAAE;MACtB;;IAGF,IAAI,CAACsQ,cAAc,CAAC3Q,UAAU,CAAC,IAAI,CAACK,YAAY,CAACrY,EAAE,CAAC,CAACsuB,SAAS,CAAC;MAC7DluB,IAAI,EAAG0yB,IAAI,IAAI;QACb,IAAI,CAAC7H,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC5S,YAAY,GAAG,IAAI;MAC1B,CAAC;MACDjN,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC6f,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC5S,YAAY,GAAG,IAAI;MAC1B;KACD,CAAC;EACJ;EAEA;;;EAGA2B,OAAOA,CAAA;IACL,IAAIrB,UAAU,GAAQ,IAAI;IAE1B,MAAMyf,GAAG,GAAG,IAAI,CAACzP,cAAc,CAACkZ,WAAW,CAACvT,SAAS,CAAEwE,IAAI,IAAI;MAC7Dna,UAAU,GAAGma,IAAI;MAEjB,IAAI,CAACna,UAAU,EAAE;QACf;;MAGF,IAAI,CAACgQ,cAAc,CAAC3O,OAAO,CAACrB,UAAU,CAAC3Y,EAAE,CAAC,CAACsuB,SAAS,CAAC;QACnDluB,IAAI,EAAG0yB,IAAI,IAAI;UACb;QAAA,CACD;QACD1nB,KAAK,EAAGA,KAAK,IAAI;UACf;QAAA;OAEH,CAAC;IACJ,CAAC,CAAC;IAEFgtB,GAAG,CAACkK,WAAW,EAAE;EACnB;EAiEA;EAEA;EAEA;EACA;EACA;EAEA9d,uBAAuBA,CAAA;IACrB,IAAI,CAACgI,WAAW,CAAC,cAAc,CAAC;IAChC,IAAI,IAAI,CAACvD,qBAAqB,EAAE;MAC9B,IAAI,CAAC5E,iBAAiB,EAAE;;EAE5B;EAEA;;;EAGAA,iBAAiBA,CAACke,OAAA,GAAmB,KAAK;IACxC,MAAMC,OAAO,GAAG,IAAI,CAAC7Z,cAAc,CAAC8Z,gBAAgB,CAClDF,OAAO,EACP,CAAC,EACD,EAAE,CACH,CAACjU,SAAS,CAAC;MACVluB,IAAI,EAAGulB,aAAa,IAAI;QACtB,IAAI4c,OAAO,EAAE;UACX,IAAI,CAAC5c,aAAa,GAAGA,aAAa;SACnC,MAAM;UACL,IAAI,CAACA,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE,GAAGA,aAAa,CAAC;;QAGhE,IAAI,CAAC4J,uBAAuB,EAAE;MAChC,CAAC;MACDnkB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CACzB,6CAA6C,CAC9C;MACH;KACD,CAAC;IAEF,IAAI,CAACoqB,aAAa,CAAC1J,GAAG,CAACshB,OAAO,CAAC;EACjC;EAyBA;;;EAGAlhB,2BAA2BA,CAACohB,cAAsB;IAChD,IAAI,IAAI,CAACziB,qBAAqB,CAAC+B,GAAG,CAAC0gB,cAAc,CAAC,EAAE;MAClD,IAAI,CAACziB,qBAAqB,CAAC0iB,MAAM,CAACD,cAAc,CAAC;KAClD,MAAM;MACL,IAAI,CAACziB,qBAAqB,CAACiB,GAAG,CAACwhB,cAAc,CAAC;;EAElD;EAEA;;;EAGA7iB,4BAA4BA,CAAA;IAC1B,MAAM+iB,qBAAqB,GAAG,IAAI,CAACld,wBAAwB,EAAE;IAC7D,MAAMmd,WAAW,GAAGD,qBAAqB,CAACE,KAAK,CAAE1T,CAAC,IAChD,IAAI,CAACnP,qBAAqB,CAAC+B,GAAG,CAACoN,CAAC,CAACpvB,EAAE,CAAC,CACrC;IAED,IAAI6iC,WAAW,EAAE;MACfD,qBAAqB,CAAC7J,OAAO,CAAE3J,CAAC,IAC9B,IAAI,CAACnP,qBAAqB,CAAC0iB,MAAM,CAACvT,CAAC,CAACpvB,EAAE,CAAC,CACxC;KACF,MAAM;MACL4iC,qBAAqB,CAAC7J,OAAO,CAAE3J,CAAC,IAC9B,IAAI,CAACnP,qBAAqB,CAACiB,GAAG,CAACkO,CAAC,CAACpvB,EAAE,CAAC,CACrC;;EAEL;EAEA;;;EAGAggB,2BAA2BA,CAAA;IACzB,MAAM4iB,qBAAqB,GAAG,IAAI,CAACld,wBAAwB,EAAE;IAC7D,OACEkd,qBAAqB,CAAC16B,MAAM,GAAG,CAAC,IAChC06B,qBAAqB,CAACE,KAAK,CAAE1T,CAAC,IAAK,IAAI,CAACnP,qBAAqB,CAAC+B,GAAG,CAACoN,CAAC,CAACpvB,EAAE,CAAC,CAAC;EAE5E;EAEA;;;EAGAmf,kBAAkBA,CAAA;IAChB,MAAM4jB,WAAW,GAAGhS,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/Q,qBAAqB,CAAC;IAC1D,IAAI8iB,WAAW,CAAC76B,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAAC4hB,YAAY,CAACrpB,WAAW,CAAC,kCAAkC,CAAC;MACjE;;IAGF;IAEA,MAAMuiC,OAAO,GAAG,IAAI,CAACra,cAAc,CAAC0Y,UAAU,CAAC0B,WAAW,CAAC,CAACzU,SAAS,CAAC;MACpEluB,IAAI,EAAGm5B,MAAM,IAAI;QACf,IAAI,CAAC5T,aAAa,GAAG,IAAI,CAACA,aAAa,CAACyN,GAAG,CAAEhE,CAAC,IAC5C2T,WAAW,CAACjP,QAAQ,CAAC1E,CAAC,CAACpvB,EAAE,CAAC,GACtB;UAAE,GAAGovB,CAAC;UAAEtN,MAAM,EAAE,IAAI;UAAEmhB,MAAM,EAAE,IAAItX,IAAI;QAAE,CAAE,GAC1CyD,CAAC,CACN;QAED,IAAI,CAACnP,qBAAqB,CAACpf,KAAK,EAAE;QAClC,IAAI,CAAC0uB,uBAAuB,EAAE;QAC9B;QAEA,IAAI,CAACzF,YAAY,CAACvpB,WAAW,CAC3B,GAAGg5B,MAAM,CAAC2J,SAAS,0CAA0C,CAC9D;MACH,CAAC;MACD93B,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CACzB,2CAA2C,CAC5C;MACH;KACD,CAAC;IAEF,IAAI,CAACoqB,aAAa,CAAC1J,GAAG,CAAC8hB,OAAO,CAAC;EACjC;EAEA;;;EAGA1iB,aAAaA,CAAA;IACX,MAAM6iB,mBAAmB,GAAG,IAAI,CAACxd,aAAa,CAAChlB,MAAM,CAAEyuB,CAAC,IAAK,CAACA,CAAC,CAACtN,MAAM,CAAC;IACvE,IAAIqhB,mBAAmB,CAACj7B,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAAC4hB,YAAY,CAACppB,QAAQ,CAAC,6BAA6B,CAAC;MACzD;;IAGF,MAAM0iC,SAAS,GAAGD,mBAAmB,CAAC/P,GAAG,CAAEhE,CAAC,IAAKA,CAAC,CAACpvB,EAAE,CAAC;IACtD;IAEA,MAAMgjC,OAAO,GAAG,IAAI,CAACra,cAAc,CAAC0Y,UAAU,CAAC+B,SAAS,CAAC,CAAC9U,SAAS,CAAC;MAClEluB,IAAI,EAAGm5B,MAAM,IAAI;QACf,IAAI,CAAC5T,aAAa,GAAG,IAAI,CAACA,aAAa,CAACyN,GAAG,CAAEhE,CAAC,IAC5CgU,SAAS,CAACtP,QAAQ,CAAC1E,CAAC,CAACpvB,EAAE,CAAC,GACpB;UAAE,GAAGovB,CAAC;UAAEtN,MAAM,EAAE,IAAI;UAAEmhB,MAAM,EAAE,IAAItX,IAAI;QAAE,CAAE,GAC1CyD,CAAC,CACN;QAED,IAAI,CAACG,uBAAuB,EAAE;QAC9B;QAEA,IAAI,CAACzF,YAAY,CAACvpB,WAAW,CAC3B,sDAAsD,CACvD;MACH,CAAC;MACD6K,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CACzB,2CAA2C,CAC5C;MACH;KACD,CAAC;IAEF,IAAI,CAACoqB,aAAa,CAAC1J,GAAG,CAAC8hB,OAAO,CAAC;EACjC;EAEA;;;EAGA1jB,8BAA8BA,CAAA;IAC5B,IAAI,IAAI,CAACW,qBAAqB,CAACC,IAAI,KAAK,CAAC,EAAE;MACzC,IAAI,CAAC4J,YAAY,CAACrpB,WAAW,CAAC,kCAAkC,CAAC;MACjE;;IAGF,IAAI,CAAC4qB,sBAAsB,GAAG,IAAI;EACpC;EAEA;;;EAGAxE,2BAA2BA,CAAA;IACzB,MAAMkc,WAAW,GAAGhS,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/Q,qBAAqB,CAAC;IAC1D,IAAI8iB,WAAW,CAAC76B,MAAM,KAAK,CAAC,EAAE;IAE9B,IAAI,CAACuX,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAAC4L,sBAAsB,GAAG,KAAK;IAEnC,MAAMgY,SAAS,GAAG,IAAI,CAAC1a,cAAc,CAAC2a,2BAA2B,CAC/DP,WAAW,CACZ,CAACzU,SAAS,CAAC;MACVluB,IAAI,EAAGm5B,MAAM,IAAI;QACf,IAAI,CAAC5T,aAAa,GAAG,IAAI,CAACA,aAAa,CAAChlB,MAAM,CAC3CyuB,CAAC,IAAK,CAAC2T,WAAW,CAACjP,QAAQ,CAAC1E,CAAC,CAACpvB,EAAE,CAAC,CACnC;QACD,IAAI,CAACigB,qBAAqB,CAACpf,KAAK,EAAE;QAClC,IAAI,CAAC0uB,uBAAuB,EAAE;QAC9B,IAAI,CAAC9P,uBAAuB,GAAG,KAAK;QAEpC,IAAI,CAACqK,YAAY,CAACvpB,WAAW,CAC3B,GAAGg5B,MAAM,CAAC32B,KAAK,+BAA+B,CAC/C;MACH,CAAC;MACDwI,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACqU,uBAAuB,GAAG,KAAK;QACpC,IAAI,CAACqK,YAAY,CAACtpB,SAAS,CACzB,iDAAiD,CAClD;MACH;KACD,CAAC;IAEF,IAAI,CAACoqB,aAAa,CAAC1J,GAAG,CAACmiB,SAAS,CAAC;EACnC;EAEA;;;EAGAzhB,kBAAkBA,CAAC8gB,cAAsB;IACvC,MAAMW,SAAS,GAAG,IAAI,CAAC1a,cAAc,CAAC/G,kBAAkB,CACtD8gB,cAAc,CACf,CAACpU,SAAS,CAAC;MACVluB,IAAI,EAAGm5B,MAAM,IAAI;QACf,IAAI,CAAC5T,aAAa,GAAG,IAAI,CAACA,aAAa,CAAChlB,MAAM,CAC3CyuB,CAAC,IAAKA,CAAC,CAACpvB,EAAE,KAAK0iC,cAAc,CAC/B;QACD,IAAI,CAACziB,qBAAqB,CAAC0iB,MAAM,CAACD,cAAc,CAAC;QACjD,IAAI,CAACnT,uBAAuB,EAAE;QAC9B,IAAI,CAACzF,YAAY,CAACvpB,WAAW,CAAC,wBAAwB,CAAC;MACzD,CAAC;MACD6K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CACzB,kDAAkD,CACnD;MACH;KACD,CAAC;IAEF,IAAI,CAACoqB,aAAa,CAAC1J,GAAG,CAACmiB,SAAS,CAAC;EACnC;EAEA;;;EAGA1iB,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACgF,aAAa,CAACzd,MAAM,KAAK,CAAC,EAAE;IAErC;IACA,IACE,CAACoyB,OAAO,CACN,8FAA8F,CAC/F,EACD;MACA;;IAGF,IAAI,CAAC7a,uBAAuB,GAAG,IAAI;IAEnC,MAAM8jB,YAAY,GAAG,IAAI,CAAC5a,cAAc,CAAChI,sBAAsB,EAAE,CAAC2N,SAAS,CACzE;MACEluB,IAAI,EAAGm5B,MAAM,IAAI;QACf,IAAI,CAAC5T,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC1F,qBAAqB,CAACpf,KAAK,EAAE;QAClC,IAAI,CAAC0uB,uBAAuB,EAAE;QAC9B,IAAI,CAAC9P,uBAAuB,GAAG,KAAK;QAEpC,IAAI,CAACqK,YAAY,CAACvpB,WAAW,CAC3B,GAAGg5B,MAAM,CAAC32B,KAAK,uCAAuC,CACvD;MACH,CAAC;MACDwI,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACqU,uBAAuB,GAAG,KAAK;QACpC,IAAI,CAACqK,YAAY,CAACtpB,SAAS,CACzB,2DAA2D,CAC5D;MACH;KACD,CACF;IAED,IAAI,CAACoqB,aAAa,CAAC1J,GAAG,CAACqiB,YAAY,CAAC;EACtC;EAEA;;;EAGA7c,yBAAyBA,CAAA;IACvB,IAAI,CAAC2E,sBAAsB,GAAG,KAAK;EACrC;EA0BA;EACA;EACA;EAEA;;;EAGQ8K,qBAAqBA,CAAA;IAC3B,MAAMqN,SAAS,GAAG,IAAI,CAAC7a,cAAc,CAACwN,qBAAqB,EAAE,CAAC7H,SAAS,CAAC;MACtEluB,IAAI,EAAG8wB,IAAU,IAAI;QACnB,IAAI,CAACuS,sBAAsB,CAACvS,IAAI,CAAC;MACnC,CAAC;MACD9lB,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;IAEF,IAAI,CAACwf,aAAa,CAAC1J,GAAG,CAACsiB,SAAS,CAAC;EACnC;EAEA;;;EAGQC,sBAAsBA,CAACvS,IAAU;IACvC,IAAI,CAACA,IAAI,CAAClxB,EAAE,EAAE;IAEd,IAAIkxB,IAAI,CAACjvB,QAAQ,EAAE;MACjB,IAAI,CAACupB,WAAW,CAACkY,GAAG,CAACxS,IAAI,CAAClxB,EAAE,EAAEkxB,IAAI,CAAC;KACpC,MAAM;MACL,IAAI,CAAC1F,WAAW,CAACmX,MAAM,CAACzR,IAAI,CAAClxB,EAAE,CAAC;;IAGlC,IAAI,IAAI,CAAC+B,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC/B,EAAE,KAAKkxB,IAAI,CAAClxB,EAAE,EAAE;MACjE,IAAI,CAAC+B,gBAAgB,GAAG;QAAE,GAAG,IAAI,CAACA,gBAAgB;QAAE,GAAGmvB;MAAI,CAAE;;EAEjE;EAEA;;;EAGQkF,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACzjB,aAAa,EAAE;IAEzB;IACA,MAAMgxB,YAAY,GAAG,IAAI,CAAChb,cAAc,CAACib,aAAa,CACpD,IAAI,CAACjxB,aAAa,CACnB,CAAC2b,SAAS,CAAC;MACVluB,IAAI,EAAG8wB,IAAU,IAAI;QACnB,IAAI,CAACxsB,iBAAiB,GAAG,QAAQ;QACjC,IAAI,CAACgnB,gBAAgB,GAAG,IAAIC,IAAI,EAAE;MACpC,CAAC;MACDvgB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC2e,MAAM,CAAC3e,KAAK,CACf,aAAa,EACb,2CAA2C,EAC3CA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAACwf,aAAa,CAAC1J,GAAG,CAACyiB,YAAY,CAAC;EACtC;EAEA;;;EAGQtN,qBAAqBA,CAAA;IAC3B;IACA,MAAMwN,MAAM,GAAG,CACb,WAAW,EACX,WAAW,EACX,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,OAAO,CACR;IAEDA,MAAM,CAAC9K,OAAO,CAAEjH,KAAK,IAAI;MACvBwE,QAAQ,CAACC,gBAAgB,CAACzE,KAAK,EAAE,IAAI,CAACgS,cAAc,CAACrN,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IACxE,CAAC,CAAC;EACJ;EAEA;;;EAGQqN,cAAcA,CAAA;IACpB,IAAI,CAACpY,gBAAgB,GAAG,IAAIC,IAAI,EAAE;IAElC;IACA,IAAI,IAAI,CAACC,eAAe,EAAE;MACxB8M,YAAY,CAAC,IAAI,CAAC9M,eAAe,CAAC;;IAGpC;IACA,IACE,IAAI,CAAClnB,iBAAiB,KAAK,MAAM,IACjC,IAAI,CAACA,iBAAiB,KAAK,SAAS,EACpC;MACA,IAAI,CAACN,gBAAgB,CAAC,QAAQ,CAAC;;IAGjC;IACA,IAAI,CAACwnB,eAAe,GAAGvrB,UAAU,CAAC,MAAK;MACrC,IAAI,IAAI,CAACqE,iBAAiB,KAAK,QAAQ,EAAE;QACvC,IAAI,CAACN,gBAAgB,CAAC,MAAM,CAAC;;IAEjC,CAAC,EAAE,IAAI,CAAC2nB,aAAa,CAAC;EACxB;EAEQgY,oBAAoBA,CAAA;IAC1B,MAAMC,GAAG,GAAG,IAAIrY,IAAI,EAAE;IACtB,MAAMsY,qBAAqB,GACzBD,GAAG,CAACnM,OAAO,EAAE,GAAG,IAAI,CAACnM,gBAAgB,CAACmM,OAAO,EAAE;IAEjD,IACEoM,qBAAqB,GAAG,IAAI,CAAClY,aAAa,IAC1C,IAAI,CAACrnB,iBAAiB,KAAK,QAAQ,EACnC;MACA,IAAI,CAACN,gBAAgB,CAAC,MAAM,CAAC;KAC9B,MAAM,IACL6/B,qBAAqB,GAAG,OAAO,IAC/B,IAAI,CAACv/B,iBAAiB,KAAK,MAAM,EACjC;MACA,IAAI,CAACN,gBAAgB,CAAC,SAAS,CAAC;;EAEpC;EAEA;;;EAGAA,gBAAgBA,CAACssB,MAAc;IAC7B,IAAI,CAAC,IAAI,CAAC/d,aAAa,EAAE;IAEzB,MAAMuxB,cAAc,GAAG,IAAI,CAACx/B,iBAAiB;IAE7C,IAAIy/B,gBAAgB;IACpB,IAAIzT,MAAM,KAAK,QAAQ,EAAE;MACvByT,gBAAgB,GAAG,IAAI,CAACxb,cAAc,CAACib,aAAa,CAAC,IAAI,CAACjxB,aAAa,CAAC;KACzE,MAAM;MACLwxB,gBAAgB,GAAG,IAAI,CAACxb,cAAc,CAACyb,cAAc,CAAC,IAAI,CAACzxB,aAAa,CAAC;;IAG3E,MAAM0xB,SAAS,GAAGF,gBAAgB,CAAC7V,SAAS,CAAC;MAC3CluB,IAAI,EAAG8wB,IAAU,IAAI;QACnB,IAAI,CAACxsB,iBAAiB,GAAGgsB,MAAM;QAE/B,IAAIA,MAAM,KAAKwT,cAAc,EAAE;UAC7B,MAAMI,UAAU,GAAG,IAAI,CAACh/B,aAAa,CAACorB,MAAM,CAAC;UAC7C,IAAI,CAAC5G,YAAY,CAACppB,QAAQ,CAAC,YAAY4jC,UAAU,EAAE,CAAC;;MAExD,CAAC;MACDl5B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC2e,MAAM,CAAC3e,KAAK,CACf,aAAa,EACb,yCAAyC,EACzCA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAACwf,aAAa,CAAC1J,GAAG,CAACmjB,SAAS,CAAC;EACnC;EAEA;;;EAGAE,eAAeA,CAAA;IACb,MAAMC,QAAQ,GAAG,IAAI,CAAC7b,cAAc,CAAC8b,WAAW,CAC9C,KAAK,EACL/H,SAAS,EACT,CAAC,EACD,EAAE,EACF,UAAU,EACV,KAAK,EACL,IAAI,CACL,CAACpO,SAAS,CAAC;MACVluB,IAAI,EAAGskC,KAAa,IAAI;QACtBA,KAAK,CAAC3L,OAAO,CAAE7H,IAAI,IAAI;UACrB,IAAIA,IAAI,CAACjvB,QAAQ,IAAIivB,IAAI,CAAClxB,EAAE,EAAE;YAC5B,IAAI,CAACwrB,WAAW,CAACkY,GAAG,CAACxS,IAAI,CAAClxB,EAAE,EAAEkxB,IAAI,CAAC;;QAEvC,CAAC,CAAC;MACJ,CAAC;MACD9lB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC2e,MAAM,CAAC3e,KAAK,CACf,aAAa,EACb,qDAAqD,EACrDA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAACwf,aAAa,CAAC1J,GAAG,CAACsjB,QAAQ,CAAC;EAClC;EAkCA;EACAj/B,gBAAgBA,CAAA;IACd,OAAO60B,MAAM,CAACuK,OAAO,CAAC,IAAI,CAAC3U,YAAY,CAAC,CAACoD,GAAG,CAAC,CAAC,CAAC/uB,GAAG,EAAEugC,MAAM,CAAC,MAAM;MAC/DvgC,GAAG;MACH,GAAGugC;KACJ,CAAC,CAAC;EACL;EA8BA;EACA3+B,eAAeA,CAAA;IACb,OAAO,IAAI,CAACqqB,WAAW;EACzB;EAkCA;EACAuU,eAAeA,CAAA;IACb,OAAO,IAAI,CAACtU,WAAW,CAAC5vB,MAAM,CAAE05B,KAAK,IAAKA,KAAK,CAACz6B,IAAI,EAAE,CAAC;EACzD;EAgCA;EACAklC,eAAeA,CAACnkC,MAAc;IAC5B,IAAI,CAACurB,gBAAgB,GAAGvrB,MAAM;EAChC;EAEAokC,gBAAgBA,CAAA;IACd,OAAOhU,KAAK,CAACC,IAAI,CAAC,IAAI,CAACxF,WAAW,CAACyF,MAAM,EAAE,CAAC;EAC9C;EAEA;EAEA;EACA/jB,mBAAmBA,CAACrN,OAAY;IAC9B,IAAI,CAAC0W,iBAAiB,GAAG1W,OAAO;EAClC;EAEAwW,WAAWA,CAAA;IACT,IAAI,CAACE,iBAAiB,GAAG,IAAI;EAC/B;EAEAlJ,gBAAgBA,CAACxN,OAAY;IAC3B,IAAI,CAAC4e,iBAAiB,GAAG5e,OAAO;IAChC,IAAI,CAAC0rB,gBAAgB,GAAG,IAAI;EAC9B;EAEAlO,iBAAiBA,CAAA;IACf,IAAI,CAACkO,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC9M,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACE,qBAAqB,GAAG,EAAE;EACjC;EAmFA6X,eAAeA,CAAC1E,KAAY;IAC1B,MAAM0C,MAAM,GAAG1C,KAAK,CAAC0C,MAAqB;IAE1C,IACE,CAACA,MAAM,CAACwQ,OAAO,CAAC,sBAAsB,CAAC,IACvC,CAACxQ,MAAM,CAACwQ,OAAO,CAAC,YAAY,CAAC,EAC7B;MACA,IAAI,CAACla,iBAAiB,GAAG,KAAK;;IAGhC,IAAI,CAAC0J,MAAM,CAACwQ,OAAO,CAAC,eAAe,CAAC,IAAI,CAACxQ,MAAM,CAACwQ,OAAO,CAAC,YAAY,CAAC,EAAE;MACrE,IAAI,CAACha,eAAe,GAAG,KAAK;;EAEhC;EA4LAia,WAAWA,CAAA;IACT,IAAI,CAACjJ,mBAAmB,EAAE;IAE1B,IAAI,IAAI,CAACrD,aAAa,EAAE;MACtBD,YAAY,CAAC,IAAI,CAACC,aAAa,CAAC;;IAGlC,IAAI,IAAI,CAAC/M,eAAe,EAAE;MACxB8M,YAAY,CAAC,IAAI,CAAC9M,eAAe,CAAC;;IAGpC,IAAI,IAAI,CAACjZ,aAAa,EAAE;MACtB,IAAI,CAACgW,cAAc,CAACyb,cAAc,CAAC,IAAI,CAACzxB,aAAa,CAAC,CAAC2b,SAAS,EAAE;;IAGpE,IAAI,CAAC1D,aAAa,CAAC0X,WAAW,EAAE;EAClC;;;uBAp6EW5Z,oBAAoB,EAAAnnB,+DAAA,CAAA4jC,yEAAA,GAAA5jC,+DAAA,CAAA6jC,4DAAA,GAAA7jC,+DAAA,CAAA+jC,8EAAA,GAAA/jC,+DAAA,CAAAikC,wDAAA,GAAAjkC,+DAAA,CAAAmkC,mFAAA,GAAAnkC,+DAAA,CAAA6jC,oDAAA,GAAA7jC,+DAAA,CAAAskC,wEAAA,GAAAtkC,+DAAA,CAAAukC,0EAAA,GAAAvkC,+DAAA,CAAAA,4DAAA;IAAA;EAAA;;;YAApBmnB,oBAAoB;MAAAud,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC9BjC7kC,4DAAA,aAGC;UAIGA,uDAAA,aAEO;UAEPA,4DAAA,aAAyE;UACvEA,uDAAA,aAAqE;UACvEA,0DAAA,EAAM;UAIRA,4DAAA,aAAkC;UACxBA,wDAAA,mBAAA+kC,sDAAA;YAAA,OAASD,GAAA,CAAA3F,qBAAA,EAAuB;UAAA,EAAC;UACvCn/B,uDAAA,WAAiC;UACnCA,0DAAA,EAAS;UAETA,4DAAA,aAAgC;UAE5BA,uDAAA,eAGE;UACFA,wDAAA,KAAAglC,qCAAA,mBAGQ;UACVhlC,0DAAA,EAAM;UAGNA,wDAAA,KAAAilC,oCAAA,kBAWM;UACRjlC,0DAAA,EAAM;UAENA,4DAAA,eAA8B;UAE5BA,wDAAA,KAAAklC,6CAAA,2BAsBe;UAGfllC,4DAAA,eAAsB;UAElBA,wDAAA,mBAAAmlC,uDAAA;YAAA,OAASL,GAAA,CAAA/hC,oBAAA,EAAsB;UAAA,EAAC;UAOhC/C,uDAAA,aAGK;UAELA,wDAAA,KAAAolC,qCAAA,mBAGQ;UACVplC,0DAAA,EAAS;UAGTA,wDAAA,KAAAqlC,oCAAA,mBA8DM;UACRrlC,0DAAA,EAAM;UAGNA,4DAAA,eAAsB;UAElBA,wDAAA,mBAAAslC,uDAAA;YAAA,OAASR,GAAA,CAAA/Y,mBAAA,EAAqB;UAAA,EAAC;UAG/B/rB,uDAAA,aAA8B;UAChCA,0DAAA,EAAS;UAGTA,wDAAA,KAAAulC,oCAAA,kBAgCM;UACRvlC,0DAAA,EAAM;UAGNA,4DAAA,eAAsB;UAElBA,wDAAA,mBAAAwlC,uDAAA;YAAA,OAASV,GAAA,CAAA9Y,cAAA,EAAgB;UAAA,EAAC;UAI1BhsB,uDAAA,aAAiC;UACnCA,0DAAA,EAAS;UAGTA,wDAAA,KAAAylC,oCAAA,mBA+CM;UACRzlC,0DAAA,EAAM;UAKVA,wDAAA,KAAA0lC,oCAAA,mBA0GM;UAGN1lC,wDAAA,KAAA2lC,oCAAA,mBAqHM;UAGN3lC,4DAAA,mBAKC;UADCA,wDAAA,oBAAA4lC,qDAAA9+B,MAAA;YAAA,OAAUg+B,GAAA,CAAAzJ,QAAA,CAAAv0B,MAAA,CAAgB;UAAA,EAAC;UAG3B9G,wDAAA,KAAA6lC,oCAAA,kBAiBM;UAGN7lC,wDAAA,KAAA8lC,oCAAA,kBAwBM;UAGN9lC,wDAAA,KAAA+lC,oCAAA,kBAiBM;UAGN/lC,wDAAA,KAAAgmC,oCAAA,mBAmBM;UAGNhmC,wDAAA,KAAAimC,6CAAA,2BAmae;UAGfjmC,wDAAA,KAAAkmC,4CAAA,iCAAAlmC,oEAAA,CAiBc;UAGdA,wDAAA,KAAAomC,oCAAA,kBAoBM;UACRpmC,0DAAA,EAAM;UAGNA,4DAAA,eAAsC;UAEpCA,wDAAA,KAAAqmC,oCAAA,kBAKM;UAGNrmC,wDAAA,KAAAsmC,oCAAA,mBA8BM;UAGNtmC,wDAAA,KAAAumC,oCAAA,kBAsBM;UAENvmC,4DAAA,gBAIC;UAFCA,wDAAA,sBAAAwmC,wDAAA;YAAA,OAAY1B,GAAA,CAAAtK,WAAA,EAAa;UAAA,EAAC;UAI1Bx6B,4DAAA,eAAkC;UAG9BA,wDAAA,mBAAAymC,uDAAA;YAAA,OAAS3B,GAAA,CAAA7Y,iBAAA,EAAmB;UAAA,EAAC;UAI7BjsB,uDAAA,aAA4B;UAC9BA,0DAAA,EAAS;UACTA,4DAAA,kBAIC;UAFCA,wDAAA,mBAAA0mC,uDAAA;YAAA1mC,2DAAA,CAAA2mC,KAAA;YAAA,MAAAC,IAAA,GAAA5mC,yDAAA;YAAA,OAASA,yDAAA,CAAA4mC,IAAA,CAAAtM,KAAA,EAAiB;UAAA,EAAC;UAG3Bt6B,uDAAA,aAAgC;UAChCA,4DAAA,qBAME;UAHAA,wDAAA,oBAAA8mC,uDAAAhgC,MAAA;YAAA,OAAUg+B,GAAA,CAAArN,cAAA,CAAA3wB,MAAA,CAAsB;UAAA,EAAC;UAHnC9G,0DAAA,EAME;UAKNA,wDAAA,KAAA+mC,mDAAA,iCAKsB;UAEtB/mC,wDAAA,KAAAgnC,sCAAA,oBAOE;UAEFhnC,wDAAA,KAAAinC,uCAAA,qBAOS;UAETjnC,wDAAA,KAAAknC,uCAAA,qBAQS;UACXlnC,0DAAA,EAAO;UAITA,wDAAA,KAAAmnC,oCAAA,mBA+BM;UAGNnnC,wDAAA,KAAAonC,oCAAA,kBAwIM;UAGNpnC,wDAAA,KAAAqnC,oCAAA,oBA2LM;UACRrnC,0DAAA,EAAM;UAGNA,wDAAA,KAAAsnC,oCAAA,oBAmSM;UAGNtnC,wDAAA,KAAAunC,oCAAA,mBAyBM;UAGNvnC,wDAAA,KAAAwnC,oCAAA,mBA8BM;UAGNxnC,wDAAA,KAAAynC,4CAAA,iCAAAznC,oEAAA,CAiBc;UAEdA,wDAAA,KAAA0nC,4CAAA,iCAAA1nC,oEAAA,CAqBc;UAGdA,wDAAA,KAAA2nC,4CAAA,iCAAA3nC,oEAAA,CAcc;;;;;;UAr7DZA,wDAAA,YAAA8kC,GAAA,CAAAxb,aAAA,CAAyB;UAuBjBtpB,uDAAA,IAAqE;UAArEA,wDAAA,SAAA8kC,GAAA,CAAAtkC,gBAAA,kBAAAskC,GAAA,CAAAtkC,gBAAA,CAAAkI,KAAA,yCAAA1I,2DAAA,CAAqE;UAIpEA,uDAAA,GAAgC;UAAhCA,wDAAA,SAAA8kC,GAAA,CAAAtkC,gBAAA,kBAAAskC,GAAA,CAAAtkC,gBAAA,CAAAE,QAAA,CAAgC;UAM/BV,uDAAA,GAAsB;UAAtBA,wDAAA,SAAA8kC,GAAA,CAAAtkC,gBAAA,CAAsB;UAgBKR,uDAAA,GAAqB;UAArBA,wDAAA,YAAA8kC,GAAA,CAAAxd,gBAAA,GAAqB;UA6BlDtnB,uDAAA,GAEE;UAFFA,wDAAA,YAAAA,6DAAA,KAAA4nC,IAAA,EAAA9C,GAAA,CAAA/a,kBAAA,EAEE;UAIA/pB,uDAAA,GAA0C;UAA1CA,wDAAA,CAAA8kC,GAAA,CAAAhV,aAAA,CAAAgV,GAAA,CAAA3hC,iBAAA,EAA0C;UAC1CnD,wDAAA,YAAA8kC,GAAA,CAAAhhC,cAAA,CAAAghC,GAAA,CAAA3hC,iBAAA,EAA6C;UAI5CnD,uDAAA,GAAsB;UAAtBA,wDAAA,SAAA8kC,GAAA,CAAA7hC,gBAAA,CAAsB;UAOxBjD,uDAAA,GAAwB;UAAxBA,wDAAA,SAAA8kC,GAAA,CAAA/a,kBAAA,CAAwB;UA2ExB/pB,uDAAA,GAAuB;UAAvBA,wDAAA,SAAA8kC,GAAA,CAAAvb,iBAAA,CAAuB;UA8CvBvpB,uDAAA,GAAkB;UAAlBA,wDAAA,SAAA8kC,GAAA,CAAAtb,YAAA,CAAkB;UAqDxBxpB,uDAAA,GAAmB;UAAnBA,wDAAA,SAAA8kC,GAAA,CAAAtd,aAAA,CAAmB;UA6GnBxnB,uDAAA,GAAwB;UAAxBA,wDAAA,SAAA8kC,GAAA,CAAArd,kBAAA,CAAwB;UA0HzBznB,uDAAA,GAAgD;UAAhDA,wDAAA,YAAAA,6DAAA,KAAA6nC,IAAA,EAAA/C,GAAA,CAAAtd,aAAA,EAAgD;UAI1CxnB,uDAAA,GAAa;UAAbA,wDAAA,SAAA8kC,GAAA,CAAAnxB,OAAA,CAAa;UAqBhB3T,uDAAA,GAAmB;UAAnBA,wDAAA,SAAA8kC,GAAA,CAAA3b,aAAA,CAAmB;UA2BnBnpB,uDAAA,GAA6C;UAA7CA,wDAAA,UAAA8kC,GAAA,CAAA1b,eAAA,IAAA0b,GAAA,CAAA7xB,QAAA,CAAAtM,MAAA,KAA6C;UAoB7C3G,uDAAA,GAAW;UAAXA,wDAAA,SAAA8kC,GAAA,CAAAj7B,KAAA,CAAW;UAqBC7J,uDAAA,GAAuC;UAAvCA,wDAAA,SAAA8kC,GAAA,CAAA7xB,QAAA,IAAA6xB,GAAA,CAAA7xB,QAAA,CAAAtM,MAAA,KAAuC,aAAAmhC,IAAA;UA0bhD9nC,uDAAA,GAAc;UAAdA,wDAAA,SAAA8kC,GAAA,CAAAlc,QAAA,CAAc;UA0Bd5oB,uDAAA,GAAgB;UAAhBA,wDAAA,SAAA8kC,GAAA,CAAA5wB,UAAA,CAAgB;UAQhBlU,uDAAA,GAAqB;UAArBA,wDAAA,SAAA8kC,GAAA,CAAArb,eAAA,CAAqB;UAkCxBzpB,uDAAA,GAAuB;UAAvBA,wDAAA,SAAA8kC,GAAA,CAAA9vB,iBAAA,CAAuB;UAwBxBhV,uDAAA,GAAyB;UAAzBA,wDAAA,cAAA8kC,GAAA,CAAAxxB,WAAA,CAAyB;UAUrBtT,uDAAA,GAAuC;UAAvCA,wDAAA,YAAAA,6DAAA,KAAA+X,IAAA,EAAA+sB,GAAA,CAAArb,eAAA,EAAuC;UAsBxCzpB,uDAAA,GAAsB;UAAtBA,wDAAA,SAAA8kC,GAAA,CAAAjc,gBAAA,CAAsB;UAOtB7oB,uDAAA,GAAuB;UAAvBA,wDAAA,UAAA8kC,GAAA,CAAAjc,gBAAA,CAAuB;UAUvB7oB,uDAAA,GAAmE;UAAnEA,wDAAA,UAAA8kC,GAAA,CAAAjc,gBAAA,MAAAkf,QAAA,GAAAjD,GAAA,CAAAxxB,WAAA,CAAAC,GAAA,8BAAAw0B,QAAA,CAAAnpC,KAAA,SAAmE;UASnEoB,uDAAA,GAAmE;UAAnEA,wDAAA,UAAA8kC,GAAA,CAAAjc,gBAAA,MAAAmf,QAAA,GAAAlD,GAAA,CAAAxxB,WAAA,CAAAC,GAAA,8BAAAy0B,QAAA,CAAAppC,KAAA,SAAmE;UAWpEoB,uDAAA,GAAmC;UAAnCA,wDAAA,SAAA8kC,GAAA,CAAApb,aAAA,IAAAob,GAAA,CAAAhuB,YAAA,CAAmC;UAmCtC9W,uDAAA,GAAuC;UAAvCA,wDAAA,SAAA8kC,GAAA,CAAAnb,mBAAA,IAAAmb,GAAA,CAAA1tB,UAAA,CAAuC;UA2IvCpX,uDAAA,GAAsB;UAAtBA,wDAAA,SAAA8kC,GAAA,CAAA9a,gBAAA,CAAsB;UA8LrBhqB,uDAAA,GAA2B;UAA3BA,wDAAA,SAAA8kC,GAAA,CAAApd,qBAAA,CAA2B;UAuSb1nB,uDAAA,GAAoB;UAApBA,wDAAA,YAAA8kC,GAAA,CAAAxB,eAAA,GAAoB;UA2BlCtjC,uDAAA,GAA4B;UAA5BA,wDAAA,SAAA8kC,GAAA,CAAAhb,sBAAA,CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC51DoC;;;;;;;;;;;;IC2D5D9pB,4DAAA,cAA4D;IAExDA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IAEPA,uDAAA,cAEO;IACTA,0DAAA,EAAM;;;;IANFA,uDAAA,GACF;IADEA,gEAAA,MAAAioC,QAAA,MACF;;;;;IAsCNjoC,4DAAA,cAGC;IACCA,uDAAA,cAA6C;IAC7CA,4DAAA,YAAmC;IAAAA,oDAAA,sCAA+B;IAAAA,0DAAA,EAAI;;;;;;IAIxEA,4DAAA,cAAsD;IAElDA,uDAAA,YAA2C;IAC7CA,0DAAA,EAAM;IACNA,4DAAA,cAAoB;IAEhBA,oDAAA,+CACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAoC;IAClCA,oDAAA,GACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,iBAAsE;IAA9DA,wDAAA,mBAAAkoC,8DAAA;MAAAloC,2DAAA,CAAAmoC,GAAA;MAAA,MAAArgC,MAAA,GAAA9H,2DAAA;MAAA,OAASA,yDAAA,CAAA8H,MAAA,CAAAsgC,iBAAA,EAAmB;IAAA,EAAC;IACnCpoC,uDAAA,YAAsC;IAACA,oDAAA,wBACzC;IAAAA,0DAAA,EAAS;;;;IAJPA,uDAAA,GACF;IADEA,gEAAA,MAAAqoC,MAAA,CAAAx+B,KAAA,MACF;;;;;;IAQJ7J,4DAAA,cAGC;IAEGA,uDAAA,YAA+B;IACjCA,0DAAA,EAAM;IAENA,4DAAA,aAAmC;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAK;IAE3DA,4DAAA,YAAiC;IAC/BA,oDAAA,iEACF;IAAAA,0DAAA,EAAI;IAEJA,4DAAA,iBAGC;IAFCA,wDAAA,mBAAAsoC,8DAAA;MAAAtoC,2DAAA,CAAAuoC,IAAA;MAAA,MAAAC,MAAA,GAAAxoC,2DAAA;MAAA,OAASA,yDAAA,CAAAwoC,MAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCzoC,uDAAA,YAAuC;IACvCA,oDAAA,8BACF;IAAAA,0DAAA,EAAS;;;;;IAIXA,4DAAA,cAGC;IAEGA,uDAAA,YAA6B;IAC/BA,0DAAA,EAAM;IAENA,4DAAA,aAAmC;IAAAA,oDAAA,sCAAqB;IAAAA,0DAAA,EAAK;IAE7DA,4DAAA,YAAiC;IAAAA,oDAAA,0CAAmC;IAAAA,0DAAA,EAAI;;;;;IA8BlEA,uDAAA,cAMO;;;;;IAoBHA,4DAAA,eAGG;IAAAA,oDAAA,aACH;IAAAA,0DAAA,EAAO;;;;;IAGTA,4DAAA,cAGC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA0oC,QAAA,CAAAC,WAAA,MACF;;;;;;;;;;;IA5DR3oC,4DAAA,aAQC;IANCA,wDAAA,mBAAA4oC,8DAAA;MAAA,MAAAnnC,WAAA,GAAAzB,2DAAA,CAAA6oC,IAAA;MAAA,MAAAH,QAAA,GAAAjnC,WAAA,CAAAG,SAAA;MAAA,MAAAgS,OAAA,GAAA5T,2DAAA;MAAA,OAASA,yDAAA,CAAA4T,OAAA,CAAAk1B,gBAAA,CAAAJ,QAAA,CAAAjqC,EAAA,CAAyB;IAAA,EAAC;IAOnCuB,4DAAA,cAA+B;IAG3BA,uDAAA,cAOE;IAGFA,wDAAA,IAAA+oC,+CAAA,kBAMO;IACT/oC,0DAAA,EAAM;IAGNA,4DAAA,cAA6C;IAGvCA,oDAAA,GAKF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAA2C;IACzCA,oDAAA,IACF;;IAAAA,0DAAA,EAAO;IAGTA,4DAAA,eAA6C;IAEzCA,wDAAA,KAAAgpC,iDAAA,mBAIO;IACPhpC,oDAAA,IACF;IAAAA,0DAAA,EAAI;IACJA,wDAAA,KAAAipC,gDAAA,kBAKM;IACRjpC,0DAAA,EAAM;;;;;;;;IAzDVA,wDAAA,YAAAA,6DAAA,KAAAkpC,GAAA,EAAAC,OAAA,CAAAC,sBAAA,KAAAV,QAAA,CAAAjqC,EAAA,EAGE;IAMIuB,uDAAA,GAIC;IAJDA,wDAAA,SAAA0oC,QAAA,CAAA/tB,YAAA,IAAA0uB,OAAA,GAAAF,OAAA,CAAAG,mBAAA,CAAAZ,QAAA,CAAA/tB,YAAA,oBAAA0uB,OAAA,CAAA3gC,KAAA,gDAAA1I,2DAAA,CAIC;IAMAA,uDAAA,GAIjB;IAJiBA,wDAAA,SAAA0oC,QAAA,CAAA/tB,YAAA,MAAA4uB,OAAA,GAAAJ,OAAA,CAAAG,mBAAA,CAAAZ,QAAA,CAAA/tB,YAAA,oBAAA4uB,OAAA,CAAA7oC,QAAA,EAIjB;IAQkBV,uDAAA,GAKF;IALEA,gEAAA,OAAA0oC,QAAA,CAAA/tB,YAAA,IAAA6uB,OAAA,GAAAL,OAAA,CAAAG,mBAAA,CAAAZ,QAAA,CAAA/tB,YAAA,oBAAA6uB,OAAA,CAAA/oC,QAAA,uCAKF;IAEET,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,QAAA0oC,QAAA,CAAAgB,WAAA,kBAAAhB,QAAA,CAAAgB,WAAA,CAAAvjC,SAAA,0BACF;IAMKnG,uDAAA,GAAoD;IAApDA,wDAAA,UAAA0oC,QAAA,CAAAgB,WAAA,kBAAAhB,QAAA,CAAAgB,WAAA,CAAAjhC,MAAA,kBAAAigC,QAAA,CAAAgB,WAAA,CAAAjhC,MAAA,CAAAhK,EAAA,MAAA0qC,OAAA,CAAA/3B,aAAA,CAAoD;IAIvDpR,uDAAA,GACF;IADEA,gEAAA,OAAA0oC,QAAA,CAAAgB,WAAA,kBAAAhB,QAAA,CAAAgB,WAAA,CAAArjC,OAAA,mCACF;IAEGrG,uDAAA,GAA8C;IAA9CA,wDAAA,SAAA0oC,QAAA,CAAAC,WAAA,IAAAD,QAAA,CAAAC,WAAA,KAA8C;;;;;IA5D3D3oC,4DAAA,aAGC;IACCA,wDAAA,IAAA2pC,yCAAA,mBAgEK;IACP3pC,0DAAA,EAAK;;;;IAhEgBA,uDAAA,GAAwB;IAAxBA,wDAAA,YAAAyE,MAAA,CAAAmlC,qBAAA,CAAwB;;;AD5J7C,MAAOC,qBAAqB;EAchC7rC,YACUopB,cAA8B,EAC9Be,WAA4B,EAC5BG,MAAc,EACdJ,KAAqB,EACrBK,YAA0B,EAC1BC,MAAqB,EACrBshB,YAA0B;IAN1B,KAAA1iB,cAAc,GAAdA,cAAc;IACd,KAAAe,WAAW,GAAXA,WAAW;IACX,KAAAG,MAAM,GAANA,MAAM;IACN,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAK,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAshB,YAAY,GAAZA,YAAY;IApBtB,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAH,qBAAqB,GAAmB,EAAE;IAC1C,KAAAj2B,OAAO,GAAG,IAAI;IAEd,KAAAvC,aAAa,GAAkB,IAAI;IACnC,KAAA9K,WAAW,GAAG,EAAE;IAChB,KAAA8iC,sBAAsB,GAAkB,IAAI;IAGpC,KAAAT,WAAW,GAAG,IAAI7qC,iDAAe,CAAS,CAAC,CAAC;IAC7C,KAAAksC,YAAY,GAAG,IAAI,CAACrB,WAAW,CAACxqC,YAAY,EAAE;IAC7C,KAAAkrB,aAAa,GAAmB,EAAE;IAWxC,IAAI,CAAC4gB,WAAW,GAAG,IAAI,CAACH,YAAY,CAACI,SAAS;EAChD;EAEA3V,QAAQA,CAAA;IACN,IAAI,CAACnjB,aAAa,GAAG,IAAI,CAAC+W,WAAW,CAACqM,gBAAgB,EAAE;IACxD,IAAI,CAAC,IAAI,CAACpjB,aAAa,EAAE;MACvB,IAAI,CAACokB,WAAW,CAAC,wBAAwB,CAAC;MAC1C;;IAGF,IAAI,CAAC4S,iBAAiB,EAAE;IACxB,IAAI,CAACxT,qBAAqB,EAAE;IAC5B,IAAI,CAAC8B,8BAA8B,EAAE;IAErC;IACA,IAAI,CAACxO,KAAK,CAACiiB,UAAU,EAAE/U,MAAM,CAACrI,SAAS,CAAEqI,MAAM,IAAI;MACjD,IAAI,CAACgU,sBAAsB,GAAGhU,MAAM,CAAC,gBAAgB,CAAC,IAAI,IAAI;IAChE,CAAC,CAAC;EACJ;EAEAgT,iBAAiBA,CAAA;IACf,IAAI,CAACz0B,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC9J,KAAK,GAAG,IAAI;IAEjB,MAAMgtB,GAAG,GAAG,IAAI,CAACzP,cAAc,CAACgjB,gBAAgB,EAAE,CAACrd,SAAS,CAAC;MAC3DluB,IAAI,EAAGkrC,aAAa,IAAI;QACtB,IAAI,CAACA,aAAa,GAAGva,KAAK,CAAC6a,OAAO,CAACN,aAAa,CAAC,GAC7C,CAAC,GAAGA,aAAa,CAAC,GAClB,EAAE;QAEN,IAAI,CAACO,mBAAmB,EAAE;QAC1B,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAAC72B,OAAO,GAAG,KAAK;MACtB,CAAC;MACD9J,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAAC8J,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC4U,YAAY,CAACtpB,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;IACF,IAAI,CAACoqB,aAAa,CAACgJ,IAAI,CAACwE,GAAG,CAAC;EAC9B;EAEAyT,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAChkC,WAAW,EAAE;MACrB,IAAI,CAACsjC,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAACG,aAAa,CAAC;MACpD;;IAGF,MAAMlW,KAAK,GAAG,IAAI,CAACvtB,WAAW,CAAC+sB,WAAW,EAAE;IAC5C,IAAI,CAACuW,qBAAqB,GAAG,IAAI,CAACG,aAAa,CAAC3qC,MAAM,CAAEqrC,IAAI,IAAI;MAC9D,MAAMjqC,gBAAgB,GAAGiqC,IAAI,CAAC9vB,YAAY,GACtC,IAAI,CAAC2uB,mBAAmB,CAACmB,IAAI,CAAC9vB,YAAY,CAAC,GAC3CwgB,SAAS;MACb,OACE36B,gBAAgB,EAAEC,QAAQ,CAAC4yB,WAAW,EAAE,CAACd,QAAQ,CAACsB,KAAK,CAAC,IACxD4W,IAAI,CAACf,WAAW,EAAErjC,OAAO,EAAEgtB,WAAW,EAAE,CAACd,QAAQ,CAACsB,KAAK,CAAC,IACxD,KAAK;IAET,CAAC,CAAC;EACJ;EAEQ0W,iBAAiBA,CAAA;IACvB,MAAMlpC,KAAK,GAAG,IAAI,CAAC0oC,aAAa,CAACW,MAAM,CACrC,CAACC,GAAG,EAAEF,IAAI,KAAKE,GAAG,IAAIF,IAAI,CAAC9B,WAAW,IAAI,CAAC,CAAC,EAC5C,CAAC,CACF;IACD,IAAI,CAACA,WAAW,CAAC9pC,IAAI,CAACwC,KAAK,CAAC;EAC9B;EAEAmpC,iBAAiBA,CAAA;IACf,IAAI,CAACT,aAAa,CAAC7T,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/B,MAAMwU,KAAK,GAAG,IAAI,CAACC,mBAAmB,CAAC1U,CAAC,CAAC;MACzC,MAAM2U,KAAK,GAAG,IAAI,CAACD,mBAAmB,CAACzU,CAAC,CAAC;MACzC,OAAO0U,KAAK,CAACxU,OAAO,EAAE,GAAGsU,KAAK,CAACtU,OAAO,EAAE;IAC1C,CAAC,CAAC;IACF,IAAI,CAACgU,mBAAmB,EAAE;EAC5B;EAEQO,mBAAmBA,CAACJ,IAAkB;IAC5C;IACA,MAAMM,WAAW,GAAG,IAAI3gB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjC,IAAIqgB,IAAI,CAACf,WAAW,EAAEvjC,SAAS,EAAE;MAC/B,OAAO,OAAOskC,IAAI,CAACf,WAAW,CAACvjC,SAAS,KAAK,QAAQ,GACjD,IAAIikB,IAAI,CAACqgB,IAAI,CAACf,WAAW,CAACvjC,SAAS,CAAC,GACpCskC,IAAI,CAACf,WAAW,CAACvjC,SAAS;;IAGhC,IAAIskC,IAAI,CAACO,SAAS,EAAE;MAClB,OAAO,OAAOP,IAAI,CAACO,SAAS,KAAK,QAAQ,GACrC,IAAI5gB,IAAI,CAACqgB,IAAI,CAACO,SAAS,CAAC,GACxBP,IAAI,CAACO,SAAS;;IAGpB,IAAIP,IAAI,CAACtR,SAAS,EAAE;MAClB,OAAO,OAAOsR,IAAI,CAACtR,SAAS,KAAK,QAAQ,GACrC,IAAI/O,IAAI,CAACqgB,IAAI,CAACtR,SAAS,CAAC,GACxBsR,IAAI,CAACtR,SAAS;;IAGpB,OAAO4R,WAAW;EACpB;EAEAzB,mBAAmBA,CAAC3uB,YAAgC;IAClD,IAAI,CAACA,YAAY,IAAI,CAAC6U,KAAK,CAAC6a,OAAO,CAAC1vB,YAAY,CAAC,EAAE;MACjD,OAAOwgB,SAAS;;IAElB,OAAOxgB,YAAY,CAACob,IAAI,CACrBC,CAAC,IAAKA,CAAC,CAAC3kB,GAAG,KAAK,IAAI,CAACD,aAAa,IAAI4kB,CAAC,CAACv3B,EAAE,KAAK,IAAI,CAAC2S,aAAa,CACnE;EACH;EAEAwjB,qBAAqBA,CAAA;IACnB,MAAMiC,GAAG,GAAG,IAAI,CAACzP,cAAc,CAACwN,qBAAqB,EAAE,CACpDS,IAAI,CAACxD,yCAAG,CAAElC,IAAI,IAAK,IAAI,CAACvI,cAAc,CAAC6jB,aAAa,CAACtb,IAAI,CAAC,CAAC,CAAC,CAC5D5C,SAAS,CAAC;MACTluB,IAAI,EAAG8wB,IAAU,IAAI;QACnB,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC9sB,gBAAgB,CAAC8sB,IAAI,CAAC;;MAE/B,CAAC;MACD9lB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;IACJ,IAAI,CAACoqB,aAAa,CAACgJ,IAAI,CAACwE,GAAG,CAAC;EAC9B;EAEAH,8BAA8BA,CAAA;IAC5B,MAAMG,GAAG,GAAG,IAAI,CAACzP,cAAc,CAACsP,8BAA8B,CAC5D,QAAQ,CACT,CAAC3J,SAAS,CAAC;MACVluB,IAAI,EAAGqsC,WAAW,IAAI;QACpB,MAAM7e,KAAK,GAAG,IAAI,CAAC0d,aAAa,CAACoB,SAAS,CACvCrZ,CAAC,IAAKA,CAAC,CAACrzB,EAAE,KAAKysC,WAAW,CAACzsC,EAAE,CAC/B;QACD,IAAI4tB,KAAK,IAAI,CAAC,EAAE;UACd,IAAI,CAAC0d,aAAa,CAAC1d,KAAK,CAAC,GAAG6e,WAAW;SACxC,MAAM;UACL,IAAI,CAACnB,aAAa,CAACnK,OAAO,CAACsL,WAAW,CAAC;;QAEzC,IAAI,CAACV,iBAAiB,EAAE;MAC1B,CAAC;MACD3gC,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;IACF,IAAI,CAACwf,aAAa,CAACgJ,IAAI,CAACwE,GAAG,CAAC;EAC9B;EAEAh0B,gBAAgBA,CAACuoC,WAAiB;IAChC,IAAI,CAACrB,aAAa,GAAG,IAAI,CAACA,aAAa,CAAClY,GAAG,CAAE4Y,IAAI,IAAI;MACnD,IAAI,CAACA,IAAI,CAAC9vB,YAAY,EAAE;QACtB,OAAO8vB,IAAI;;MAEb,MAAM9vB,YAAY,GAAG8vB,IAAI,CAAC9vB,YAAY,CAACkX,GAAG,CAAEmE,CAAC,IAAI;QAC/C,MAAMqV,aAAa,GACjBrV,CAAC,CAAC3kB,GAAG,KAAK+5B,WAAW,CAAC/5B,GAAG,IAAI2kB,CAAC,CAACv3B,EAAE,KAAK2sC,WAAW,CAAC/5B,GAAG;QACvD,OAAOg6B,aAAa,GAChB;UACE,GAAGrV,CAAC;UACJt1B,QAAQ,EAAE0qC,WAAW,CAAC1qC,QAAQ;UAC9BE,UAAU,EAAEwqC,WAAW,CAACxqC;SACzB,GACDo1B,CAAC;MACP,CAAC,CAAC;MACF,OAAO;QAAE,GAAGyU,IAAI;QAAE9vB;MAAY,CAAE;IAClC,CAAC,CAAC;IACF,IAAI,CAAC2vB,mBAAmB,EAAE;EAC5B;EAEAxB,gBAAgBA,CAAC5W,cAAkC;IACjD,IAAI,CAACA,cAAc,EAAE;MACnB;;IAGF,IAAI,CAACkX,sBAAsB,GAAGlX,cAAc;IAC5C,IAAI,CAAC5J,MAAM,CAAC8W,QAAQ,CAAC,CAAC,MAAM,EAAElN,cAAc,CAAC,EAAE;MAAEoZ,UAAU,EAAE,IAAI,CAACpjB;IAAK,CAAE,CAAC;EAC5E;EAEAugB,oBAAoBA,CAAA;IAClB,IAAI,CAACngB,MAAM,CAAC8W,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAz+B,gBAAgBA,CAACC,UAAkB;IACjC,OAAO,IAAI,CAACwmB,cAAc,CAACzmB,gBAAgB,CAACC,UAAU,CAAC;EACzD;EAEQ40B,WAAWA,CAACl3B,OAAe,EAAEuL,KAAW;IAC9C,IAAI,CAAC2e,MAAM,CAAC3e,KAAK,CAAC,uBAAuB,EAAEvL,OAAO,EAAEuL,KAAK,CAAC;IAC1D,IAAI,CAACA,KAAK,GAAGvL,OAAO;IACpB,IAAI,CAACqV,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC4U,YAAY,CAACtpB,SAAS,CAACX,OAAO,CAAC;EACtC;EAEAolC,WAAWA,CAAA;IACT,IAAI,CAACra,aAAa,CAACmO,OAAO,CAAEX,GAAG,IAAKA,GAAG,CAACkK,WAAW,EAAE,CAAC;EACxD;;;uBA9NW8I,qBAAqB,EAAA7pC,+DAAA,CAAA4jC,yEAAA,GAAA5jC,+DAAA,CAAA6jC,8EAAA,GAAA7jC,+DAAA,CAAA+jC,mDAAA,GAAA/jC,+DAAA,CAAA+jC,2DAAA,GAAA/jC,+DAAA,CAAAikC,wEAAA,GAAAjkC,+DAAA,CAAAmkC,0EAAA,GAAAnkC,+DAAA,CAAAskC,qEAAA;IAAA;EAAA;;;YAArBuF,qBAAqB;MAAAnF,SAAA;MAAA8G,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAA/G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCflC7kC,4DAAA,aAGC;;UAECA,4DAAA,aAAkE;UAChEA,uDAAA,aAEO;UAMPA,4DAAA,aAA4D;UAExDA,uDAAA,aAAmE;UAWrEA,0DAAA,EAAM;UAKVA,4DAAA,cAEC;UAIGA,uDAAA,cAEO;UAKPA,4DAAA,eAAoD;UACHA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAK;UAC5DA,4DAAA,eAAyC;UAErCA,wDAAA,mBAAA6rC,wDAAA;YAAA,OAAS/G,GAAA,CAAA2D,oBAAA,EAAsB;UAAA,EAAC;UAIhCzoC,uDAAA,eAEO;UAITA,0DAAA,EAAS;UACTA,wDAAA,KAAA8rC,qCAAA,kBAQM;;UACR9rC,0DAAA,EAAM;UAIRA,4DAAA,eAA4B;UAExBA,wDAAA,2BAAA+rC,+DAAAjlC,MAAA;YAAA,OAAAg+B,GAAA,CAAAx+B,WAAA,GAAAQ,MAAA;UAAA,EAAyB,2BAAAilC,+DAAA;YAAA,OACRjH,GAAA,CAAAwF,mBAAA,EAAqB;UAAA,EADb;UAD3BtqC,0DAAA,EAME;UACFA,4DAAA,eAEC;UACCA,uDAAA,aAEK;UACPA,0DAAA,EAAM;UACNA,4DAAA,eAEC;UACCA,uDAAA,eAEO;UACTA,0DAAA,EAAM;UAKVA,4DAAA,eAAkE;UAEhEA,wDAAA,KAAAgsC,qCAAA,kBAMM;UAGNhsC,wDAAA,KAAAisC,qCAAA,mBAeM;UAGNjsC,wDAAA,KAAAksC,qCAAA,mBAqBM;UAGNlsC,wDAAA,KAAAmsC,qCAAA,kBAWM;UAGNnsC,wDAAA,KAAAosC,oCAAA,iBAqEK;UACPpsC,0DAAA,EAAM;UAIRA,4DAAA,eAAiE;UAE/DA,uDAAA,qBAA+B;UACjCA,0DAAA,EAAM;;;UAjPNA,yDAAA,SAAAA,yDAAA,OAAA8kC,GAAA,CAAAmF,WAAA,EAAkC;UA0DpBjqC,uDAAA,IAA2B;UAA3BA,wDAAA,SAAAA,yDAAA,SAAA8kC,GAAA,CAAAkF,YAAA,EAA2B;UAejChqC,uDAAA,GAAyB;UAAzBA,wDAAA,YAAA8kC,GAAA,CAAAx+B,WAAA,CAAyB;UA2B1BtG,uDAAA,GAAa;UAAbA,wDAAA,SAAA8kC,GAAA,CAAAnxB,OAAA,CAAa;UAQV3T,uDAAA,GAAW;UAAXA,wDAAA,SAAA8kC,GAAA,CAAAj7B,KAAA,CAAW;UAmBd7J,uDAAA,GAAoE;UAApEA,wDAAA,UAAA8kC,GAAA,CAAAnxB,OAAA,IAAAmxB,GAAA,CAAA8E,qBAAA,CAAAjjC,MAAA,WAAAm+B,GAAA,CAAAx+B,WAAA,CAAoE;UAwBpEtG,uDAAA,GAAmE;UAAnEA,wDAAA,UAAA8kC,GAAA,CAAAnxB,OAAA,IAAAmxB,GAAA,CAAA8E,qBAAA,CAAAjjC,MAAA,UAAAm+B,GAAA,CAAAx+B,WAAA,CAAmE;UAcnEtG,uDAAA,GAAkD;UAAlDA,wDAAA,UAAA8kC,GAAA,CAAAnxB,OAAA,IAAAmxB,GAAA,CAAA8E,qBAAA,CAAAjjC,MAAA,KAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtKJ;AACsB;AACG;AACZ;AACe;;;AAEnF,MAAM+lC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,4FAAsB;EACjCI,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,eAAe;IAAEC,SAAS,EAAE;EAAM,CAAE,EAC5D;IACEJ,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAE/C,yFAAqB;IAChCmD,IAAI,EAAE;MAAE1qC,KAAK,EAAE;IAAe;GAC/B,EACD;IACEqqC,IAAI,EAAE,wBAAwB;IAC9BC,SAAS,EAAEzlB,sFAAoB;IAC/B6lB,IAAI,EAAE;MAAE1qC,KAAK,EAAE;IAAM;GACtB,EACD;IACEqqC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEzlB,sFAAoB;IAC/B6lB,IAAI,EAAE;MAAE1qC,KAAK,EAAE;IAAM;GACtB,EACD;IACEqqC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEJ,6EAAiB;IAC5BQ,IAAI,EAAE;MAAE1qC,KAAK,EAAE;IAAc;GAC9B,EAED;IACEqqC,IAAI,EAAE,KAAK;IACXG,UAAU,EAAE,OAAO;IACnBC,SAAS,EAAE;GACZ;CAEJ,CACF;AAMK,MAAOE,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBV,yDAAY,CAACW,QAAQ,CAACR,MAAM,CAAC,EAC7BH,yDAAY;IAAA;EAAA;;;sHAEXU,qBAAqB;IAAAE,OAAA,GAAAvJ,yDAAA;IAAAwJ,OAAA,GAFtBb,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;ACtClB,MAAOc,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAA3I,SAAA;MAAA8G,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAA2B,kCAAAzI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPrC7kC,4DAAA,QAAG;UAAAA,oDAAA,8BAAuB;UAAAA,0DAAA,EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCiB;AACA;AAEmB;AACA;AACpB;AAC+B;AACG;AACZ;AACe;AACM;AAChB;AACP;AACyB;;AAqBrF,MAAO4tC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACxJ,mFAAiB,EAAEhd,4EAAc,CAAC;MAAA+lB,OAAA,GAR5CI,0DAAY,EACZN,2EAAqB,EACrBO,wDAAW,EACXC,gEAAmB,EACnBC,yDAAY,EACZnB,0DAAY,EACZoB,qGAAkB;IAAA;EAAA;;;sHAITC,cAAc;IAAAC,YAAA,GAjBvB1mB,sFAAoB,EACpB0iB,yFAAqB,EACrB2C,6EAAiB,EACjBC,4FAAsB,EACtBY,kGAAwB;IAAAF,OAAA,GAGxBI,0DAAY,EACZN,2EAAqB,EACrBO,wDAAW,EACXC,gEAAmB,EACnBC,yDAAY,EACZnB,0DAAY,EACZoB,qGAAkB;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AC7BoC;AAMF;AAEA;;;;;;;;;;;;IC0IlD3tC,4DAAA,cAGC;IAEIA,oDAAA,GAC4B;IAAAA,0DAAA,EAC9B;IACDA,4DAAA,WAAM;IAAAA,oDAAA,GAA2C;IAAAA,0DAAA,EAAO;;;;IAHrDA,uDAAA,GAC4B;IAD5BA,gEAAA,kBAAAkuC,MAAA,CAAA/K,KAAA,CAAAx8B,MAAA,WAAAunC,MAAA,CAAAC,UAAA,kBAC4B;IAEzBnuC,uDAAA,GAA2C;IAA3CA,gEAAA,UAAAkuC,MAAA,CAAAhlB,WAAA,WAAAglB,MAAA,CAAAE,UAAA,KAA2C;;;;;IAcrDpuC,4DAAA,cAA2E;IACzEA,uDAAA,cAA6C;IAC7CA,4DAAA,cAAqC;IAAAA,oDAAA,qCAA8B;IAAAA,0DAAA,EAAM;;;;;IAI3EA,4DAAA,cAA2E;IAEvEA,uDAAA,YAA4B;IAC9BA,0DAAA,EAAM;IACNA,4DAAA,aAAmC;IAAAA,oDAAA,oCAAwB;IAAAA,0DAAA,EAAK;IAChEA,4DAAA,YAAiC;IAC/BA,oDAAA,mEACF;IAAAA,0DAAA,EAAI;;;;;IAeEA,uDAAA,eAGQ;;;;;;IAYVA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAAquC,wEAAA;MAAAruC,2DAAA,CAAAsuC,IAAA;MAAA,MAAAC,OAAA,GAAAvuC,2DAAA,GAAA4B,SAAA;MAAA,MAAAunC,OAAA,GAAAnpC,2DAAA;MAAA,OAASA,yDAAA,CAAAmpC,OAAA,CAAAqF,cAAA,CAAAD,OAAA,CAAA9vC,EAAA,IAAA8vC,OAAA,CAAAl9B,GAAA,CAAmC;IAAA,EAAC;IAI7CrR,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;;IACTA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAAyuC,wEAAA;MAAAzuC,2DAAA,CAAA0uC,IAAA;MAAA,MAAAH,OAAA,GAAAvuC,2DAAA,GAAA4B,SAAA;MAAA,MAAAoR,OAAA,GAAAhT,2DAAA;MAAA,OAASA,yDAAA,CAAAgT,OAAA,CAAA27B,cAAA,CAAAJ,OAAA,CAAA9vC,EAAA,IAAA8vC,OAAA,CAAAl9B,GAAA,CAAmC;IAAA,EAAC;IAI7CrR,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;;IAxCbA,4DAAA,aAA4D;IAGxDA,wDAAA,mBAAA4uC,2DAAA;MAAA,MAAAntC,WAAA,GAAAzB,2DAAA,CAAA6oC,IAAA;MAAA,MAAA0F,OAAA,GAAA9sC,WAAA,CAAAG,SAAA;MAAA,MAAAgS,OAAA,GAAA5T,2DAAA;MAAA,OAASA,yDAAA,CAAA4T,OAAA,CAAAi7B,iBAAA,CAAAN,OAAA,CAAA9vC,EAAA,IAAA8vC,OAAA,CAAAl9B,GAAA,CAAsC;IAAA,EAAC;IAEhDrR,4DAAA,cAA+B;IAC7BA,uDAAA,cAGE;IACFA,wDAAA,IAAA8uC,4CAAA,mBAGQ;IACV9uC,0DAAA,EAAM;IACNA,4DAAA,cAAkC;IAE9BA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAiC;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAI;IAKzDA,4DAAA,eAAqC;IACnCA,wDAAA,KAAA+uC,+CAAA,qBAOS;IACT/uC,wDAAA,KAAAgvC,+CAAA,qBAOS;IACXhvC,0DAAA,EAAM;;;;IAlCAA,uDAAA,GAAwD;IAAxDA,wDAAA,QAAAuuC,OAAA,CAAA7lC,KAAA,wCAAA1I,2DAAA,CAAwD;IAIvDA,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAuuC,OAAA,CAAA7tC,QAAA,CAAmB;IAMpBV,uDAAA,GACF;IADEA,gEAAA,MAAAuuC,OAAA,CAAA9tC,QAAA,MACF;IACiCT,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAuuC,OAAA,CAAAU,KAAA,CAAgB;IAOhDjvC,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAuuC,OAAA,CAAA7tC,QAAA,CAAmB;IAQnBV,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAuuC,OAAA,CAAA7tC,QAAA,CAAmB;;;;;IAnC5BV,4DAAA,aAA2D;IACzDA,wDAAA,IAAAkvC,qCAAA,kBA0CK;IACPlvC,0DAAA,EAAK;;;;IA3CkBA,uDAAA,GAAQ;IAARA,wDAAA,YAAAmvC,MAAA,CAAAhM,KAAA,CAAQ;;;;;IA8C/BnjC,4DAAA,cAAyE;IAErEA,uDAAA,cAAsE;IAGxEA,0DAAA,EAAM;IACNA,4DAAA,cAAqC;IACnCA,oDAAA,6CACF;IAAAA,0DAAA,EAAM;;;;;;IAIRA,4DAAA,cAA4E;IAClEA,wDAAA,mBAAAovC,0DAAA;MAAApvC,2DAAA,CAAAqvC,IAAA;MAAA,MAAA56B,OAAA,GAAAzU,2DAAA;MAAA,OAASA,yDAAA,CAAAyU,OAAA,CAAA66B,YAAA,EAAc;IAAA,EAAC;IAC9BtvC,uDAAA,YAAwC;IACxCA,oDAAA,oCACF;IAAAA,0DAAA,EAAS;;;ADxOT,MAAOwsC,iBAAiB;EA8B5BxuC,YACUopB,cAA8B,EAC/BkB,MAAc,EACdJ,KAAqB,EACpBC,WAA4B,EAC5BI,YAA0B,EAC1BC,MAAqB,EACrBshB,YAA0B;IAN1B,KAAA1iB,cAAc,GAAdA,cAAc;IACf,KAAAkB,MAAM,GAANA,MAAM;IACN,KAAAJ,KAAK,GAALA,KAAK;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAI,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAshB,YAAY,GAAZA,YAAY;IApCtB,KAAA3G,KAAK,GAAW,EAAE;IAClB,KAAAxvB,OAAO,GAAG,IAAI;IACd,KAAAvC,aAAa,GAAkB,IAAI;IAGnC;IACA,KAAA8X,WAAW,GAAG,CAAC;IACf,KAAAqmB,QAAQ,GAAG,EAAE;IACb,KAAApB,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAoB,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,MAAM,GAAG,UAAU;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAG,IAAI5B,qDAAS,CAAC;MACzB1nC,WAAW,EAAE,IAAIynC,uDAAW,CAAC,EAAE,CAAC;MAChCrtC,QAAQ,EAAE,IAAIqtC,uDAAW,CAAiB,IAAI;KAC/C,CAAC;IAEF;IACA,KAAA8B,kBAAkB,GAAG,IAAI;IACzB,KAAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC;IAGrB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAA1mB,aAAa,GAAiB,IAAI1pB,8CAAY,EAAE;IAWtD,IAAI,CAACsqC,WAAW,GAAG,IAAI,CAACH,YAAY,CAACI,SAAS;EAChD;EAEA3V,QAAQA,CAAA;IACN,IAAI,CAACnjB,aAAa,GAAG,IAAI,CAAC+W,WAAW,CAACqM,gBAAgB,EAAE;IACxD,IAAI,CAACwb,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEQF,oBAAoBA,CAAA;IAC1B;IACA,MAAMG,SAAS,GAAG,IAAI,CAACP,UAAU,CAC9Br8B,GAAG,CAAC,aAAa,CAAE,CACnB68B,YAAY,CAACrjB,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACsjB,eAAe,EAAE;MACtB,IAAI,CAACH,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAAC7mB,aAAa,CAAC1J,GAAG,CAACwwB,SAAS,CAAC;IAEjC;IACA,MAAMG,SAAS,GAAG,IAAI,CAACV,UAAU,CAC9Br8B,GAAG,CAAC,UAAU,CAAE,CAChB68B,YAAY,CAACrjB,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACsjB,eAAe,EAAE;MACtB,IAAI,CAACH,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAAC7mB,aAAa,CAAC1J,GAAG,CAAC2wB,SAAS,CAAC;EACnC;EAEQL,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACJ,kBAAkB,EAAE;MAC3B,IAAI,CAACU,uBAAuB,GAAGzC,8CAAQ,CACrC,IAAI,CAACgC,mBAAmB,CACzB,CAAC/iB,SAAS,CAAC,MAAK;QACf,IAAI,CAAC,IAAI,CAACpZ,OAAO,IAAI,CAAC,IAAI,CAACi8B,UAAU,CAACr8B,GAAG,CAAC,aAAa,CAAC,EAAE3U,KAAK,EAAE;UAC/D,IAAI,CAACsxC,SAAS,CAAC,IAAI,CAAC;;MAExB,CAAC,CAAC;;EAEN;EAEAM,iBAAiBA,CAAA;IACf,IAAI,CAACX,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAElD,IAAI,IAAI,CAACA,kBAAkB,EAAE;MAC3B,IAAI,CAACI,gBAAgB,EAAE;KACxB,MAAM,IAAI,IAAI,CAACM,uBAAuB,EAAE;MACvC,IAAI,CAACA,uBAAuB,CAACxP,WAAW,EAAE;MAC1C,IAAI,CAACwP,uBAAuB,GAAGpV,SAAS;;EAE5C;EAEAkV,eAAeA,CAAA;IACb,IAAI,CAACnnB,WAAW,GAAG,CAAC;EACtB;EAEA;EACA,IAAI5iB,WAAWA,CAAA;IACb,OAAO,IAAI,CAACspC,UAAU,CAACr8B,GAAG,CAAC,aAAa,CAAC,EAAE3U,KAAK,IAAI,EAAE;EACxD;EAEA;EACA,IAAI0H,WAAWA,CAAC1H,KAAa;IAC3B,IAAI,CAACgxC,UAAU,CAACr8B,GAAG,CAAC,aAAa,CAAC,EAAEC,QAAQ,CAAC5U,KAAK,CAAC;EACrD;EAEA;EACA6xC,IAAIA,CAACC,IAAS;IACZ,OAAOA,IAAI;EACb;EAEAR,SAASA,CAACS,YAAY,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACZ,WAAW,EAAE;IAEtB,IAAI,CAACp8B,OAAO,GAAG,IAAI;IAEnB,MAAMrN,WAAW,GAAG,IAAI,CAACspC,UAAU,CAACr8B,GAAG,CAAC,aAAa,CAAC,EAAE3U,KAAK,IAAI,EAAE;IACnE,MAAM8B,QAAQ,GAAG,IAAI,CAACkvC,UAAU,CAACr8B,GAAG,CAAC,UAAU,CAAC,EAAE3U,KAAK;IAEvD,MAAMi4B,GAAG,GAAG,IAAI,CAACzP,cAAc,CAAC8b,WAAW,CACzCyN,YAAY,EACZrqC,WAAW,EACX,IAAI,CAAC4iB,WAAW,EAChB,IAAI,CAACqmB,QAAQ,EACb,IAAI,CAACG,MAAM,EACX,IAAI,CAACC,SAAS,EACdjvC,QAAQ,KAAK,IAAI,CAClB,CAACqsB,SAAS,CAAC;MACVluB,IAAI,EAAGskC,KAAK,IAAI;QACd,IAAI,CAAC3T,KAAK,CAAC6a,OAAO,CAAClH,KAAK,CAAC,EAAE;UACzB,IAAI,CAACA,KAAK,GAAG,EAAE;UACf,IAAI,CAACxvB,OAAO,GAAG,KAAK;UACpB,IAAI,CAACo8B,WAAW,GAAG,KAAK;UACxB,IAAI,CAACxnB,YAAY,CAACtpB,SAAS,CAAC,oCAAoC,CAAC;UACjE;;QAGF;QACA,IAAI,IAAI,CAACiqB,WAAW,KAAK,CAAC,EAAE;UAC1B;UACA,IAAI,CAACia,KAAK,GAAGA,KAAK,CAAC/jC,MAAM,CAAEuwB,IAAI,IAAI;YACjC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;YACvB,MAAMuH,MAAM,GAAGvH,IAAI,CAAClxB,EAAE,IAAIkxB,IAAI,CAACte,GAAG;YAClC,OAAO6lB,MAAM,KAAK,IAAI,CAAC9lB,aAAa;UACtC,CAAC,CAAC;SACH,MAAM;UACL;UACA,MAAMw/B,QAAQ,GAAGzN,KAAK,CAAC/jC,MAAM,CAAEyxC,OAAO,IAAI;YACxC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;YAC1B,MAAM3Z,MAAM,GAAG2Z,OAAO,CAACpyC,EAAE,IAAIoyC,OAAO,CAACx/B,GAAG;YACxC,OACE6lB,MAAM,KAAK,IAAI,CAAC9lB,aAAa,IAC7B,CAAC,IAAI,CAAC+xB,KAAK,CAACnE,IAAI,CACb8R,YAAY,IACX,CAACA,YAAY,CAACryC,EAAE,IAAIqyC,YAAY,CAACz/B,GAAG,MAAM6lB,MAAM,CACnD;UAEL,CAAC,CAAC;UAEF,IAAI,CAACiM,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,EAAE,GAAGyN,QAAQ,CAAC;;QAG3C;QACA,MAAMG,UAAU,GAAG,IAAI,CAAC3pB,cAAc,CAAC4pB,qBAAqB;QAC5D,IAAI,CAAC7C,UAAU,GAAG4C,UAAU,CAACE,UAAU;QACvC,IAAI,CAAC7C,UAAU,GAAG2C,UAAU,CAAC3C,UAAU;QACvC,IAAI,CAACoB,WAAW,GAAGuB,UAAU,CAACvB,WAAW;QACzC,IAAI,CAACC,eAAe,GAAGsB,UAAU,CAACtB,eAAe;QAEjD,IAAI,CAAC97B,OAAO,GAAG,KAAK;QACpB,IAAI,CAACo8B,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDlmC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC8J,OAAO,GAAG,KAAK;QACpB,IAAI,CAACo8B,WAAW,GAAG,KAAK;QACxB,IAAI,CAACxnB,YAAY,CAACtpB,SAAS,CACzB,yBAAyB4K,KAAK,CAACvL,OAAO,IAAI,eAAe,EAAE,CAC5D;QAED,IAAI,IAAI,CAAC4qB,WAAW,KAAK,CAAC,EAAE;UAC1B,IAAI,CAACia,KAAK,GAAG,EAAE;;MAEnB,CAAC;MACD+N,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACv9B,OAAO,GAAG,KAAK;QACpB,IAAI,CAACo8B,WAAW,GAAG,KAAK;MAC1B;KACD,CAAC;IAEF,IAAI,CAAC1mB,aAAa,CAAC1J,GAAG,CAACkX,GAAG,CAAC;EAC7B;EAEAgY,iBAAiBA,CAAC3X,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC3O,YAAY,CAACtpB,SAAS,CACzB,+CAA+C,CAChD;MACD;;IAGF,IAAI,CAACspB,YAAY,CAACppB,QAAQ,CAAC,0BAA0B,CAAC;IAEtD,IAAI,CAACioB,cAAc,CAAC+pB,kBAAkB,CAACja,MAAM,CAAC,CAACnK,SAAS,CAAC;MACvDluB,IAAI,EAAG6pB,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,IAAI,CAACA,YAAY,CAACjqB,EAAE,EAAE;UACrC,IAAI,CAAC8pB,YAAY,CAACtpB,SAAS,CACzB,iDAAiD,CAClD;UACD;;QAGF,IAAI,CAACqpB,MAAM,CACR8W,QAAQ,CAAC,CAAC,8BAA8B,EAAE1W,YAAY,CAACjqB,EAAE,CAAC,CAAC,CAC3D2yC,IAAI,CAAEC,OAAO,IAAI;UAChB,IAAI,CAACA,OAAO,EAAE;YACZ,IAAI,CAAC9oB,YAAY,CAACtpB,SAAS,CAAC,6BAA6B,CAAC;;QAE9D,CAAC,CAAC;MACN,CAAC;MACD4K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CACzB,kCAAkC4K,KAAK,CAACvL,OAAO,IAAI,eAAe,EAAE,CACrE;MACH;KACD,CAAC;EACJ;EAEAkwC,cAAcA,CAACtX,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAAC9P,cAAc,CAACG,YAAY,CAAC2P,MAAM,EAAEr3B,kEAAQ,CAACghC,KAAK,CAAC,CAAC9T,SAAS,CAAC;MACjEluB,IAAI,EAAG0yB,IAAI,IAAI;QACb,IAAI,CAAChJ,YAAY,CAACvpB,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACD6K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEA0vC,cAAcA,CAACzX,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAAC9P,cAAc,CAACG,YAAY,CAAC2P,MAAM,EAAEr3B,kEAAQ,CAACihC,KAAK,CAAC,CAAC/T,SAAS,CAAC;MACjEluB,IAAI,EAAG0yB,IAAI,IAAI;QACb,IAAI,CAAChJ,YAAY,CAACvpB,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACD6K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0e,YAAY,CAACtpB,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEAqwC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACE,WAAW,IAAI,CAAC,IAAI,CAAC77B,OAAO,EAAE;MACrC,IAAI,CAACo8B,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC7mB,WAAW,EAAE;MAClB,IAAI,CAACgnB,SAAS,EAAE;;EAEpB;EAEAoB,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC7B,eAAe,IAAI,CAAC,IAAI,CAAC97B,OAAO,EAAE;MACzC,IAAI,CAACo8B,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC7mB,WAAW,EAAE;MAClB,IAAI,CAACgnB,SAAS,EAAE;;EAEpB;EAEAqB,YAAYA,CAAA;IACV,IAAI,CAAClB,eAAe,EAAE;IACtB,IAAI,CAACH,SAAS,CAAC,IAAI,CAAC;EACtB;EAEAsB,YAAYA,CAAA;IACV,IAAI,CAAC5B,UAAU,CAAC3U,KAAK,CAAC;MACpB30B,WAAW,EAAE,EAAE;MACf5F,QAAQ,EAAE;KACX,CAAC;IACF,IAAI,CAAC2vC,eAAe,EAAE;IACtB,IAAI,CAACH,SAAS,CAAC,IAAI,CAAC;EACtB;EAEAuB,eAAeA,CAACC,KAAa;IAC3B,IAAI,IAAI,CAAChC,MAAM,KAAKgC,KAAK,EAAE;MACzB;MACA,IAAI,CAAC/B,SAAS,GAAG,IAAI,CAACA,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;KAC3D,MAAM;MACL;MACA,IAAI,CAACD,MAAM,GAAGgC,KAAK;MACnB,IAAI,CAAC/B,SAAS,GAAG,KAAK;;IAGxB,IAAI,CAACU,eAAe,EAAE;IACtB,IAAI,CAACH,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA;;;EAGA/Q,qBAAqBA,CAAA;IACnB,IAAI,CAAC7W,MAAM,CAAC8W,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEAsE,WAAWA,CAAA;IACT,IAAI,CAACra,aAAa,CAAC0X,WAAW,EAAE;IAChC,IAAI,IAAI,CAACwP,uBAAuB,EAAE;MAChC,IAAI,CAACA,uBAAuB,CAACxP,WAAW,EAAE;;EAE9C;;;uBAvTWyL,iBAAiB,EAAAxsC,+DAAA,CAAA4jC,4EAAA,GAAA5jC,+DAAA,CAAA6jC,oDAAA,GAAA7jC,+DAAA,CAAA6jC,4DAAA,GAAA7jC,+DAAA,CAAA+jC,8EAAA,GAAA/jC,+DAAA,CAAAikC,wEAAA,GAAAjkC,+DAAA,CAAAmkC,0EAAA,GAAAnkC,+DAAA,CAAAskC,qEAAA;IAAA;EAAA;;;YAAjBkI,iBAAiB;MAAA9H,SAAA;MAAA8G,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgG,2BAAA9M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClB9B7kC,4DAAA,aAGC;;UAECA,4DAAA,aAAkE;UAEhEA,uDAAA,aAEO;UAcPA,4DAAA,aAAuD;UAEnDA,uDAAA,aAA6C;UAW/CA,0DAAA,EAAM;UAIRA,4DAAA,cAAyE;UACvEA,uDAAA,eAAqE;UACvEA,0DAAA,EAAM;UAGRA,4DAAA,eAAqC;UAEJA,oDAAA,6BAAqB;UAAAA,0DAAA,EAAK;UACvDA,4DAAA,eAA4B;UAExBA,wDAAA,mBAAA4xC,oDAAA;YAAA,OAAS9M,GAAA,CAAAyM,YAAA,EAAc;UAAA,EAAC;UAIxBvxC,uDAAA,aAA+B;UACjCA,0DAAA,EAAS;UACTA,4DAAA,kBAGC;UAFCA,wDAAA,mBAAA6xC,oDAAA;YAAA,OAAS/M,GAAA,CAAA3F,qBAAA,EAAuB;UAAA,EAAC;UAGjCn/B,uDAAA,aAAiC;UACnCA,0DAAA,EAAS;UAKbA,4DAAA,eAAuB;UAKjBA,wDAAA,2BAAA8xC,2DAAAhrC,MAAA;YAAA,OAAAg+B,GAAA,CAAAx+B,WAAA,GAAAQ,MAAA;UAAA,EAAsC;UAFxC9G,0DAAA,EAME;UACFA,uDAAA,aAEK;UACPA,0DAAA,EAAM;UAGNA,4DAAA,eAA+C;UAUrCA,wDAAA,oBAAA+xC,oDAAAjrC,MAAA;YAAA,IAAAuM,OAAA;YAAA,QAAAA,OAAA,GAERyxB,GAAA,CAAA8K,UAAA,CAAAr8B,GAAA,CACD,UAAU,CAAC,mBADVF,OAAA,CAAAG,QAAA,CAAA1M,MAAA,CAAAmsB,MAAA,CAAA+e,OAAA,GAEF,IAAI,GAAG,IAAI,CACjB;UAAA,EADiB;UATHhyC,0DAAA,EAUE;UACFA,uDAAA,gBAAmD;UACrDA,0DAAA,EAAQ;UACRA,4DAAA,iBACG;UAAAA,oDAAA,2BAAmB;UAAAA,0DAAA,EACrB;UAIHA,4DAAA,eAAyC;UACRA,oDAAA,kBAAU;UAAAA,0DAAA,EAAO;UAChDA,4DAAA,kBAGC;UAFCA,wDAAA,oBAAAiyC,qDAAAnrC,MAAA;YAAA,OAAUg+B,GAAA,CAAA2M,eAAA,CAAA3qC,MAAA,CAAAmsB,MAAA,CAAAr0B,KAAA,CAA0C;UAAA,EAAC;UAGrDoB,4DAAA,kBAA4D;UAC1DA,oDAAA,aACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAsD;UACpDA,oDAAA,eACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAgE;UAC9DA,oDAAA,qCACF;UAAAA,0DAAA,EAAS;UAEXA,4DAAA,kBASC;UARCA,wDAAA,mBAAAkyC,oDAAA;YAAApN,GAAA,CAAA6K,SAAA,GAAA7K,GAAA,CAAA6K,SAAA,KAC6C,KAAK,GAC/D,MAAM,GAAG,KAAK;YAAA,OACd7K,GAAA,CAAAoL,SAAA,CAAU,IAAI,CACf;UAAA,EADe;UAMDlwC,uDAAA,SAIK;UACPA,0DAAA,EAAS;UAKbA,4DAAA,kBAAiE;UAAzDA,wDAAA,mBAAAmyC,oDAAA;YAAA,OAASrN,GAAA,CAAA0M,YAAA,EAAc;UAAA,EAAC;UAC9BxxC,oDAAA,6BACF;UAAAA,0DAAA,EAAS;UAIXA,wDAAA,KAAAoyC,iCAAA,kBASM;UACRpyC,0DAAA,EAAM;UAIRA,4DAAA,eAMC;UAJCA,wDAAA,oBAAAqyC,kDAAAvrC,MAAA;YAAA,OAAAA,MAAA,CAAAmsB,MAAA,CAAAsI,SAAA,GAAAz0B,MAAA,CAAAmsB,MAAA,CAAA+J,YAAA,IAAAl2B,MAAA,CAAAmsB,MAAA,CAAAyI,YAAA,GAE8C,GAAG,IAAIoJ,GAAA,CAAAwK,YAAA,EAEzD;UAAA,EADK;UAGDtvC,wDAAA,KAAAsyC,iCAAA,kBAGM;UAGNtyC,wDAAA,KAAAuyC,iCAAA,kBAQM;UAGNvyC,wDAAA,KAAAwyC,gCAAA,iBA4CK;UAGLxyC,wDAAA,KAAAyyC,iCAAA,kBASM;UAGNzyC,wDAAA,KAAA0yC,iCAAA,kBAKM;UACR1yC,0DAAA,EAAM;;;;UA1PNA,yDAAA,SAAAA,yDAAA,QAAA8kC,GAAA,CAAAmF,WAAA,EAAkC;UAoE1BjqC,uDAAA,IAAuB;UAAvBA,wDAAA,YAAA8kC,GAAA,CAAAx+B,WAAA,CAAuB;UAqBjBtG,uDAAA,GAAsD;UAAtDA,wDAAA,cAAAupC,OAAA,GAAAzE,GAAA,CAAA8K,UAAA,CAAAr8B,GAAA,+BAAAg2B,OAAA,CAAA3qC,KAAA,WAAsD;UAqBhDoB,uDAAA,GAAkC;UAAlCA,wDAAA,aAAA8kC,GAAA,CAAA4K,MAAA,gBAAkC;UAGlC1vC,uDAAA,GAA+B;UAA/BA,wDAAA,aAAA8kC,GAAA,CAAA4K,MAAA,aAA+B;UAG/B1vC,uDAAA,GAAoC;UAApCA,wDAAA,aAAA8kC,GAAA,CAAA4K,MAAA,kBAAoC;UAU5C1vC,uDAAA,GAEC;UAFDA,wDAAA,UAAA8kC,GAAA,CAAA6K,SAAA,0DAEC;UAGC3vC,uDAAA,GAEC;UAFDA,wDAAA,CAAA8kC,GAAA,CAAA6K,SAAA,mDAEC;UAcR3vC,uDAAA,GAAoB;UAApBA,wDAAA,SAAA8kC,GAAA,CAAAqJ,UAAA,KAAoB;UAqBnBnuC,uDAAA,GAA8B;UAA9BA,wDAAA,SAAA8kC,GAAA,CAAAnxB,OAAA,KAAAmxB,GAAA,CAAA3B,KAAA,CAAAx8B,MAAA,CAA8B;UAM9B3G,uDAAA,GAAoC;UAApCA,wDAAA,UAAA8kC,GAAA,CAAAnxB,OAAA,IAAAmxB,GAAA,CAAA3B,KAAA,CAAAx8B,MAAA,OAAoC;UAWrC3G,uDAAA,GAAsB;UAAtBA,wDAAA,SAAA8kC,GAAA,CAAA3B,KAAA,CAAAx8B,MAAA,KAAsB;UA+CrB3G,uDAAA,GAAiC;UAAjCA,wDAAA,SAAA8kC,GAAA,CAAAnxB,OAAA,IAAAmxB,GAAA,CAAA3B,KAAA,CAAAx8B,MAAA,KAAiC;UAYjC3G,uDAAA,GAA6B;UAA7BA,wDAAA,SAAA8kC,GAAA,CAAA0K,WAAA,KAAA1K,GAAA,CAAAnxB,OAAA,CAA6B;;;;;;;;;;;;;;;;;;;;;;;ACtPa;AACpB;AACzB,SAASm6B,QAAQA,CAAC+E,MAAM,GAAG,CAAC,EAAEC,SAAS,GAAGH,4DAAc,EAAE;EAC7D,IAAIE,MAAM,GAAG,CAAC,EAAE;IACZA,MAAM,GAAG,CAAC;EACd;EACA,OAAOD,6CAAK,CAACC,MAAM,EAAEA,MAAM,EAAEC,SAAS,CAAC;AAC3C", "sources": ["./src/app/services/toast.service.ts", "./src/app/views/front/messages/message-chat/message-chat.component.ts", "./src/app/views/front/messages/message-chat/message-chat.component.html", "./src/app/views/front/messages/messages-list/messages-list.component.ts", "./src/app/views/front/messages/messages-list/messages-list.component.html", "./src/app/views/front/messages/messages-routing.module.ts", "./src/app/views/front/messages/messages-sidebar/messages-sidebar.component.ts", "./src/app/views/front/messages/messages-sidebar/messages-sidebar.component.html", "./src/app/views/front/messages/messages.module.ts", "./src/app/views/front/messages/user-list/user-list.component.ts", "./src/app/views/front/messages/user-list/user-list.component.html", "./node_modules/rxjs/dist/esm/internal/observable/interval.js"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { Toast  } from 'src/app/models/message.model';\n@Injectable({\n  providedIn: 'root'\n})\nexport class ToastService {\n  private toastsSubject = new BehaviorSubject<Toast[]>([]);\n  toasts$ = this.toastsSubject.asObservable();\n  private currentId = 0;\n\n  constructor() { }\n\n  show(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info', duration = 5000) {\n    const id = this.currentId++;\n    const toast: Toast = { id, type, message, duration };\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, toast]);\n\n    if (duration > 0) {\n      setTimeout(() => this.dismiss(id), duration);\n    }\n  }\n\n  showSuccess(message: string, duration = 3000) {\n    this.show(message, 'success', duration);\n  }\n\n  showError(message: string, duration = 5000) {\n    this.show(message, 'error', duration);\n  }\n\n  showWarning(message: string, duration = 4000) {\n    this.show(message, 'warning', duration);\n  }\n\n  showInfo(message: string, duration = 3000) {\n    this.show(message, 'info', duration);\n  }\n\n  dismiss(id: number) {\n    const currentToasts = this.toastsSubject.value.filter(t => t.id !== id);\n    this.toastsSubject.next(currentToasts);\n  }\n\n  clear() {\n    this.toastsSubject.next([]);\n  }\n}", "import {\n  Component,\n  On<PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>hild,\n  ElementRef,\n  AfterViewChecked,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription, Observable } from 'rxjs';\nimport { User } from '@app/models/user.model';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport {\n  Message,\n  Conversation,\n  MessageType,\n  CallType,\n} from 'src/app/models/message.model';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { MessageService } from '@app/services/message.service';\nimport { LoggerService } from 'src/app/services/logger.service';\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: 'message-chat.component.html',\n  styleUrls: ['./message-chat.component.css'],\n})\nexport class MessageChatComponent\n  implements OnInit, OnDestroy, AfterViewChecked\n{\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  messages: Message[] = [];\n  messageForm: FormGroup;\n  conversation: Conversation | null = null;\n  loading = true;\n  error: any;\n  currentUserId: string | null = null;\n  currentUsername: string = 'You';\n  otherParticipant: User | null = null;\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n  isUploading = false;\n  isTyping = false;\n  typingTimeout: any;\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n\n  private readonly MAX_MESSAGES_PER_SIDE = 5; // Nombre maximum de messages à afficher par côté (expéditeur/destinataire)\n  private readonly MAX_MESSAGES_TO_LOAD = 10; // Nombre maximum de messages à charger à la fois (pagination)\n  private readonly MAX_TOTAL_MESSAGES = 100; // Limite totale de messages à conserver en mémoire\n  private currentPage = 1; // Page actuelle pour la pagination\n  isLoadingMore = false; // Indicateur de chargement en cours (public pour le template)\n  hasMoreMessages = true; // Indique s'il y a plus de messages à charger (public pour le template)\n  private subscriptions: Subscription = new Subscription();\n\n  // Variables d'interface consolidées\n  selectedTheme: string = 'theme-default';\n  showThemeSelector: boolean = false;\n  showMainMenu: boolean = false;\n  showEmojiPicker: boolean = false;\n\n  // Variables d'appel consolidées\n  incomingCall: any = null;\n  activeCall: any = null;\n  showCallModal = false;\n  showActiveCallModal = false;\n  isCallMuted = false;\n  isVideoEnabled = true;\n  callDuration = 0;\n  callTimer: any = null;\n\n  // Variables de notification et message ultra-consolidées\n  notifications: any[] = [];\n  showNotificationPanel: boolean = false;\n  unreadNotificationCount: number = 0;\n  selectedNotifications: Set<string> = new Set();\n  showDeleteConfirmModal: boolean = false;\n  showMessageOptions: { [key: string]: boolean } = {};\n  editingMessageId: string | null = null;\n  editingContent = '';\n  replyingToMessage: any = null;\n\n  // Variables de recherche consolidées\n  showSearchBar = false;\n  searchQuery = '';\n  isSearching = false;\n  searchMode = false;\n  searchResults: any[] = [];\n\n  // Variables de panneau consolidées\n  showPinnedMessages = false;\n  pinnedMessages: any[] = [];\n  showStatusSelector = false;\n  showReactionPicker: { [key: string]: boolean } = {};\n  availableReactions = ['👍', '❤️', '😂', '😮', '😢', '😡'];\n  showDeleteConfirm: { [key: string]: boolean } = {};\n  showPinConfirm: { [key: string]: boolean } = {};\n  isPinning: { [key: string]: boolean } = {};\n\n  // Variables de transfert consolidées\n  showForwardModal = false;\n  forwardingMessage: any = null;\n  selectedConversations: string[] = [];\n  availableConversations: any[] = [];\n  isForwarding = false;\n  isLoadingConversations = false;\n\n  // Variables de notification étendues (consolidées avec les précédentes)\n  notificationFilter = 'all';\n  isLoadingNotifications = false;\n  isMarkingAsRead = false;\n  isDeletingNotifications = false;\n  hasMoreNotifications = false;\n  showNotificationSettings = false;\n  notificationSounds = true;\n  notificationPreview = true;\n  autoMarkAsRead = true;\n\n  // Variables d'appel consolidées (ajoutées)\n  isCallMinimized = false;\n  callQuality = 'connecting';\n  showCallControls = false;\n\n  // Variables de statut utilisateur ultra-consolidées\n  onlineUsers: Map<string, User> = new Map();\n  currentUserStatus: string = 'online';\n  lastActivityTime: Date = new Date();\n  autoAwayTimeout: any = null;\n  isUpdatingStatus = false;\n  showUserStatusPanel = false;\n  showCallHistoryPanel = false;\n  showCallStatsPanel = false;\n  showVoiceMessagesPanel = false;\n  callHistory: any[] = [];\n  voiceMessages: any[] = [];\n  autoAwayDelay: number = 300000; // 5 minutes\n  localVideoElement: HTMLVideoElement | null = null;\n  remoteVideoElement: HTMLVideoElement | null = null;\n  statusFilterType = 'all';\n\n  // Utiliser les emojis du service au lieu de dupliquer\n  get commonEmojis(): string[] {\n    return this.MessageService.getCommonEmojis();\n  }\n\n  /**\n   * Configuration consolidée des boutons d'action de l'en-tête\n   */\n  getHeaderActions() {\n    return [\n      {\n        class: 'btn-audio-call',\n        icon: 'fas fa-phone-alt',\n        title: 'Appel audio',\n        onClick: () => this.initiateCall('AUDIO'),\n        isActive: false,\n      },\n      {\n        class: 'btn-video-call',\n        icon: 'fas fa-video',\n        title: 'Appel vidéo',\n        onClick: () => this.initiateCall('VIDEO'),\n        isActive: false,\n      },\n      {\n        class: 'btn-search',\n        icon: 'fas fa-search',\n        title: 'Rechercher',\n        onClick: () => this.toggleSearchBar(),\n        isActive: this.showSearchBar,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-pinned relative',\n        icon: 'fas fa-thumbtack',\n        title: `Messages épinglés (${this.getPinnedMessagesCount()})`,\n        onClick: () => this.togglePinnedMessages(),\n        isActive: this.showPinnedMessages,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.getPinnedMessagesCount() > 0\n            ? {\n                count: this.getPinnedMessagesCount(),\n                class: 'bg-[#4f5fad] dark:bg-[#6d78c9]',\n                animate: false,\n              }\n            : null,\n      },\n      {\n        class: 'btn-notifications relative',\n        icon: 'fas fa-bell',\n        title: 'Notifications',\n        onClick: () => this.toggleNotificationPanel(),\n        isActive: this.showNotificationPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.unreadNotificationCount > 0\n            ? {\n                count: this.unreadNotificationCount,\n                class: 'bg-gradient-to-r from-[#ff6b69] to-[#ee5a52]',\n                animate: true,\n              }\n            : null,\n      },\n      {\n        class: 'btn-history relative',\n        icon: 'fas fa-history',\n        title: 'Historique des appels',\n        onClick: () => this.toggleCallHistoryPanel(),\n        isActive: this.showCallHistoryPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-stats relative',\n        icon: 'fas fa-chart-bar',\n        title: \"Statistiques d'appels\",\n        onClick: () => this.toggleCallStatsPanel(),\n        isActive: this.showCallStatsPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n      },\n      {\n        class: 'btn-voice-messages relative',\n        icon: 'fas fa-microphone',\n        title: 'Messages vocaux',\n        onClick: () => this.toggleVoiceMessagesPanel(),\n        isActive: this.showVoiceMessagesPanel,\n        activeClass: { 'text-[#4f5fad] dark:text-[#6d78c9]': true },\n        badge:\n          this.voiceMessages.length > 0\n            ? {\n                count: this.voiceMessages.length,\n                class: 'bg-[#4f5fad]',\n                animate: false,\n              }\n            : null,\n      },\n    ];\n  }\n\n  constructor(\n    private MessageService: MessageService,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private fb: FormBuilder,\n    public statusService: UserStatusService,\n    public router: Router,\n    private toastService: ToastService,\n    private logger: LoggerService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]],\n    });\n  }\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n    }\n\n    // Chargement des messages vocaux simplifié\n    this.subscribeToNotifications();\n    this.subscribeToUserStatus();\n    this.initializeUserStatus();\n    this.startActivityTracking();\n\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n\n    const routeSub = this.route.params\n      .pipe(\n        filter((params) => params['id']),\n        distinctUntilChanged(),\n        switchMap((params) => {\n          this.loading = true;\n          this.messages = [];\n          this.currentPage = 1;\n          this.hasMoreMessages = true;\n\n          return this.MessageService.getConversation(\n            params['id'],\n            this.MAX_MESSAGES_TO_LOAD,\n            this.currentPage\n          );\n        })\n      )\n      .subscribe({\n        next: (conversation) => {\n          this.handleConversationLoaded(conversation);\n        },\n        error: (error) => {\n          this.handleError('Failed to load conversation', error);\n        },\n      });\n    this.subscriptions.add(routeSub);\n  }\n\n  /**\n   * Gère les erreurs de manière centralisée\n   */\n  private handleError(\n    message: string,\n    error: any,\n    resetLoading: boolean = true\n  ): void {\n    this.logger.error('MessageChat', message, error);\n    if (resetLoading) {\n      this.loading = false;\n      this.isUploading = false;\n      this.isLoadingMore = false;\n    }\n    this.error = error;\n    this.toastService.showError(message);\n  }\n\n  /**\n   * Gère les succès de manière centralisée\n   */\n  private handleSuccess(message?: string, callback?: () => void): void {\n    if (message) {\n      this.toastService.showSuccess(message);\n    }\n    if (callback) {\n      callback();\n    }\n  }\n\n  // Utiliser le service FileService au lieu de dupliquer la logique\n  getFileIcon(mimeType?: string): string {\n    return this.MessageService.getFileIcon(mimeType);\n  }\n\n  getFileType(mimeType?: string): string {\n    return this.MessageService.getFileType(mimeType);\n  }\n\n  private handleConversationLoaded(conversation: Conversation): void {\n    this.conversation = conversation;\n\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.otherParticipant =\n        conversation?.participants?.find(\n          (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n        ) || null;\n      this.messages = [];\n    } else {\n      const conversationMessages = [...(conversation?.messages || [])];\n\n      conversationMessages.sort((a, b) => {\n        const timeA =\n          a.timestamp instanceof Date\n            ? a.timestamp.getTime()\n            : new Date(a.timestamp as string).getTime();\n        const timeB =\n          b.timestamp instanceof Date\n            ? b.timestamp.getTime()\n            : new Date(b.timestamp as string).getTime();\n        return timeA - timeB;\n      });\n\n      this.messages = conversationMessages;\n    }\n\n    this.otherParticipant =\n      conversation?.participants?.find(\n        (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n      ) || null;\n\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n\n    // Messages épinglés supprimés\n    this.markMessagesAsRead();\n\n    if (this.conversation?.id) {\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n  }\n\n  private subscribeToConversationUpdates(conversationId: string): void {\n    const sub = this.MessageService.subscribeToConversationUpdates(\n      conversationId\n    ).subscribe({\n      next: (updatedConversation) => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages\n          ? [...updatedConversation.messages]\n          : [];\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to conversation updates lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToNewMessages(conversationId: string): void {\n    const sub = this.MessageService.subscribeToNewMessages(\n      conversationId\n    ).subscribe({\n      next: (newMessage) => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA =\n              a.timestamp instanceof Date\n                ? a.timestamp.getTime()\n                : new Date(a.timestamp as string).getTime();\n            const timeB =\n              b.timestamp instanceof Date\n                ? b.timestamp.getTime()\n                : new Date(b.timestamp as string).getTime();\n            return timeA - timeB;\n          });\n\n          setTimeout(() => this.scrollToBottom(), 100);\n\n          if (\n            newMessage.sender?.id !== this.currentUserId &&\n            newMessage.sender?._id !== this.currentUserId\n          ) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to new messages lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToTypingIndicators(conversationId: string): void {\n    const sub = this.MessageService.subscribeToTypingIndicator(\n      conversationId\n    ).subscribe({\n      next: (event) => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private markMessagesAsRead(): void {\n    const unreadMessages = this.messages.filter(\n      (msg) =>\n        !msg.isRead &&\n        (msg.receiver?.id === this.currentUserId ||\n          msg.receiver?._id === this.currentUserId)\n    );\n\n    unreadMessages.forEach((msg) => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: (error) => {\n            // Error handled silently\n          },\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    // Validate file size (e.g., 5MB max)\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n\n    // Validate file type\n    const validTypes = [\n      'image/jpeg',\n      'image/png',\n      'image/gif',\n      'application/pdf',\n      'application/msword',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    ];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError(\n        'Invalid file type. Only images, PDFs and Word docs are allowed'\n      );\n      return;\n    }\n\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n\n  removeAttachment(): void {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n\n  private typingTimer: any;\n  private isCurrentlyTyping = false;\n  private readonly TYPING_DELAY = 500; // Délai en ms avant d'envoyer l'événement de frappe\n  private readonly TYPING_TIMEOUT = 3000; // Délai en ms avant d'arrêter l'indicateur de frappe\n\n  /**\n   * Gère l'événement de frappe de l'utilisateur\n   * Envoie un indicateur de frappe avec un délai pour éviter trop de requêtes\n   */\n  onTyping(): void {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n\n    const conversationId = this.conversation.id;\n    clearTimeout(this.typingTimer);\n\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {\n          // Success handled silently\n        },\n        error: (error) => {\n          // Error handled silently\n        },\n      });\n    }\n\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {\n            // Success handled silently\n          },\n          error: (error) => {\n            // Error handled silently\n          },\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n\n  /**\n   * Méthode générique pour basculer les panneaux\n   */\n  private togglePanel(panelName: string, closeOthers: boolean = true): void {\n    const panels = {\n      theme: 'showThemeSelector',\n      menu: 'showMainMenu',\n      emoji: 'showEmojiPicker',\n      notification: 'showNotificationPanel',\n      search: 'showSearchBar',\n      status: 'showStatusSelector',\n    };\n\n    const currentPanel = panels[panelName as keyof typeof panels];\n    if (currentPanel) {\n      (this as any)[currentPanel] = !(this as any)[currentPanel];\n\n      if (closeOthers && (this as any)[currentPanel]) {\n        Object.values(panels).forEach((panel) => {\n          if (panel !== currentPanel) {\n            (this as any)[panel] = false;\n          }\n        });\n      }\n    }\n  }\n\n  // Méthodes de toggle ultra-consolidées - Fusion de toggleMethods et toggleConfig\n  readonly toggleMethods = {\n    themeSelector: (): void => this.togglePanel('theme'),\n    mainMenu: (): void => this.togglePanel('menu'),\n    emojiPicker: (): void => this.togglePanel('emoji'),\n    changeTheme: (theme: string): void => {\n      this.selectedTheme = theme;\n      this.showThemeSelector = false;\n      localStorage.setItem('chat-theme', theme);\n    },\n    pinnedMessages: (): void => {\n      this.showPinnedMessages = !this.showPinnedMessages;\n    },\n    searchBar: (): void => {\n      this.togglePanel('search');\n      if (!this.showSearchBar) this.clearSearch();\n    },\n    statusSelector: (): void => this.togglePanel('status'),\n    notificationSettings: (): void => {\n      this.showNotificationSettings = !this.showNotificationSettings;\n    },\n    userStatusPanel: (): void => {\n      this.showUserStatusPanel = !this.showUserStatusPanel;\n    },\n    callMinimize: (): void => {\n      this.isCallMinimized = !this.isCallMinimized;\n    },\n    callHistoryPanel: (): void => {\n      this.showCallHistoryPanel = !this.showCallHistoryPanel;\n    },\n    callStatsPanel: (): void => {\n      this.showCallStatsPanel = !this.showCallStatsPanel;\n    },\n    voiceMessagesPanel: (): void => {\n      this.showVoiceMessagesPanel = !this.showVoiceMessagesPanel;\n    },\n  };\n\n  // Méthodes publiques pour le template - Toutes les méthodes de toggle consolidées\n  toggleThemeSelector = this.toggleMethods.themeSelector;\n  toggleMainMenu = this.toggleMethods.mainMenu;\n  toggleEmojiPicker = this.toggleMethods.emojiPicker;\n  changeTheme = this.toggleMethods.changeTheme;\n  togglePinnedMessages = this.toggleMethods.pinnedMessages;\n  toggleSearchBar = this.toggleMethods.searchBar;\n  toggleStatusSelector = this.toggleMethods.statusSelector;\n  toggleNotificationSettings = this.toggleMethods.notificationSettings;\n  toggleUserStatusPanel = this.toggleMethods.userStatusPanel;\n  toggleCallMinimize = this.toggleMethods.callMinimize;\n  toggleCallHistoryPanel = this.toggleMethods.callHistoryPanel;\n  toggleCallStatsPanel = this.toggleMethods.callStatsPanel;\n  toggleVoiceMessagesPanel = this.toggleMethods.voiceMessagesPanel;\n\n  // Méthodes de conversation ultra-consolidées\n  clearConversation(): void {\n    if (!this.conversation?.id || this.messages.length === 0) {\n      this.toastService.showWarning('Aucune conversation à vider');\n      return;\n    }\n\n    if (\n      confirm(\n        'Êtes-vous sûr de vouloir vider cette conversation ? Cette action supprimera tous les messages de votre vue locale.'\n      )\n    ) {\n      this.messages = [];\n      this.showMainMenu = false;\n      this.toastService.showSuccess('Conversation vidée avec succès');\n    }\n  }\n\n  exportConversation(): void {\n    if (!this.conversation?.id || this.messages.length === 0) {\n      this.toastService.showWarning('Aucune conversation à exporter');\n      return;\n    }\n\n    const conversationName = this.conversation.isGroup\n      ? this.conversation.groupName || 'Groupe sans nom'\n      : this.otherParticipant?.username || 'Conversation privée';\n\n    const exportData = {\n      conversation: {\n        id: this.conversation.id,\n        name: conversationName,\n        isGroup: this.conversation.isGroup,\n        participants: this.conversation.participants,\n        createdAt: this.conversation.createdAt,\n      },\n      messages: this.messages.map((msg) => ({\n        id: msg.id,\n        content: msg.content,\n        sender: msg.sender,\n        timestamp: msg.timestamp,\n        type: msg.type,\n      })),\n      exportedAt: new Date().toISOString(),\n      exportedBy: this.currentUserId,\n    };\n\n    const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n      type: 'application/json',\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n\n    const safeFileName = conversationName\n      .replace(/[^a-z0-9]/gi, '_')\n      .toLowerCase();\n    const dateStr = new Date().toISOString().split('T')[0];\n    link.download = `conversation-${safeFileName}-${dateStr}.json`;\n\n    link.click();\n    window.URL.revokeObjectURL(url);\n\n    this.showMainMenu = false;\n    this.toastService.showSuccess('Conversation exportée avec succès');\n  }\n\n  // Méthodes de conversation consolidées - Suppression des duplications\n  readonly conversationMethods = {\n    showInfo: (): void => {\n      this.showMainMenu = false;\n      this.toastService.showInfo('Fonctionnalité en cours de développement');\n    },\n    showSettings: (): void => {\n      this.showMainMenu = false;\n      this.toastService.showInfo('Fonctionnalité en cours de développement');\n    },\n  };\n\n  // Méthodes publiques pour le template\n  toggleConversationInfo = this.conversationMethods.showInfo;\n  toggleConversationSettings = this.conversationMethods.showSettings;\n\n  sendMessage(): void {\n    if (\n      (this.messageForm.invalid && !this.selectedFile) ||\n      !this.currentUserId ||\n      !this.otherParticipant?.id\n    ) {\n      return;\n    }\n\n    this.stopTypingIndicator();\n\n    const content = this.messageForm.get('content')?.value;\n\n    // Créer un message temporaire pour l'affichage immédiat (comme dans Facebook Messenger)\n    const tempMessage: Message = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername,\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient',\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true, // Marquer comme en attente\n    };\n\n    // Si un fichier est sélectionné, ajouter l'aperçu au message temporaire\n    if (this.selectedFile) {\n      // Déterminer le type de fichier\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n\n        // Pour les images, ajouter un aperçu immédiat\n        if (this.previewUrl) {\n          tempMessage.attachments = [\n            {\n              id: 'temp-attachment',\n              url: this.previewUrl ? this.previewUrl.toString() : '',\n              type: MessageType.IMAGE,\n              name: this.selectedFile.name,\n              size: this.selectedFile.size,\n            },\n          ];\n        }\n      }\n\n      // Définir le type de message en fonction du type de fichier\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n\n    // Ajouter immédiatement le message temporaire à la liste\n    this.messages = [...this.messages, tempMessage];\n\n    // Réinitialiser le formulaire immédiatement pour une meilleure expérience utilisateur\n    const fileToSend = this.selectedFile; // Sauvegarder une référence\n    this.messageForm.reset();\n    this.removeAttachment();\n\n    // Forcer le défilement vers le bas immédiatement\n    setTimeout(() => this.scrollToBottom(true), 50);\n\n    // Maintenant, envoyer le message au serveur\n    this.isUploading = true;\n\n    const sendSub = this.MessageService.sendMessage(\n      this.otherParticipant.id,\n      content,\n      fileToSend || undefined,\n      MessageType.TEXT,\n      this.conversation?.id\n    ).subscribe({\n      next: (message) => {\n        this.messages = this.messages.map((msg) =>\n          msg.id === tempMessage.id ? message : msg\n        );\n        this.isUploading = false;\n      },\n      error: (error) => {\n        this.messages = this.messages.map((msg) => {\n          if (msg.id === tempMessage.id) {\n            return {\n              ...msg,\n              isPending: false,\n              isError: true,\n            };\n          }\n          return msg;\n        });\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      },\n    });\n\n    this.subscriptions.add(sendSub);\n  }\n\n  // Méthodes déléguées au service - Consolidées en un seul objet\n  readonly serviceMethods = {\n    formatMessageTime: (timestamp: string | Date | undefined): string =>\n      this.MessageService.formatMessageTime(timestamp),\n    formatLastActive: (lastActive: string | Date | undefined): string =>\n      this.MessageService.formatLastActive(lastActive),\n    formatMessageDate: (timestamp: string | Date | undefined): string =>\n      this.MessageService.formatMessageDate(timestamp),\n    shouldShowDateHeader: (index: number): boolean =>\n      this.MessageService.shouldShowDateHeader(this.messages, index),\n    getMessageType: (message: Message | null | undefined): MessageType =>\n      this.MessageService.getMessageType(message),\n    hasImage: (message: Message | null | undefined): boolean =>\n      this.MessageService.hasImage(message),\n    isVoiceMessage: (message: Message | null | undefined): boolean =>\n      this.MessageService.isVoiceMessage(message),\n    getVoiceMessageUrl: (message: Message | null | undefined): string =>\n      this.MessageService.getVoiceMessageUrl(message),\n    getVoiceMessageDuration: (message: Message | null | undefined): number =>\n      this.MessageService.getVoiceMessageDuration(message),\n    getVoiceBarHeight: (index: number): number =>\n      this.MessageService.getVoiceBarHeight(index),\n    formatVoiceDuration: (seconds: number): string =>\n      this.MessageService.formatVoiceDuration(seconds),\n    getImageUrl: (message: Message | null | undefined): string =>\n      this.MessageService.getImageUrl(message),\n    getMessageTypeClass: (message: Message | null | undefined): string =>\n      this.MessageService.getMessageTypeClass(message, this.currentUserId),\n  };\n\n  // Méthodes publiques pour le template\n  formatMessageTime = this.serviceMethods.formatMessageTime;\n  formatLastActive = this.serviceMethods.formatLastActive;\n  formatMessageDate = this.serviceMethods.formatMessageDate;\n  shouldShowDateHeader = this.serviceMethods.shouldShowDateHeader;\n  getMessageType = this.serviceMethods.getMessageType;\n  hasImage = this.serviceMethods.hasImage;\n  isVoiceMessage = this.serviceMethods.isVoiceMessage;\n  getVoiceMessageUrl = this.serviceMethods.getVoiceMessageUrl;\n  getVoiceMessageDuration = this.serviceMethods.getVoiceMessageDuration;\n  getVoiceBarHeight = this.serviceMethods.getVoiceBarHeight;\n  formatVoiceDuration = this.serviceMethods.formatVoiceDuration;\n  getImageUrl = this.serviceMethods.getImageUrl;\n  getMessageTypeClass = this.serviceMethods.getMessageTypeClass;\n\n  // La méthode ngAfterViewChecked est implémentée plus bas dans le fichier\n\n  // Méthode pour détecter le défilement vers le haut et charger plus de messages\n  onScroll(event: any): void {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n\n    // Si on est proche du haut de la liste et qu'on n'est pas déjà en train de charger\n    if (\n      scrollTop < 50 &&\n      !this.isLoadingMore &&\n      this.conversation?.id &&\n      this.hasMoreMessages\n    ) {\n      // Afficher un indicateur de chargement en haut de la liste\n      this.showLoadingIndicator();\n\n      // Sauvegarder la hauteur actuelle et la position des messages\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n\n      // Marquer comme chargement en cours\n      this.isLoadingMore = true;\n\n      // Charger plus de messages avec un délai réduit\n      this.loadMoreMessages();\n\n      // Maintenir la position de défilement pour que l'utilisateur reste au même endroit\n      // en utilisant le premier message visible comme ancre\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(\n              firstVisibleMessage.id\n            );\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({ block: 'center' });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n\n  // Méthode pour trouver le premier message visible dans la vue\n  private getFirstVisibleMessage(): Message | null {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length)\n      return null;\n\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find((m) => m.id === messageId) || null;\n      }\n    }\n\n    return null;\n  }\n\n  // Méthode pour trouver un élément de message par ID\n  private findMessageElement(\n    messageId: string | undefined\n  ): HTMLElement | null {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(\n      `[data-message-id=\"${messageId}\"]`\n    );\n  }\n\n  // Afficher un indicateur de chargement en haut de la liste\n  private showLoadingIndicator(): void {\n    // Créer l'indicateur s'il n'existe pas déjà\n    if (!document.getElementById('message-loading-indicator')) {\n      const indicator = document.createElement('div');\n      indicator.id = 'message-loading-indicator';\n      indicator.className = 'text-center py-2 text-gray-500 text-sm';\n      indicator.innerHTML =\n        '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n\n      if (this.messagesContainer?.nativeElement) {\n        this.messagesContainer.nativeElement.prepend(indicator);\n      }\n    }\n  }\n\n  // Masquer l'indicateur de chargement\n  private hideLoadingIndicator(): void {\n    const indicator = document.getElementById('message-loading-indicator');\n    if (indicator && indicator.parentNode) {\n      indicator.parentNode.removeChild(indicator);\n    }\n  }\n\n  // Méthode pour charger plus de messages (style Facebook Messenger)\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages)\n      return;\n\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n\n    // Augmenter la page pour charger les messages plus anciens\n    this.currentPage++;\n\n    // Charger plus de messages depuis le serveur avec pagination\n    this.MessageService.getConversation(\n      this.conversation.id,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        if (\n          conversation &&\n          conversation.messages &&\n          conversation.messages.length > 0\n        ) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n\n          // Créer un Set des IDs existants pour une recherche de doublons plus rapide\n          const existingIds = new Set(oldMessages.map((msg) => msg.id));\n\n          // Filtrer et trier les nouveaux messages plus efficacement\n          const newMessages = conversation.messages\n            .filter((msg) => !existingIds.has(msg.id))\n            .sort((a, b) => {\n              const timeA = new Date(a.timestamp as string).getTime();\n              const timeB = new Date(b.timestamp as string).getTime();\n              return timeA - timeB;\n            });\n\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages =\n              newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: (error) => {\n        this.logger.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      },\n    });\n  }\n\n  // Méthode utilitaire pour comparer les timestamps\n  private isSameTimestamp(\n    timestamp1: string | Date | undefined,\n    timestamp2: string | Date | undefined\n  ): boolean {\n    if (!timestamp1 || !timestamp2) return false;\n\n    try {\n      const time1 =\n        timestamp1 instanceof Date\n          ? timestamp1.getTime()\n          : new Date(timestamp1 as string).getTime();\n      const time2 =\n        timestamp2 instanceof Date\n          ? timestamp2.getTime()\n          : new Date(timestamp2 as string).getTime();\n      return Math.abs(time1 - time2) < 1000; // Tolérance d'une seconde\n    } catch (error) {\n      return false;\n    }\n  }\n\n  scrollToBottom(force: boolean = false): void {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n\n      // Utiliser requestAnimationFrame pour s'assurer que le DOM est prêt\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom =\n          container.scrollHeight - container.clientHeight <=\n          container.scrollTop + 150;\n\n        // Faire défiler vers le bas si:\n        // - force est true (pour les nouveaux messages envoyés par l'utilisateur)\n        // - ou si l'utilisateur est déjà proche du bas\n        if (force || isScrolledToBottom) {\n          // Utiliser une animation fluide pour le défilement (comme dans Messenger)\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth',\n          });\n        }\n      });\n    } catch (err) {\n      this.logger.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n\n  // Méthode pour ouvrir l'image en plein écran (style Messenger)\n  /**\n   * Active/désactive l'enregistrement vocal\n   */\n  toggleVoiceRecording(): void {\n    this.isRecordingVoice = !this.isRecordingVoice;\n\n    if (!this.isRecordingVoice) {\n      // Si on désactive l'enregistrement, réinitialiser la durée\n      this.voiceRecordingDuration = 0;\n    }\n  }\n\n  /**\n   * Gère la fin de l'enregistrement vocal\n   * @param audioBlob Blob audio enregistré\n   */\n  onVoiceRecordingComplete(audioBlob: Blob): void {\n    if (!this.conversation?.id && !this.otherParticipant?.id) {\n      this.toastService.showError('No conversation or recipient selected');\n      this.isRecordingVoice = false;\n      return;\n    }\n\n    const receiverId = this.otherParticipant?.id || '';\n\n    this.MessageService.sendVoiceMessage(\n      receiverId,\n      audioBlob,\n      this.conversation?.id,\n      this.voiceRecordingDuration\n    ).subscribe({\n      next: (message) => {\n        this.isRecordingVoice = false;\n        this.voiceRecordingDuration = 0;\n        this.scrollToBottom(true);\n      },\n      error: (error) => {\n        this.toastService.showError('Failed to send voice message');\n        this.isRecordingVoice = false;\n      },\n    });\n  }\n\n  /**\n   * Gère l'annulation de l'enregistrement vocal\n   */\n  onVoiceRecordingCancelled(): void {\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n  }\n\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl: string): void {\n    window.open(imageUrl, '_blank');\n  }\n\n  /**\n   * Détecte les changements après chaque vérification de la vue\n   * Cela permet de s'assurer que les messages vocaux sont correctement affichés\n   * et que le défilement est maintenu\n   */\n  ngAfterViewChecked(): void {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n\n    // Forcer la détection des changements pour les messages vocaux\n    // Cela garantit que les messages vocaux sont correctement affichés même après avoir quitté la conversation\n    if (this.messages.some((msg) => msg.type === MessageType.VOICE_MESSAGE)) {\n      // Utiliser setTimeout pour éviter l'erreur ExpressionChangedAfterItHasBeenCheckedError\n      setTimeout(() => {\n        this.cdr.detectChanges();\n      }, 0);\n    }\n  }\n\n  /**\n   * Arrête l'indicateur de frappe\n   */\n  private stopTypingIndicator(): void {\n    if (this.isCurrentlyTyping && this.conversation?.id) {\n      this.isCurrentlyTyping = false;\n      clearTimeout(this.typingTimer);\n\n      const conversationId = this.conversation?.id;\n      if (conversationId) {\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {\n            // Success handled silently\n          },\n          error: (error) => {\n            // Error handled silently\n          },\n        });\n      }\n    }\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  // Méthodes dupliquées supprimées - Déjà définies dans les objets consolidés\n\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji: string): void {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector(\n          '.whatsapp-input-field'\n        ) as HTMLInputElement;\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  private subscribeToNotifications(): void {\n    const notificationSub =\n      this.MessageService.subscribeToNewNotifications().subscribe({\n        next: (notification) => {\n          this.notifications.unshift(notification);\n          this.updateNotificationCount();\n\n          this.MessageService.play('notification');\n\n          if (\n            notification.type === 'NEW_MESSAGE' &&\n            notification.conversationId === this.conversation?.id\n          ) {\n            if (notification.id) {\n              this.MessageService.markAsRead([notification.id]).subscribe();\n            }\n          }\n        },\n        error: (error) => {\n          // Error handled silently\n        },\n      });\n    this.subscriptions.add(notificationSub);\n\n    const notificationsListSub = this.MessageService.notifications$.subscribe({\n      next: (notifications) => {\n        this.notifications = notifications;\n        this.updateNotificationCount();\n      },\n      error: (error) => {\n        // Error handled silently\n      },\n    });\n    this.subscriptions.add(notificationsListSub);\n\n    const notificationCountSub =\n      this.MessageService.notificationCount$.subscribe({\n        next: (count) => {\n          this.unreadNotificationCount = count;\n        },\n      });\n    this.subscriptions.add(notificationCountSub);\n\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: (call) => {\n        if (call) {\n          this.incomingCall = call;\n          this.showCallModal = true;\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      },\n    });\n    this.subscriptions.add(callSub);\n\n    const activeCallSub = this.MessageService.activeCall$.subscribe({\n      next: (call) => {\n        this.activeCall = call;\n        if (call) {\n          this.showActiveCallModal = true;\n          this.startCallTimerMethod();\n        } else {\n          this.showActiveCallModal = false;\n          this.stopCallTimerMethod();\n          this.resetCallStateMethod();\n        }\n      },\n    });\n    this.subscriptions.add(activeCallSub);\n\n    // S'abonner aux flux vidéo locaux\n    const localStreamSub = this.MessageService.localStream$.subscribe({\n      next: (stream: MediaStream | null) => {\n        if (stream && this.localVideoElement) {\n          this.localVideoElement.srcObject = stream;\n        }\n      },\n    });\n    this.subscriptions.add(localStreamSub);\n\n    // S'abonner aux flux vidéo distants\n    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({\n      next: (stream: MediaStream | null) => {\n        if (stream && this.remoteVideoElement) {\n          this.remoteVideoElement.srcObject = stream;\n        }\n      },\n    });\n    this.subscriptions.add(remoteStreamSub);\n  }\n\n  /**\n   * Initie un appel audio ou vidéo avec l'autre participant\n   * @param type Type d'appel (AUDIO ou VIDEO)\n   */\n  initiateCall(type: 'AUDIO' | 'VIDEO'): void {\n    if (!this.otherParticipant || !this.otherParticipant.id) {\n      return;\n    }\n\n    this.MessageService.initiateCall(\n      this.otherParticipant.id,\n      type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO,\n      this.conversation?.id\n    ).subscribe({\n      next: (call) => {\n        // Call initiated successfully\n      },\n      error: (error) => {\n        this.toastService.showError(\n          \"Impossible d'initier l'appel. Veuillez réessayer.\"\n        );\n      },\n    });\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(): void {\n    if (!this.incomingCall) {\n      return;\n    }\n\n    this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n      next: (call) => {\n        this.showCallModal = false;\n        this.incomingCall = null;\n        this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';\n        this.callQuality = 'connecting';\n        this.toastService.showSuccess('Appel connecté');\n      },\n      error: (error) => {\n        this.toastService.showError(\n          \"Impossible d'accepter l'appel. Veuillez réessayer.\"\n        );\n        this.showCallModal = false;\n        this.incomingCall = null;\n      },\n    });\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(): void {\n    if (!this.incomingCall) {\n      return;\n    }\n\n    this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n      next: (call) => {\n        this.showCallModal = false;\n        this.incomingCall = null;\n      },\n      error: (error) => {\n        this.showCallModal = false;\n        this.incomingCall = null;\n      },\n    });\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(): void {\n    let activeCall: any = null;\n\n    const sub = this.MessageService.activeCall$.subscribe((call) => {\n      activeCall = call;\n\n      if (!activeCall) {\n        return;\n      }\n\n      this.MessageService.endCall(activeCall.id).subscribe({\n        next: (call) => {\n          // Call ended successfully\n        },\n        error: (error) => {\n          // Error handled silently\n        },\n      });\n    });\n\n    sub.unsubscribe();\n  }\n\n  // Méthodes d'appel ultra-consolidées - Suppression des duplications\n  readonly callMethods = {\n    toggleMute: (): void => {\n      this.isCallMuted = !this.isCallMuted;\n      this.callMethods.updateMedia();\n      this.toastService.showInfo(\n        this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé'\n      );\n    },\n    toggleVideo: (): void => {\n      this.isVideoEnabled = !this.isVideoEnabled;\n      this.callMethods.updateMedia();\n      this.toastService.showInfo(\n        this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'\n      );\n    },\n    toggleMinimize: (): void => {\n      this.isCallMinimized = !this.isCallMinimized;\n    },\n    updateMedia: (): void => {\n      this.MessageService.toggleMedia(\n        this.activeCall?.id,\n        !this.isCallMuted,\n        this.isVideoEnabled\n      ).subscribe({\n        next: () => {},\n        error: () => {},\n      });\n    },\n  };\n\n  // Méthodes publiques pour le template\n  toggleCallMute = this.callMethods.toggleMute;\n  toggleCallVideo = this.callMethods.toggleVideo;\n  private updateCallMedia = this.callMethods.updateMedia;\n\n  // Méthodes de timer ultra-consolidées - Suppression des duplications\n  readonly timerMethods = {\n    startCall: (): void => {\n      this.callDuration = 0;\n      this.callTimer = setInterval(() => {\n        this.callDuration++;\n        if (this.callDuration === 3 && this.callQuality === 'connecting') {\n          this.callQuality = 'excellent';\n        }\n      }, 1000);\n    },\n    stopCall: (): void => {\n      if (this.callTimer) {\n        clearInterval(this.callTimer);\n        this.callTimer = null;\n      }\n    },\n    resetCall: (): void => {\n      this.callDuration = 0;\n    },\n  };\n\n  // Méthodes publiques pour le template\n  private startCallTimerMethod = this.timerMethods.startCall;\n  private stopCallTimerMethod = this.timerMethods.stopCall;\n  private resetCallStateMethod = this.timerMethods.resetCall;\n\n  // Méthode de contrôles d'appel supprimée - Propriétés inexistantes\n\n  // Méthode de configuration vidéo supprimée - Propriétés inexistantes\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion Avancée des Notifications\n  // --------------------------------------------------------------------------\n\n  toggleNotificationPanel(): void {\n    this.togglePanel('notification');\n    if (this.showNotificationPanel) {\n      this.loadNotifications();\n    }\n  }\n\n  /**\n   * Charge les notifications\n   */\n  loadNotifications(refresh: boolean = false): void {\n    const loadSub = this.MessageService.getNotifications(\n      refresh,\n      1,\n      20\n    ).subscribe({\n      next: (notifications) => {\n        if (refresh) {\n          this.notifications = notifications;\n        } else {\n          this.notifications = [...this.notifications, ...notifications];\n        }\n\n        this.updateNotificationCount();\n      },\n      error: (error) => {\n        this.toastService.showError(\n          'Erreur lors du chargement des notifications'\n        );\n      },\n    });\n\n    this.subscriptions.add(loadSub);\n  }\n\n  // Méthodes de notification ultra-consolidées - Suppression des duplications\n  readonly notificationUtilMethods = {\n    loadMore: (): void => {\n      this.loadNotifications();\n    },\n    updateCount: (): void => {\n      this.unreadNotificationCount = this.notifications.filter(\n        (n) => !n.isRead\n      ).length;\n    },\n    getFiltered: (): any[] => {\n      return this.notifications; // Filtre simplifié\n    },\n    setFilter: (filter: 'all' | 'unread' | 'read'): void => {\n      // Filtre simplifié\n    },\n  };\n\n  // Méthodes publiques pour le template\n  loadMoreNotifications = this.notificationUtilMethods.loadMore;\n  private updateNotificationCount = this.notificationUtilMethods.updateCount;\n  getFilteredNotifications = this.notificationUtilMethods.getFiltered;\n\n  /**\n   * Sélectionne/désélectionne une notification\n   */\n  toggleNotificationSelection(notificationId: string): void {\n    if (this.selectedNotifications.has(notificationId)) {\n      this.selectedNotifications.delete(notificationId);\n    } else {\n      this.selectedNotifications.add(notificationId);\n    }\n  }\n\n  /**\n   * Sélectionne/désélectionne toutes les notifications visibles\n   */\n  toggleSelectAllNotifications(): void {\n    const filteredNotifications = this.getFilteredNotifications();\n    const allSelected = filteredNotifications.every((n) =>\n      this.selectedNotifications.has(n.id)\n    );\n\n    if (allSelected) {\n      filteredNotifications.forEach((n) =>\n        this.selectedNotifications.delete(n.id)\n      );\n    } else {\n      filteredNotifications.forEach((n) =>\n        this.selectedNotifications.add(n.id)\n      );\n    }\n  }\n\n  /**\n   * Vérifie si toutes les notifications visibles sont sélectionnées\n   */\n  areAllNotificationsSelected(): boolean {\n    const filteredNotifications = this.getFilteredNotifications();\n    return (\n      filteredNotifications.length > 0 &&\n      filteredNotifications.every((n) => this.selectedNotifications.has(n.id))\n    );\n  }\n\n  /**\n   * Marque les notifications sélectionnées comme lues\n   */\n  markSelectedAsRead(): void {\n    const selectedIds = Array.from(this.selectedNotifications);\n    if (selectedIds.length === 0) {\n      this.toastService.showWarning('Aucune notification sélectionnée');\n      return;\n    }\n\n    // Marquage en cours\n\n    const markSub = this.MessageService.markAsRead(selectedIds).subscribe({\n      next: (result) => {\n        this.notifications = this.notifications.map((n) =>\n          selectedIds.includes(n.id)\n            ? { ...n, isRead: true, readAt: new Date() }\n            : n\n        );\n\n        this.selectedNotifications.clear();\n        this.updateNotificationCount();\n        // Marquage terminé\n\n        this.toastService.showSuccess(\n          `${result.readCount} notification(s) marquée(s) comme lue(s)`\n        );\n      },\n      error: (error) => {\n        // Erreur lors du marquage\n        this.toastService.showError(\n          'Erreur lors du marquage des notifications'\n        );\n      },\n    });\n\n    this.subscriptions.add(markSub);\n  }\n\n  /**\n   * Marque toutes les notifications comme lues\n   */\n  markAllAsRead(): void {\n    const unreadNotifications = this.notifications.filter((n) => !n.isRead);\n    if (unreadNotifications.length === 0) {\n      this.toastService.showInfo('Aucune notification non lue');\n      return;\n    }\n\n    const unreadIds = unreadNotifications.map((n) => n.id);\n    // Marquage en cours\n\n    const markSub = this.MessageService.markAsRead(unreadIds).subscribe({\n      next: (result) => {\n        this.notifications = this.notifications.map((n) =>\n          unreadIds.includes(n.id)\n            ? { ...n, isRead: true, readAt: new Date() }\n            : n\n        );\n\n        this.updateNotificationCount();\n        // Marquage terminé\n\n        this.toastService.showSuccess(\n          'Toutes les notifications ont été marquées comme lues'\n        );\n      },\n      error: (error) => {\n        // Erreur lors du marquage\n        this.toastService.showError(\n          'Erreur lors du marquage des notifications'\n        );\n      },\n    });\n\n    this.subscriptions.add(markSub);\n  }\n\n  /**\n   * Affiche la confirmation de suppression des notifications sélectionnées\n   */\n  showDeleteSelectedConfirmation(): void {\n    if (this.selectedNotifications.size === 0) {\n      this.toastService.showWarning('Aucune notification sélectionnée');\n      return;\n    }\n\n    this.showDeleteConfirmModal = true;\n  }\n\n  /**\n   * Supprime les notifications sélectionnées\n   */\n  deleteSelectedNotifications(): void {\n    const selectedIds = Array.from(this.selectedNotifications);\n    if (selectedIds.length === 0) return;\n\n    this.isDeletingNotifications = true;\n    this.showDeleteConfirmModal = false;\n\n    const deleteSub = this.MessageService.deleteMultipleNotifications(\n      selectedIds\n    ).subscribe({\n      next: (result) => {\n        this.notifications = this.notifications.filter(\n          (n) => !selectedIds.includes(n.id)\n        );\n        this.selectedNotifications.clear();\n        this.updateNotificationCount();\n        this.isDeletingNotifications = false;\n\n        this.toastService.showSuccess(\n          `${result.count} notification(s) supprimée(s)`\n        );\n      },\n      error: (error) => {\n        this.isDeletingNotifications = false;\n        this.toastService.showError(\n          'Erreur lors de la suppression des notifications'\n        );\n      },\n    });\n\n    this.subscriptions.add(deleteSub);\n  }\n\n  /**\n   * Supprime une notification individuelle\n   */\n  deleteNotification(notificationId: string): void {\n    const deleteSub = this.MessageService.deleteNotification(\n      notificationId\n    ).subscribe({\n      next: (result) => {\n        this.notifications = this.notifications.filter(\n          (n) => n.id !== notificationId\n        );\n        this.selectedNotifications.delete(notificationId);\n        this.updateNotificationCount();\n        this.toastService.showSuccess('Notification supprimée');\n      },\n      error: (error) => {\n        this.toastService.showError(\n          'Erreur lors de la suppression de la notification'\n        );\n      },\n    });\n\n    this.subscriptions.add(deleteSub);\n  }\n\n  /**\n   * Supprime toutes les notifications\n   */\n  deleteAllNotifications(): void {\n    if (this.notifications.length === 0) return;\n\n    // Demander confirmation\n    if (\n      !confirm(\n        'Êtes-vous sûr de vouloir supprimer toutes les notifications ? Cette action est irréversible.'\n      )\n    ) {\n      return;\n    }\n\n    this.isDeletingNotifications = true;\n\n    const deleteAllSub = this.MessageService.deleteAllNotifications().subscribe(\n      {\n        next: (result) => {\n          this.notifications = [];\n          this.selectedNotifications.clear();\n          this.updateNotificationCount();\n          this.isDeletingNotifications = false;\n\n          this.toastService.showSuccess(\n            `${result.count} notifications supprimées avec succès`\n          );\n        },\n        error: (error) => {\n          this.isDeletingNotifications = false;\n          this.toastService.showError(\n            'Erreur lors de la suppression de toutes les notifications'\n          );\n        },\n      }\n    );\n\n    this.subscriptions.add(deleteAllSub);\n  }\n\n  /**\n   * Annule la suppression des notifications\n   */\n  cancelDeleteNotifications(): void {\n    this.showDeleteConfirmModal = false;\n  }\n\n  // Méthodes utilitaires ultra-consolidées pour les notifications\n  private notificationConfig = {\n    NEW_MESSAGE: { icon: 'fas fa-comment', color: 'text-blue-500' },\n    FRIEND_REQUEST: { icon: 'fas fa-user-plus', color: 'text-green-500' },\n    GROUP_INVITATION: { icon: 'fas fa-users', color: 'text-purple-500' },\n    CALL_MISSED: { icon: 'fas fa-phone-slash', color: 'text-red-500' },\n    CALL_INCOMING: { icon: 'fas fa-phone', color: 'text-yellow-500' },\n    SYSTEM: { icon: 'fas fa-cog', color: 'text-gray-500' },\n  };\n\n  formatNotificationDate = (timestamp: string | Date): string =>\n    this.MessageService.formatLastActive(timestamp);\n\n  getNotificationIcon = (type: string): string =>\n    this.notificationConfig[type as keyof typeof this.notificationConfig]\n      ?.icon || 'fas fa-bell';\n\n  getNotificationColor = (type: string): string =>\n    this.notificationConfig[type as keyof typeof this.notificationConfig]\n      ?.color || 'text-cyan-500';\n\n  trackByNotificationId = (index: number, notification: any): string =>\n    notification.id;\n\n  // --------------------------------------------------------------------------\n  // Section: Statut Utilisateur en Temps Réel\n  // --------------------------------------------------------------------------\n\n  /**\n   * S'abonne au statut utilisateur en temps réel\n   */\n  private subscribeToUserStatus(): void {\n    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({\n      next: (user: User) => {\n        this.handleUserStatusUpdate(user);\n      },\n      error: (error) => {\n        // Error handled silently\n      },\n    });\n\n    this.subscriptions.add(statusSub);\n  }\n\n  /**\n   * Gère la mise à jour du statut d'un utilisateur\n   */\n  private handleUserStatusUpdate(user: User): void {\n    if (!user.id) return;\n\n    if (user.isOnline) {\n      this.onlineUsers.set(user.id, user);\n    } else {\n      this.onlineUsers.delete(user.id);\n    }\n\n    if (this.otherParticipant && this.otherParticipant.id === user.id) {\n      this.otherParticipant = { ...this.otherParticipant, ...user };\n    }\n  }\n\n  /**\n   * Initialise le statut de l'utilisateur actuel\n   */\n  private initializeUserStatus(): void {\n    if (!this.currentUserId) return;\n\n    // Définir l'utilisateur comme en ligne\n    const setOnlineSub = this.MessageService.setUserOnline(\n      this.currentUserId\n    ).subscribe({\n      next: (user: User) => {\n        this.currentUserStatus = 'online';\n        this.lastActivityTime = new Date();\n      },\n      error: (error) => {\n        this.logger.error(\n          'MessageChat',\n          \"Erreur lors de l'initialisation du statut\",\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(setOnlineSub);\n  }\n\n  /**\n   * Démarre le suivi d'activité automatique\n   */\n  private startActivityTracking(): void {\n    // Écouter les événements d'activité\n    const events = [\n      'mousedown',\n      'mousemove',\n      'keypress',\n      'scroll',\n      'touchstart',\n      'click',\n    ];\n\n    events.forEach((event) => {\n      document.addEventListener(event, this.onUserActivity.bind(this), true);\n    });\n  }\n\n  /**\n   * Gère l'activité de l'utilisateur\n   */\n  private onUserActivity(): void {\n    this.lastActivityTime = new Date();\n\n    // Réinitialiser le timer d'absence automatique\n    if (this.autoAwayTimeout) {\n      clearTimeout(this.autoAwayTimeout);\n    }\n\n    // Si l'utilisateur était absent, le remettre en ligne\n    if (\n      this.currentUserStatus === 'away' ||\n      this.currentUserStatus === 'offline'\n    ) {\n      this.updateUserStatus('online');\n    }\n\n    // Programmer la mise en absence automatique\n    this.autoAwayTimeout = setTimeout(() => {\n      if (this.currentUserStatus === 'online') {\n        this.updateUserStatus('away');\n      }\n    }, this.autoAwayDelay);\n  }\n\n  private checkAndUpdateStatus(): void {\n    const now = new Date();\n    const timeSinceLastActivity =\n      now.getTime() - this.lastActivityTime.getTime();\n\n    if (\n      timeSinceLastActivity > this.autoAwayDelay &&\n      this.currentUserStatus === 'online'\n    ) {\n      this.updateUserStatus('away');\n    } else if (\n      timeSinceLastActivity > 1800000 &&\n      this.currentUserStatus === 'away'\n    ) {\n      this.updateUserStatus('offline');\n    }\n  }\n\n  /**\n   * Met à jour le statut de l'utilisateur\n   */\n  updateUserStatus(status: string): void {\n    if (!this.currentUserId) return;\n\n    const previousStatus = this.currentUserStatus;\n\n    let updateObservable;\n    if (status === 'online') {\n      updateObservable = this.MessageService.setUserOnline(this.currentUserId);\n    } else {\n      updateObservable = this.MessageService.setUserOffline(this.currentUserId);\n    }\n\n    const updateSub = updateObservable.subscribe({\n      next: (user: User) => {\n        this.currentUserStatus = status;\n\n        if (status !== previousStatus) {\n          const statusText = this.getStatusText(status);\n          this.toastService.showInfo(`Statut : ${statusText}`);\n        }\n      },\n      error: (error) => {\n        this.logger.error(\n          'MessageChat',\n          'Erreur lors de la mise à jour du statut',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(updateSub);\n  }\n\n  /**\n   * Charge la liste des utilisateurs en ligne\n   */\n  loadOnlineUsers(): void {\n    const usersSub = this.MessageService.getAllUsers(\n      false,\n      undefined,\n      1,\n      50,\n      'username',\n      'asc',\n      true\n    ).subscribe({\n      next: (users: User[]) => {\n        users.forEach((user) => {\n          if (user.isOnline && user.id) {\n            this.onlineUsers.set(user.id, user);\n          }\n        });\n      },\n      error: (error) => {\n        this.logger.error(\n          'MessageChat',\n          'Erreur lors du chargement des utilisateurs en ligne',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.add(usersSub);\n  }\n\n  // Configuration ultra-consolidée des statuts\n  private statusConfig = {\n    online: {\n      text: 'En ligne',\n      color: 'text-green-500',\n      icon: 'fas fa-circle',\n      label: 'En ligne',\n      description: 'Disponible pour discuter',\n    },\n    offline: {\n      text: 'Hors ligne',\n      color: 'text-gray-500',\n      icon: 'far fa-circle',\n      label: 'Hors ligne',\n      description: 'Invisible pour tous',\n    },\n    away: {\n      text: 'Absent',\n      color: 'text-yellow-500',\n      icon: 'fas fa-clock',\n      label: 'Absent',\n      description: 'Absent temporairement',\n    },\n    busy: {\n      text: 'Occupé',\n      color: 'text-red-500',\n      icon: 'fas fa-minus-circle',\n      label: 'Occupé',\n      description: 'Ne pas déranger',\n    },\n  };\n\n  // Méthode pour obtenir les options de statut pour le template\n  getStatusOptions() {\n    return Object.entries(this.statusConfig).map(([key, config]) => ({\n      key,\n      ...config,\n    }));\n  }\n\n  // Configuration des thèmes consolidée\n  private themeConfig = [\n    {\n      key: 'theme-default',\n      label: 'Par défaut',\n      color: '[#4f5fad]',\n      hoverColor: '[#4f5fad]',\n    },\n    {\n      key: 'theme-feminine',\n      label: 'Rose',\n      color: '[#ff6b9d]',\n      hoverColor: '[#ff6b9d]',\n    },\n    {\n      key: 'theme-masculine',\n      label: 'Bleu',\n      color: '[#3d85c6]',\n      hoverColor: '[#3d85c6]',\n    },\n    {\n      key: 'theme-neutral',\n      label: 'Vert',\n      color: '[#6aa84f]',\n      hoverColor: '[#6aa84f]',\n    },\n  ];\n\n  // Méthode pour obtenir les options de thème pour le template\n  getThemeOptions() {\n    return this.themeConfig;\n  }\n\n  // Configuration des panneaux consolidée\n  private panelConfig = [\n    {\n      key: 'userStatus',\n      show: () => this.showUserStatusPanel,\n      title: 'Utilisateurs',\n      icon: 'fas fa-users',\n      closeAction: () => (this.showUserStatusPanel = false),\n    },\n    {\n      key: 'callHistory',\n      show: () => this.showCallHistoryPanel,\n      title: 'Historique des appels',\n      icon: 'fas fa-history',\n      closeAction: () => (this.showCallHistoryPanel = false),\n    },\n    {\n      key: 'callStats',\n      show: () => this.showCallStatsPanel,\n      title: \"Statistiques d'appels\",\n      icon: 'fas fa-chart-bar',\n      closeAction: () => (this.showCallStatsPanel = false),\n    },\n    {\n      key: 'voiceMessages',\n      show: () => this.showVoiceMessagesPanel,\n      title: 'Messages vocaux',\n      icon: 'fas fa-microphone',\n      closeAction: () => (this.showVoiceMessagesPanel = false),\n    },\n  ];\n\n  // Méthode pour obtenir les panneaux actifs\n  getActivePanels() {\n    return this.panelConfig.filter((panel) => panel.show());\n  }\n\n  // Méthodes de statut ultra-consolidées\n  readonly statusMethods = {\n    getText: (status: string): string =>\n      this.statusConfig[status as keyof typeof this.statusConfig]?.text ||\n      'Inconnu',\n    getColor: (status: string): string =>\n      this.statusConfig[status as keyof typeof this.statusConfig]?.color ||\n      'text-gray-400',\n    getIcon: (status: string): string =>\n      this.statusConfig[status as keyof typeof this.statusConfig]?.icon ||\n      'fas fa-question-circle',\n    formatLastSeen: (lastActive: Date | null): string => {\n      if (!lastActive) return 'Jamais vu';\n      return this.MessageService.formatLastActive(lastActive);\n    },\n    getOnlineCount: (): number =>\n      Array.from(this.onlineUsers.values()).filter((user) => user.isOnline)\n        .length,\n    trackById: (index: number, user: User): string =>\n      user.id || index.toString(),\n  };\n\n  // Méthodes publiques pour le template\n  getStatusText = this.statusMethods.getText;\n  getStatusColor = this.statusMethods.getColor;\n  getStatusIcon = this.statusMethods.getIcon;\n  formatLastSeen = this.statusMethods.formatLastSeen;\n  getOnlineUsersCount = this.statusMethods.getOnlineCount;\n  trackByUserId = this.statusMethods.trackById;\n\n  // Méthodes consolidées pour les templates\n  setStatusFilter(filter: string): void {\n    this.statusFilterType = filter;\n  }\n\n  getFilteredUsers(): User[] {\n    return Array.from(this.onlineUsers.values());\n  }\n\n  // Méthodes vides consolidées - Supprimées car non utilisées\n\n  // Gestion des réponses et transferts\n  startReplyToMessage(message: any): void {\n    this.replyingToMessage = message;\n  }\n\n  cancelReply(): void {\n    this.replyingToMessage = null;\n  }\n\n  openForwardModal(message: any): void {\n    this.forwardingMessage = message;\n    this.showForwardModal = true;\n  }\n\n  closeForwardModal(): void {\n    this.showForwardModal = false;\n    this.forwardingMessage = null;\n    this.selectedConversations = [];\n  }\n\n  // Méthodes de message ultra-consolidées\n  readonly messageMethods = {\n    getPinIcon: (message: any): string =>\n      this.messageMethods.isPinned(message)\n        ? 'fas fa-thumbtack'\n        : 'far fa-thumbtack',\n    getPinDisplayText: (message: any): string =>\n      this.messageMethods.isPinned(message) ? 'Désépingler' : 'Épingler',\n    canEdit: (message: any): boolean =>\n      message.sender?.id === this.currentUserId,\n    isPinned: (message: any): boolean => message.isPinned || false,\n    startEdit: (message: any): void => {\n      this.editingMessageId = message.id;\n      this.editingContent = message.content;\n    },\n    cancelEdit: (): void => {\n      this.editingMessageId = null;\n      this.editingContent = '';\n    },\n    saveEdit: (messageId: string): void => this.messageMethods.cancelEdit(),\n    onEditKeyPress: (event: KeyboardEvent, messageId: string): void => {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.messageMethods.saveEdit(messageId);\n      } else if (event.key === 'Escape') {\n        this.messageMethods.cancelEdit();\n      }\n    },\n    togglePin: (message: any): void => {\n      this.isPinning[message.id] = true;\n      setTimeout(() => {\n        this.isPinning[message.id] = false;\n        this.showPinConfirm[message.id] = false;\n      }, 1000);\n    },\n  };\n\n  // Méthodes publiques pour le template\n  getPinIcon = this.messageMethods.getPinIcon;\n  getPinDisplayText = this.messageMethods.getPinDisplayText;\n  canEditMessage = this.messageMethods.canEdit;\n  isMessagePinned = this.messageMethods.isPinned;\n  startEditMessage = this.messageMethods.startEdit;\n  cancelEditMessage = this.messageMethods.cancelEdit;\n  saveEditMessage = this.messageMethods.saveEdit;\n  onEditKeyPress = this.messageMethods.onEditKeyPress;\n  togglePinMessage = this.messageMethods.togglePin;\n\n  // Méthodes utilitaires pour les appels - Consolidées\n  readonly callUtils = {\n    getStatusColor: (status: string): string => {\n      const colors = {\n        COMPLETED: 'text-green-500',\n        MISSED: 'text-red-500',\n        REJECTED: 'text-orange-500',\n      };\n      return colors[status as keyof typeof colors] || 'text-gray-500';\n    },\n    getTypeIcon: (type: string): string =>\n      type === 'VIDEO' ? 'fas fa-video' : 'fas fa-phone',\n    formatDuration: (duration: number): string => {\n      if (!duration) return '00:00';\n      const minutes = Math.floor(duration / 60);\n      const seconds = duration % 60;\n      return `${minutes.toString().padStart(2, '0')}:${seconds\n        .toString()\n        .padStart(2, '0')}`;\n    },\n    formatDate: (timestamp: string | Date): string =>\n      this.MessageService.formatMessageDate(timestamp),\n    trackById: (index: number, call: any): string =>\n      call.id || index.toString(),\n  };\n\n  // Méthodes publiques pour le template\n  getCallStatusColor = this.callUtils.getStatusColor;\n  getCallTypeIcon = this.callUtils.getTypeIcon;\n  formatCallDuration = this.callUtils.formatDuration;\n  formatCallDate = this.callUtils.formatDate;\n  trackByCallId = this.callUtils.trackById;\n\n  onDocumentClick(event: Event): void {\n    const target = event.target as HTMLElement;\n\n    if (\n      !target.closest('.theme-selector-menu') &&\n      !target.closest('.btn-theme')\n    ) {\n      this.showThemeSelector = false;\n    }\n\n    if (!target.closest('.emoji-picker') && !target.closest('.btn-emoji')) {\n      this.showEmojiPicker = false;\n    }\n  }\n  // Méthodes utilitaires ultra-consolidées - Optimisées\n  readonly utilityMethods = {\n    getUniqueReactions: (message: any): any[] => message.reactions || [],\n    onReactionClick: (messageId: string, emoji: string): void => {},\n    hasUserReacted: (message: any, emoji: string): boolean => false,\n    areAllConversationsSelected: (): boolean =>\n      this.selectedConversations.length === this.availableConversations.length,\n    selectAllConversations: (): void => {\n      this.selectedConversations = this.availableConversations.map((c) => c.id);\n    },\n    deselectAllConversations: (): void => {\n      this.selectedConversations = [];\n    },\n    // formatMessageTime et formatMessageDate supprimés - Déjà définis dans serviceMethods\n    trackByMessageId: (index: number, message: any): string =>\n      message.id || index.toString(),\n  };\n\n  // Méthodes publiques pour le template - Utilitaires consolidées\n  getUniqueReactions = this.utilityMethods.getUniqueReactions;\n  onReactionClick = this.utilityMethods.onReactionClick;\n  hasUserReacted = this.utilityMethods.hasUserReacted;\n  areAllConversationsSelected = this.utilityMethods.areAllConversationsSelected;\n  selectAllConversations = this.utilityMethods.selectAllConversations;\n  deselectAllConversations = this.utilityMethods.deselectAllConversations;\n  // formatMessageTime et formatMessageDate déjà définis dans serviceMethods (ligne 868-870)\n  trackByMessageId = this.utilityMethods.trackByMessageId;\n\n  // Méthodes de conversation ultra-consolidées\n  readonly conversationUtilMethods = {\n    toggleSelection: (conversationId: string): void => {\n      const index = this.selectedConversations.indexOf(conversationId);\n      index > -1\n        ? this.selectedConversations.splice(index, 1)\n        : this.selectedConversations.push(conversationId);\n    },\n    isSelected: (conversationId: string): boolean =>\n      this.selectedConversations.includes(conversationId),\n    getDisplayImage: (conversation: any): string =>\n      conversation.image || 'assets/images/default-avatar.png',\n    getDisplayName: (conversation: any): string =>\n      conversation.name || 'Conversation',\n    forward: (): void => {\n      this.isForwarding = true;\n      setTimeout(() => {\n        this.isForwarding = false;\n        this.closeForwardModal();\n      }, 1000);\n    },\n  };\n\n  // Méthodes publiques pour le template\n  toggleConversationSelection = this.conversationUtilMethods.toggleSelection;\n  isConversationSelected = this.conversationUtilMethods.isSelected;\n  getConversationDisplayImage = this.conversationUtilMethods.getDisplayImage;\n  getConversationDisplayName = this.conversationUtilMethods.getDisplayName;\n  forwardMessage = this.conversationUtilMethods.forward;\n\n  // toggleConfig supprimé - Fusionné avec toggleMethods (ligne 597)\n\n  // Méthodes de notification ultra-consolidées - Suppression des duplications\n  readonly notificationMethods = {\n    onCallMouseMove: (): void => {\n      this.showCallControls = true;\n    },\n    saveSettings: (): void => {\n      // Méthode pour sauvegarder les paramètres de notification\n    },\n    setFilter: (filter: string): void => {\n      this.notificationFilter = filter;\n    },\n    cancelDelete: (): void => {\n      this.showDeleteConfirmModal = false;\n    },\n  };\n\n  // Méthodes publiques pour le template\n  onCallMouseMove = this.notificationMethods.onCallMouseMove;\n  saveNotificationSettings = this.notificationMethods.saveSettings;\n  setNotificationFilter = this.notificationMethods.setFilter;\n  // cancelDeleteNotifications déjà définie ligne 1772\n\n  // Sections supprimées - Toutes les méthodes sont maintenant consolidées dans les objets appropriés\n\n  // Méthodes de recherche ultra-consolidées\n  readonly searchMethods = {\n    onInput: (event: any): void => {\n      this.searchQuery = event.target.value;\n      this.searchQuery.length >= 2\n        ? this.searchMethods.perform()\n        : this.searchMethods.clear();\n    },\n    onKeyPress: (event: KeyboardEvent): void => {\n      event.key === 'Enter'\n        ? this.searchMethods.perform()\n        : event.key === 'Escape'\n        ? this.searchMethods.clear()\n        : null;\n    },\n    perform: (): void => {\n      this.isSearching = true;\n      this.searchMode = true;\n      setTimeout(() => {\n        this.searchResults = this.messages.filter((m) =>\n          m.content?.toLowerCase().includes(this.searchQuery.toLowerCase())\n        );\n        this.isSearching = false;\n      }, 500);\n    },\n    clear: (): void => {\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.isSearching = false;\n      this.searchMode = false;\n    },\n  };\n\n  // Méthodes publiques pour le template\n  onSearchInput = this.searchMethods.onInput;\n  onSearchKeyPress = this.searchMethods.onKeyPress;\n  performSearch = this.searchMethods.perform;\n  clearSearch = this.searchMethods.clear;\n\n  // Méthode clearSearch supprimée - Déjà définie dans searchMethods\n\n  // Méthodes de confirmation ultra-consolidées\n  private readonly confirmationMethods = {\n    showPin: (messageId: string): void => {\n      this.showPinConfirm[messageId] = true;\n    },\n    cancelPin: (messageId: string): void => {\n      this.showPinConfirm[messageId] = false;\n    },\n    showDelete: (messageId: string): void => {\n      this.showDeleteConfirm[messageId] = true;\n    },\n    cancelDelete: (messageId: string): void => {\n      this.showDeleteConfirm[messageId] = false;\n    },\n    confirmDelete: (messageId: string): void => {\n      this.showDeleteConfirm[messageId] = false;\n    },\n  };\n\n  // Méthodes publiques pour le template\n  showPinConfirmation = this.confirmationMethods.showPin;\n  cancelPinConfirmation = this.confirmationMethods.cancelPin;\n  showDeleteConfirmation = this.confirmationMethods.showDelete;\n  cancelDeleteMessage = this.confirmationMethods.cancelDelete;\n  confirmDeleteMessage = this.confirmationMethods.confirmDelete;\n\n  // Méthodes utilitaires finales ultra-consolidées\n  readonly finalUtilityMethods = {\n    navigateToMessage: (messageId: string): void => {},\n    scrollToPinnedMessage: (messageId: string): void => {},\n    getPinnedMessagesCount: (): number => this.pinnedMessages.length,\n    highlightSearchTerms: (content: string, query: string): string => {\n      if (!query) return content;\n      const regex = new RegExp(`(${query})`, 'gi');\n      return content.replace(regex, '<mark>$1</mark>');\n    },\n  };\n\n  // Méthodes publiques pour le template\n  navigateToMessage = this.finalUtilityMethods.navigateToMessage;\n  scrollToPinnedMessage = this.finalUtilityMethods.scrollToPinnedMessage;\n  getPinnedMessagesCount = this.finalUtilityMethods.getPinnedMessagesCount;\n  highlightSearchTerms = this.finalUtilityMethods.highlightSearchTerms;\n\n  // Gestion des réactions et options ultra-consolidée\n  readonly reactionMethods = {\n    togglePicker: (messageId: string): void => {\n      this.showReactionPicker[messageId] = !this.showReactionPicker[messageId];\n    },\n    reactTo: (messageId: string, emoji: string): void => {\n      this.showReactionPicker[messageId] = false;\n    },\n    toggleOptions: (messageId: string): void => {\n      this.showMessageOptions[messageId] = !this.showMessageOptions[messageId];\n    },\n  };\n\n  // Méthodes publiques pour le template\n  toggleReactionPicker = this.reactionMethods.togglePicker;\n  reactToMessage = this.reactionMethods.reactTo;\n  toggleMessageOptions = this.reactionMethods.toggleOptions;\n\n  ngOnDestroy(): void {\n    this.stopTypingIndicator();\n\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n\n    if (this.autoAwayTimeout) {\n      clearTimeout(this.autoAwayTimeout);\n    }\n\n    if (this.currentUserId) {\n      this.MessageService.setUserOffline(this.currentUserId).subscribe();\n    }\n\n    this.subscriptions.unsubscribe();\n  }\n}\n", "<div\n  class=\"flex flex-col h-full bg-[#edf1f4] dark:bg-[#121212] relative overflow-hidden futuristic-chat-container dark\"\n  [ngClass]=\"selectedTheme\"\n>\n  <!-- Background decorative elements optimized -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <!-- Simplified grid pattern -->\n    <div\n      class=\"absolute inset-0 opacity-5 dark:opacity-[0.03] bg-grid-pattern\"\n    ></div>\n    <!-- Scan line effect for dark mode -->\n    <div class=\"absolute inset-0 opacity-0 dark:opacity-100 overflow-hidden\">\n      <div class=\"h-px w-full bg-[#00f7ff]/20 absolute animate-scan\"></div>\n    </div>\n  </div>\n\n  <!-- En-tête style WhatsApp -->\n  <div class=\"whatsapp-chat-header\">\n    <button (click)=\"goBackToConversations()\" class=\"whatsapp-action-button\">\n      <i class=\"fas fa-arrow-left\"></i>\n    </button>\n\n    <div class=\"whatsapp-user-info\">\n      <div class=\"whatsapp-avatar\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          alt=\"User avatar\"\n        />\n        <span\n          *ngIf=\"otherParticipant?.isOnline\"\n          class=\"whatsapp-online-indicator\"\n        ></span>\n      </div>\n\n      <!-- Nom et statut style WhatsApp -->\n      <div *ngIf=\"otherParticipant\" class=\"whatsapp-user-details\">\n        <span class=\"whatsapp-username\">\n          {{ otherParticipant.username }}\n        </span>\n        <span class=\"whatsapp-status\">\n          {{\n            otherParticipant.isOnline\n              ? \"En ligne\"\n              : formatLastActive(otherParticipant.lastActive)\n          }}\n        </span>\n      </div>\n    </div>\n\n    <div class=\"whatsapp-actions\">\n      <!-- Boutons d'action consolidés -->\n      <ng-container *ngFor=\"let action of getHeaderActions()\">\n        <button\n          [class]=\"'whatsapp-action-button ' + action.class\"\n          [ngClass]=\"\n            action.activeClass && action.isActive ? action.activeClass : {}\n          \"\n          [title]=\"action.title\"\n          (click)=\"action.onClick()\"\n        >\n          <i [class]=\"action.icon\"></i>\n          <!-- Badge universel -->\n          <span\n            *ngIf=\"action.badge && action.badge.count > 0\"\n            [class]=\"\n              'absolute -top-2 -right-2 min-w-[20px] h-5 px-1.5 text-white text-xs rounded-md flex items-center justify-center font-bold shadow-lg border border-white/20 ' +\n              action.badge.class\n            \"\n            [ngClass]=\"{ 'animate-pulse': action.badge.animate }\"\n          >\n            {{ action.badge.count > 99 ? \"99+\" : action.badge.count }}\n          </span>\n        </button>\n      </ng-container>\n\n      <!-- Bouton du statut utilisateur -->\n      <div class=\"relative\">\n        <button\n          (click)=\"toggleStatusSelector()\"\n          class=\"whatsapp-action-button relative\"\n          [ngClass]=\"{\n            'text-[#4f5fad] dark:text-[#6d78c9]': showStatusSelector\n          }\"\n          title=\"Statut utilisateur\"\n        >\n          <i\n            [class]=\"getStatusIcon(currentUserStatus)\"\n            [ngClass]=\"getStatusColor(currentUserStatus)\"\n          ></i>\n          <!-- Indicateur de mise à jour -->\n          <span\n            *ngIf=\"isUpdatingStatus\"\n            class=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"\n          ></span>\n        </button>\n\n        <!-- Menu déroulant du statut -->\n        <div\n          *ngIf=\"showStatusSelector\"\n          class=\"absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\"\n        >\n          <div\n            class=\"p-3 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] bg-gradient-to-r from-[#4f5fad]/10 to-[#6d78c9]/10\"\n          >\n            <div class=\"flex items-center justify-between\">\n              <span>Statut actuel</span>\n              <span\n                class=\"font-medium\"\n                [ngClass]=\"getStatusColor(currentUserStatus)\"\n              >\n                {{ getStatusText(currentUserStatus) }}\n              </span>\n            </div>\n          </div>\n\n          <div class=\"p-1\">\n            <!-- Boutons de statut consolidés avec *ngFor -->\n            <button\n              *ngFor=\"let status of getStatusOptions()\"\n              (click)=\"updateUserStatus(status.key); toggleStatusSelector()\"\n              [disabled]=\"isUpdatingStatus\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n              [ngClass]=\"{\n                'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20':\n                  currentUserStatus === status.key\n              }\"\n            >\n              <div class=\"flex items-center\">\n                <i\n                  [class]=\"status.icon + ' ' + status.color + ' mr-3 text-xs'\"\n                ></i>\n                <div>\n                  <div class=\"font-medium\">{{ status.label }}</div>\n                  <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n                    {{ status.description }}\n                  </div>\n                </div>\n              </div>\n            </button>\n          </div>\n\n          <div class=\"border-t border-[#edf1f4]/50 dark:border-[#2a2a2a] p-1\">\n            <button\n              (click)=\"toggleUserStatusPanel(); toggleStatusSelector()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i\n                  class=\"fas fa-users text-[#4f5fad] dark:text-[#6d78c9] mr-3 text-xs\"\n                ></i>\n                <div>\n                  <div class=\"font-medium\">Voir tous les utilisateurs</div>\n                  <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n                    {{ getOnlineUsersCount() }} en ligne\n                  </div>\n                </div>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Sélecteur de thème -->\n      <div class=\"relative\">\n        <button\n          (click)=\"toggleThemeSelector()\"\n          class=\"whatsapp-action-button btn-theme\"\n        >\n          <i class=\"fas fa-palette\"></i>\n        </button>\n\n        <!-- Menu déroulant des thèmes -->\n        <div\n          *ngIf=\"showThemeSelector\"\n          class=\"theme-selector-menu absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\"\n        >\n          <div\n            class=\"p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n          >\n            Choisir un thème\n          </div>\n          <div class=\"p-1\">\n            <!-- Boutons de thème consolidés avec *ngFor -->\n            <a\n              *ngFor=\"let theme of getThemeOptions()\"\n              href=\"javascript:void(0)\"\n              (click)=\"changeTheme(theme.key)\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md transition-colors\"\n              [ngClass]=\"\n                'hover:bg-' +\n                theme.hoverColor +\n                '/10 dark:hover:bg-' +\n                theme.hoverColor +\n                '/10'\n              \"\n            >\n              <div class=\"flex items-center\">\n                <div\n                  [ngClass]=\"'w-4 h-4 rounded-full bg-' + theme.color + ' mr-2'\"\n                ></div>\n                <div>{{ theme.label }}</div>\n              </div>\n            </a>\n          </div>\n        </div>\n      </div>\n\n      <!-- Bouton menu principal -->\n      <div class=\"relative\">\n        <button\n          (click)=\"toggleMainMenu()\"\n          class=\"whatsapp-action-button btn-menu\"\n          title=\"Menu principal\"\n        >\n          <i class=\"fas fa-ellipsis-v\"></i>\n        </button>\n\n        <!-- Menu déroulant principal -->\n        <div\n          *ngIf=\"showMainMenu\"\n          class=\"absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\"\n        >\n          <div\n            class=\"p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n          >\n            Options de conversation\n          </div>\n          <div class=\"p-1\">\n            <button\n              (click)=\"clearConversation()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-red-500/10 dark:hover:bg-red-500/10 transition-colors text-red-600 dark:text-red-400\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-trash mr-3 text-xs\"></i>\n                <div>Vider la conversation</div>\n              </div>\n            </button>\n            <button\n              (click)=\"exportConversation()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-download mr-3 text-xs\"></i>\n                <div>Exporter la conversation</div>\n              </div>\n            </button>\n            <button\n              (click)=\"toggleConversationInfo()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-info-circle mr-3 text-xs\"></i>\n                <div>Informations</div>\n              </div>\n            </button>\n            <button\n              (click)=\"toggleConversationSettings()\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <i class=\"fas fa-cog mr-3 text-xs\"></i>\n                <div>Paramètres</div>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Barre de recherche -->\n  <div\n    *ngIf=\"showSearchBar\"\n    class=\"search-bar bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] px-4 py-3 relative z-10\"\n  >\n    <div class=\"flex items-center space-x-3\">\n      <!-- Icône de recherche -->\n      <div class=\"text-[#6d6870] dark:text-[#a0a0a0]\">\n        <i class=\"fas fa-search text-sm\"></i>\n      </div>\n\n      <!-- Champ de recherche -->\n      <div class=\"flex-1 relative\">\n        <input\n          type=\"text\"\n          [(ngModel)]=\"searchQuery\"\n          (input)=\"onSearchInput($event)\"\n          (keydown)=\"onSearchKeyPress($event)\"\n          placeholder=\"Rechercher dans cette conversation...\"\n          class=\"w-full px-3 py-2 text-sm bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 border border-[#edf1f4] dark:border-[#3a3a3a] rounded-lg focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 transition-colors\"\n          autofocus\n        />\n\n        <!-- Indicateur de chargement -->\n        <div\n          *ngIf=\"isSearching\"\n          class=\"absolute right-3 top-1/2 transform -translate-y-1/2\"\n        >\n          <div\n            class=\"w-4 h-4 border-2 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\n          ></div>\n        </div>\n\n        <!-- Bouton de suppression -->\n        <button\n          *ngIf=\"searchQuery && !isSearching\"\n          (click)=\"clearSearch()\"\n          class=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\n        >\n          <i class=\"fas fa-times text-xs\"></i>\n        </button>\n      </div>\n\n      <!-- Bouton de fermeture -->\n      <button\n        (click)=\"toggleSearchBar()\"\n        class=\"text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n\n    <!-- Résultats de recherche -->\n    <div\n      *ngIf=\"searchMode && searchResults.length > 0\"\n      class=\"mt-3 max-h-40 overflow-y-auto border border-[#edf1f4]/50 dark:border-[#3a3a3a] rounded-lg bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30\"\n    >\n      <div\n        class=\"p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#3a3a3a]\"\n      >\n        {{ searchResults.length }} résultat(s) trouvé(s)\n      </div>\n      <div class=\"max-h-32 overflow-y-auto\">\n        <button\n          *ngFor=\"let result of searchResults\"\n          (click)=\"result.id && navigateToMessage(result.id)\"\n          class=\"w-full text-left px-3 py-2 hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors border-b border-[#edf1f4]/30 dark:border-[#3a3a3a]/30 last:border-b-0\"\n        >\n          <div class=\"flex items-start space-x-2\">\n            <div\n              class=\"w-6 h-6 rounded-full bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 flex items-center justify-center flex-shrink-0 mt-0.5\"\n            >\n              <i\n                class=\"fas fa-comment text-xs text-[#4f5fad] dark:text-[#6d78c9]\"\n              ></i>\n            </div>\n            <div class=\"flex-1 min-w-0\">\n              <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mb-1\">\n                {{ formatMessageTime(result.timestamp) }}\n              </div>\n              <div\n                class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] truncate\"\n                [innerHTML]=\"\n                  highlightSearchTerms(result.content || '', searchQuery)\n                \"\n              ></div>\n            </div>\n          </div>\n        </button>\n      </div>\n    </div>\n\n    <!-- Message aucun résultat -->\n    <div\n      *ngIf=\"\n        searchMode &&\n        searchResults.length === 0 &&\n        !isSearching &&\n        searchQuery.length >= 2\n      \"\n      class=\"mt-3 p-3 text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30 rounded-lg\"\n    >\n      <i\n        class=\"fas fa-search text-lg mb-2 block text-[#6d6870]/50 dark:text-[#a0a0a0]/50\"\n      ></i>\n      Aucun message trouvé pour \"{{ searchQuery }}\"\n    </div>\n  </div>\n\n  <!-- Panneau des messages épinglés -->\n  <div\n    *ngIf=\"showPinnedMessages\"\n    class=\"pinned-messages-panel bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] relative z-10\"\n  >\n    <!-- En-tête du panneau -->\n    <div\n      class=\"flex items-center justify-between p-4 border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n    >\n      <div class=\"flex items-center space-x-2\">\n        <i class=\"fas fa-thumbtack text-[#4f5fad] dark:text-[#6d78c9]\"></i>\n        <h3 class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">\n          Messages épinglés ({{ getPinnedMessagesCount() }})\n        </h3>\n      </div>\n      <button\n        (click)=\"togglePinnedMessages()\"\n        class=\"w-6 h-6 flex items-center justify-center text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-full transition-colors\"\n      >\n        <i class=\"fas fa-times text-xs\"></i>\n      </button>\n    </div>\n\n    <!-- Liste des messages épinglés -->\n    <div class=\"max-h-48 overflow-y-auto\">\n      <!-- État de chargement -->\n      <div *ngIf=\"pinnedMessages.length === 0\" class=\"p-4 text-center\">\n        <div class=\"text-[#6d6870]/70 dark:text-[#a0a0a0]/70\">\n          <i class=\"fas fa-thumbtack text-2xl mb-2 block opacity-50\"></i>\n          <div class=\"text-sm\">Aucun message épinglé</div>\n        </div>\n      </div>\n\n      <!-- Messages épinglés -->\n      <div\n        *ngIf=\"pinnedMessages.length > 0\"\n        class=\"divide-y divide-[#edf1f4]/30 dark:divide-[#3a3a3a]/30\"\n      >\n        <button\n          *ngFor=\"let pinnedMessage of pinnedMessages\"\n          (click)=\"scrollToPinnedMessage(pinnedMessage.id!)\"\n          class=\"w-full text-left p-3 hover:bg-[#4f5fad]/5 dark:hover:bg-[#6d78c9]/5 transition-colors\"\n        >\n          <div class=\"flex items-start space-x-3\">\n            <!-- Avatar de l'expéditeur -->\n            <div class=\"flex-shrink-0\">\n              <img\n                [src]=\"\n                  pinnedMessage.sender?.image ||\n                  'assets/images/default-avatar.png'\n                \"\n                [alt]=\"pinnedMessage.sender?.username || 'User'\"\n                class=\"w-8 h-8 rounded-full object-cover\"\n                onerror=\"this.src='assets/images/default-avatar.png'\"\n              />\n            </div>\n\n            <!-- Contenu du message -->\n            <div class=\"flex-1 min-w-0\">\n              <!-- En-tête avec nom et date -->\n              <div class=\"flex items-center justify-between mb-1\">\n                <span\n                  class=\"text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9]\"\n                >\n                  {{ pinnedMessage.sender?.username || \"Utilisateur inconnu\" }}\n                </span>\n                <span class=\"text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70\">\n                  {{ formatMessageTime(pinnedMessage.timestamp) }}\n                </span>\n              </div>\n\n              <!-- Contenu du message -->\n              <div\n                class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] line-clamp-2\"\n              >\n                <span *ngIf=\"pinnedMessage.content\">{{\n                  pinnedMessage.content\n                }}</span>\n                <span\n                  *ngIf=\"hasImage(pinnedMessage)\"\n                  class=\"italic flex items-center\"\n                >\n                  <i class=\"fas fa-image mr-1\"></i>\n                  Image\n                </span>\n                <span\n                  *ngIf=\"isVoiceMessage(pinnedMessage)\"\n                  class=\"italic flex items-center\"\n                >\n                  <i class=\"fas fa-microphone mr-1\"></i>\n                  Message vocal\n                </span>\n              </div>\n\n              <!-- Indicateur d'épinglage -->\n              <div class=\"flex items-center mt-1\">\n                <i\n                  class=\"fas fa-thumbtack text-xs text-[#4f5fad] dark:text-[#6d78c9] mr-1\"\n                ></i>\n                <span class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9]\">\n                  Épinglé\n                  {{\n                    pinnedMessage.pinnedAt\n                      ? \"le \" + formatMessageDate(pinnedMessage.pinnedAt)\n                      : \"\"\n                  }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Icône de navigation -->\n            <div class=\"flex-shrink-0 text-[#6d6870]/50 dark:text-[#a0a0a0]/50\">\n              <i class=\"fas fa-chevron-right text-xs\"></i>\n            </div>\n          </div>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Zone de messages futuriste -->\n  <div\n    #messagesContainer\n    class=\"futuristic-messages-container\"\n    [ngClass]=\"{ 'with-search-bar': showSearchBar }\"\n    (scroll)=\"onScroll($event)\"\n  >\n    <!-- État de chargement (initial) -->\n    <div *ngIf=\"loading\" class=\"flex justify-center items-center h-full\">\n      <div class=\"flex flex-col items-center\">\n        <div class=\"relative\">\n          <div\n            class=\"w-12 h-12 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin mb-3\"\n          ></div>\n          <!-- Glow effect -->\n          <div\n            class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n          ></div>\n        </div>\n        <div\n          class=\"text-[#6d6870] dark:text-[#a0a0a0] mt-3 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent font-medium\"\n        >\n          Initializing communication...\n        </div>\n      </div>\n    </div>\n\n    <!-- Indicateur de chargement de messages supplémentaires -->\n    <div\n      *ngIf=\"isLoadingMore\"\n      class=\"flex justify-center py-2 sticky top-0 z-10\"\n    >\n      <div\n        class=\"flex flex-col items-center backdrop-blur-sm bg-white/50 dark:bg-[#1e1e1e]/50 px-4 py-2 rounded-full shadow-sm\"\n      >\n        <div class=\"flex space-x-2 mb-1\">\n          <div\n            class=\"w-2 h-2 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-pulse shadow-[0_0_5px_rgba(79,95,173,0.5)] dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\"\n          ></div>\n          <div\n            class=\"w-2 h-2 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-pulse shadow-[0_0_5px_rgba(79,95,173,0.5)] dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\"\n            style=\"animation-delay: 0.2s\"\n          ></div>\n          <div\n            class=\"w-2 h-2 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-pulse shadow-[0_0_5px_rgba(79,95,173,0.5)] dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\"\n            style=\"animation-delay: 0.4s\"\n          ></div>\n        </div>\n        <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n          Retrieving data...\n        </div>\n      </div>\n    </div>\n\n    <!-- Indicateur de début de conversation -->\n    <div\n      *ngIf=\"!hasMoreMessages && messages.length > 0\"\n      class=\"flex justify-center py-2 mb-2\"\n    >\n      <div class=\"flex items-center w-full max-w-xs\">\n        <div\n          class=\"flex-1 h-px bg-gradient-to-r from-transparent via-[#4f5fad]/20 dark:via-[#6d78c9]/20 to-transparent\"\n        ></div>\n        <div\n          class=\"px-3 text-xs bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent font-medium\"\n        >\n          Communication Initialized\n        </div>\n        <div\n          class=\"flex-1 h-px bg-gradient-to-r from-transparent via-[#4f5fad]/20 dark:via-[#6d78c9]/20 to-transparent\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- État d'erreur -->\n    <div\n      *ngIf=\"error\"\n      class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-md my-4 backdrop-blur-sm\"\n    >\n      <div class=\"flex items-start\">\n        <div class=\"text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <!-- Glow effect -->\n          <div\n            class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\n          ></div>\n        </div>\n        <div>\n          <h3 class=\"font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1\">\n            System Error: Communication Failure\n          </h3>\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Messages -->\n    <ng-container *ngIf=\"messages && messages.length > 0; else noMessages\">\n      <div\n        *ngFor=\"let message of messages; let i = index\"\n        class=\"futuristic-message-wrapper\"\n        [attr.data-message-id]=\"message.id\"\n      >\n        <!-- Séparateur de date futuriste -->\n        <div *ngIf=\"shouldShowDateHeader(i)\" class=\"futuristic-date-separator\">\n          <div class=\"futuristic-date-line\"></div>\n          <div class=\"futuristic-date-text\">\n            {{ formatMessageDate(message?.timestamp) }}\n          </div>\n          <div class=\"futuristic-date-line\"></div>\n        </div>\n\n        <!-- Conteneur de message avec alignement -->\n        <div\n          class=\"futuristic-message group relative\"\n          [ngClass]=\"{\n            'futuristic-message-current-user':\n              message?.sender?.id === currentUserId ||\n              message?.sender?._id === currentUserId ||\n              message?.senderId === currentUserId,\n            'futuristic-message-other-user': !(\n              message?.sender?.id === currentUserId ||\n              message?.sender?._id === currentUserId ||\n              message?.senderId === currentUserId\n            )\n          }\"\n        >\n          <!-- Bouton de réaction rapide (pour tous les messages) -->\n          <button\n            *ngIf=\"message.id && !message.isDeleted\"\n            (click)=\"toggleReactionPicker(message.id)\"\n            data-reaction-trigger\n            class=\"absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-white dark:bg-[#2a2a2a] border border-[#edf1f4] dark:border-[#3a3a3a] hover:border-[#4f5fad] dark:hover:border-[#6d78c9] text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] rounded-full flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-all duration-200 z-10 shadow-lg\"\n          >\n            <i class=\"fas fa-smile\"></i>\n          </button>\n\n          <!-- Sélecteur de réactions global (positionné sous le message) -->\n          <div\n            *ngIf=\"message.id && showReactionPicker[message.id]\"\n            class=\"reaction-picker absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#edf1f4]/50 dark:border-[#3a3a3a] z-30 p-2\"\n          >\n            <div class=\"flex space-x-1\">\n              <button\n                *ngFor=\"let emoji of availableReactions\"\n                (click)=\"reactToMessage(message.id, emoji)\"\n                class=\"w-8 h-8 flex items-center justify-center text-lg hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded transition-colors\"\n                [title]=\"'Réagir avec ' + emoji\"\n              >\n                {{ emoji }}\n              </button>\n            </div>\n          </div>\n          <!-- Avatar pour les messages reçus -->\n          <div\n            *ngIf=\"\n              !(\n                message?.sender?.id === currentUserId ||\n                message?.sender?._id === currentUserId ||\n                message?.senderId === currentUserId\n              )\n            \"\n            class=\"futuristic-avatar\"\n          >\n            <img\n              [src]=\"\n                message?.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              alt=\"User avatar\"\n              onerror=\"this.src='assets/images/default-avatar.png'\"\n            />\n          </div>\n\n          <!-- Contenu du message -->\n          <div class=\"futuristic-message-content\">\n            <!-- Contenu textuel -->\n            <div\n              *ngIf=\"\n                message?.content &&\n                !hasImage(message) &&\n                !isVoiceMessage(message)\n              \"\n              class=\"futuristic-message-bubble relative\"\n              [ngClass]=\"{\n                'futuristic-message-pending': message.isPending,\n                'futuristic-message-sending':\n                  message.isPending && !message.isError,\n                'futuristic-message-error': message.isError,\n                'futuristic-message-pinned': isMessagePinned(message)\n              }\"\n              [id]=\"'message-' + message.id\"\n              (mouseenter)=\"message.id && canEditMessage(message) ? null : null\"\n              (mouseleave)=\"message.id && canEditMessage(message) ? null : null\"\n            >\n              <!-- Bouton d'options (trois points) pour les messages de l'utilisateur -->\n              <button\n                *ngIf=\"\n                  message.id && canEditMessage(message) && !message.isDeleted\n                \"\n                (click)=\"toggleMessageOptions(message.id)\"\n                class=\"absolute -top-2 -right-2 w-6 h-6 bg-[#4f5fad]/80 dark:bg-[#6d78c9]/80 hover:bg-[#4f5fad] dark:hover:bg-[#6d78c9] text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\"\n              >\n                <i class=\"fas fa-ellipsis-v text-xs\"></i>\n              </button>\n\n              <!-- Menu d'options -->\n              <div\n                *ngIf=\"message.id && showMessageOptions[message.id]\"\n                class=\"absolute top-6 right-0 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#edf1f4]/50 dark:border-[#3a3a3a] z-20 min-w-[140px] overflow-hidden\"\n              >\n                <button\n                  (click)=\"startReplyToMessage(message)\"\n                  class=\"w-full px-3 py-2 text-left text-sm hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\n                >\n                  <i class=\"fas fa-reply text-xs\"></i>\n                  <span>Répondre</span>\n                </button>\n                <button\n                  (click)=\"openForwardModal(message)\"\n                  class=\"w-full px-3 py-2 text-left text-sm hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\n                >\n                  <i class=\"fas fa-share text-xs\"></i>\n                  <span>Transférer</span>\n                </button>\n                <button\n                  (click)=\"showPinConfirmation(message.id)\"\n                  class=\"w-full px-3 py-2 text-left text-sm hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\n                >\n                  <i [class]=\"getPinIcon(message)\" class=\"text-xs\"></i>\n                  <span>{{ getPinDisplayText(message) }}</span>\n                </button>\n                <button\n                  *ngIf=\"canEditMessage(message)\"\n                  (click)=\"startEditMessage(message)\"\n                  class=\"w-full px-3 py-2 text-left text-sm hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\n                >\n                  <i class=\"fas fa-edit text-xs\"></i>\n                  <span>Modifier</span>\n                </button>\n                <button\n                  *ngIf=\"canEditMessage(message)\"\n                  (click)=\"showDeleteConfirmation(message.id)\"\n                  class=\"w-full px-3 py-2 text-left text-sm hover:bg-[#ff6b69]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] transition-colors\"\n                >\n                  <i class=\"fas fa-trash text-xs\"></i>\n                  <span>Supprimer</span>\n                </button>\n              </div>\n\n              <!-- Confirmation de suppression -->\n              <div\n                *ngIf=\"message.id && showDeleteConfirm[message.id]\"\n                class=\"absolute top-6 right-0 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#ff6b69]/50 z-20 min-w-[200px] p-3\"\n              >\n                <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-3\">\n                  Supprimer ce message ?\n                </div>\n                <div class=\"flex space-x-2\">\n                  <button\n                    (click)=\"cancelDeleteMessage(message.id)\"\n                    class=\"px-3 py-1 text-xs bg-[#edf1f4] dark:bg-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 text-[#6d6870] dark:text-[#a0a0a0] rounded transition-colors\"\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    (click)=\"confirmDeleteMessage(message.id)\"\n                    class=\"px-3 py-1 text-xs bg-[#ff6b69] hover:bg-[#ff6b69]/80 text-white rounded transition-colors\"\n                  >\n                    Supprimer\n                  </button>\n                </div>\n              </div>\n\n              <!-- Confirmation d'épinglage -->\n              <div\n                *ngIf=\"message.id && showPinConfirm[message.id]\"\n                class=\"absolute top-6 right-0 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#4f5fad]/50 dark:border-[#6d78c9]/50 z-20 min-w-[200px] p-3\"\n              >\n                <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-3\">\n                  {{\n                    isMessagePinned(message)\n                      ? \"Désépingler ce message ?\"\n                      : \"Épingler ce message ?\"\n                  }}\n                </div>\n                <div class=\"flex space-x-2\">\n                  <button\n                    (click)=\"cancelPinConfirmation(message.id)\"\n                    class=\"px-3 py-1 text-xs bg-[#edf1f4] dark:bg-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 text-[#6d6870] dark:text-[#a0a0a0] rounded transition-colors\"\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    (click)=\"togglePinMessage(message)\"\n                    [disabled]=\"isPinning[message.id]\"\n                    class=\"px-3 py-1 text-xs bg-[#4f5fad] dark:bg-[#6d78c9] hover:bg-[#4f5fad]/80 dark:hover:bg-[#6d78c9]/80 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1\"\n                  >\n                    <i\n                      *ngIf=\"isPinning[message.id]\"\n                      class=\"fas fa-spinner fa-spin text-xs\"\n                    ></i>\n                    <span>{{\n                      isMessagePinned(message) ? \"Désépingler\" : \"Épingler\"\n                    }}</span>\n                  </button>\n                </div>\n              </div>\n\n              <!-- Contenu du message (normal ou en édition) -->\n              <div\n                *ngIf=\"editingMessageId !== message.id\"\n                class=\"futuristic-message-text\"\n              >\n                <!-- Indicateur de message épinglé -->\n                <div\n                  *ngIf=\"isMessagePinned(message)\"\n                  class=\"flex items-center mb-1\"\n                >\n                  <i\n                    class=\"fas fa-thumbtack text-xs text-[#4f5fad] dark:text-[#6d78c9] mr-1\"\n                  ></i>\n                  <span\n                    class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n                    >Épinglé</span\n                  >\n                </div>\n\n                <!-- Message auquel on répond -->\n                <div\n                  *ngIf=\"message.replyTo\"\n                  class=\"reply-to-message bg-[#edf1f4]/30 dark:bg-[#3a3a3a]/30 border-l-2 border-[#4f5fad]/50 dark:border-[#6d78c9]/50 p-2 mb-2 rounded-r text-xs\"\n                >\n                  <div\n                    class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium mb-1\"\n                  >\n                    {{ message.replyTo.sender?.username || \"Utilisateur\" }}\n                  </div>\n                  <div class=\"text-[#6d6870] dark:text-[#a0a0a0] line-clamp-2\">\n                    {{ message.replyTo.content || \"Message\" }}\n                  </div>\n                </div>\n\n                <span *ngIf=\"!message.isDeleted\">{{ message.content }}</span>\n                <span\n                  *ngIf=\"message.isDeleted\"\n                  class=\"italic text-[#6d6870] dark:text-[#a0a0a0]\"\n                >\n                  {{ message.content }}\n                </span>\n                <span\n                  *ngIf=\"message.isEdited && !message.isDeleted\"\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] ml-2\"\n                >\n                  (modifié)\n                </span>\n              </div>\n\n              <!-- Mode édition -->\n              <div\n                *ngIf=\"editingMessageId === message.id\"\n                class=\"futuristic-message-edit\"\n              >\n                <textarea\n                  [(ngModel)]=\"editingContent\"\n                  (keydown)=\"onEditKeyPress($event, message.id)\"\n                  class=\"w-full p-2 text-sm bg-transparent border border-[#4f5fad]/30 dark:border-[#6d78c9]/30 rounded resize-none focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] text-[#6d6870] dark:text-[#a0a0a0]\"\n                  rows=\"2\"\n                  placeholder=\"Modifier le message...\"\n                  autofocus\n                ></textarea>\n                <div class=\"flex justify-end space-x-2 mt-2\">\n                  <button\n                    (click)=\"cancelEditMessage()\"\n                    class=\"px-3 py-1 text-xs bg-[#edf1f4] dark:bg-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 text-[#6d6870] dark:text-[#a0a0a0] rounded transition-colors\"\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    (click)=\"saveEditMessage(message.id)\"\n                    class=\"px-3 py-1 text-xs bg-[#4f5fad] hover:bg-[#4f5fad]/80 dark:bg-[#6d78c9] dark:hover:bg-[#6d78c9]/80 text-white rounded transition-colors\"\n                  >\n                    Sauvegarder\n                  </button>\n                </div>\n              </div>\n\n              <!-- Réactions existantes - Template réutilisable -->\n              <ng-container\n                *ngTemplateOutlet=\"\n                  reactionButtonsTemplate;\n                  context: { message: message }\n                \"\n              ></ng-container>\n\n              <!-- Heure du message avec statut de lecture - Template réutilisable -->\n              <ng-container\n                *ngTemplateOutlet=\"\n                  messageInfoTemplate;\n                  context: { message: message }\n                \"\n              ></ng-container>\n            </div>\n\n            <!-- Message vocal moderne (style WhatsApp) -->\n            <div\n              *ngIf=\"isVoiceMessage(message)\"\n              class=\"voice-message-modern\"\n              [ngClass]=\"{\n                'voice-message-sent':\n                  message?.sender?.id === currentUserId ||\n                  message?.sender?._id === currentUserId ||\n                  message?.senderId === currentUserId,\n                'voice-message-received': !(\n                  message?.sender?.id === currentUserId ||\n                  message?.sender?._id === currentUserId ||\n                  message?.senderId === currentUserId\n                ),\n                'voice-message-pending': message.isPending,\n                'voice-message-error': message.isError\n              }\"\n            >\n              <!-- Bouton play/pause compact -->\n              <button class=\"voice-play-btn-modern\">\n                <i class=\"fas fa-play\"></i>\n              </button>\n\n              <!-- Forme d'onde compacte -->\n              <div class=\"voice-waveform-modern\">\n                <div\n                  *ngFor=\"\n                    let i of [\n                      0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,\n                      17, 18, 19\n                    ]\n                  \"\n                  class=\"voice-bar-modern\"\n                  [style.height.px]=\"getVoiceBarHeight(i)\"\n                ></div>\n              </div>\n\n              <!-- Durée du message vocal -->\n              <div class=\"voice-duration-modern\">\n                {{ formatVoiceDuration(getVoiceMessageDuration(message)) }}\n              </div>\n\n              <!-- Composant caché pour la lecture -->\n              <app-voice-message-player\n                [audioUrl]=\"getVoiceMessageUrl(message)\"\n                [duration]=\"getVoiceMessageDuration(message)\"\n                class=\"hidden\"\n              ></app-voice-message-player>\n\n              <!-- Réactions existantes pour messages vocaux - Template réutilisable -->\n              <ng-container\n                *ngTemplateOutlet=\"\n                  reactionButtonsTemplate;\n                  context: { message: message }\n                \"\n              ></ng-container>\n\n              <!-- Heure du message avec statut de lecture - Template réutilisable -->\n              <ng-container\n                *ngTemplateOutlet=\"\n                  messageInfoTemplate;\n                  context: { message: message }\n                \"\n              ></ng-container>\n            </div>\n\n            <!-- Contenu image -->\n            <div\n              *ngIf=\"hasImage(message)\"\n              class=\"futuristic-message-image-container\"\n              [ngClass]=\"{\n                'futuristic-message-pending': message.isPending,\n                'futuristic-message-sending':\n                  message.isPending && !message.isError,\n                'futuristic-message-error': message.isError\n              }\"\n            >\n              <div class=\"futuristic-image-wrapper\">\n                <a\n                  [href]=\"getImageUrl(message)\"\n                  target=\"_blank\"\n                  class=\"futuristic-message-image-link\"\n                >\n                  <img\n                    [src]=\"getImageUrl(message)\"\n                    class=\"futuristic-message-image\"\n                    alt=\"Image\"\n                  />\n                </a>\n                <div class=\"futuristic-image-overlay\">\n                  <i class=\"fas fa-expand\"></i>\n                </div>\n              </div>\n\n              <!-- Réactions existantes pour messages avec images - Template réutilisable -->\n              <ng-container\n                *ngTemplateOutlet=\"\n                  reactionButtonsTemplate;\n                  context: { message: message }\n                \"\n              ></ng-container>\n\n              <!-- Heure du message avec statut de lecture - Template réutilisable -->\n              <ng-container\n                *ngTemplateOutlet=\"\n                  messageInfoTemplate;\n                  context: { message: message }\n                \"\n              ></ng-container>\n            </div>\n          </div>\n        </div>\n      </div>\n    </ng-container>\n\n    <!-- Template pour aucun message -->\n    <ng-template #noMessages>\n      <div *ngIf=\"!loading && !error\" class=\"futuristic-no-messages\">\n        <div class=\"futuristic-no-messages-icon\">\n          <i class=\"fas fa-satellite-dish\"></i>\n        </div>\n        <div class=\"futuristic-no-messages-text\">\n          Aucun message dans cette conversation.\n          <br />Établissez le premier contact pour commencer.\n        </div>\n        <button\n          (click)=\"messageForm.get('content')?.setValue('Bonjour!')\"\n          class=\"futuristic-start-button\"\n        >\n          <i class=\"fas fa-paper-plane\"></i>\n          Initialiser la communication\n        </button>\n      </div>\n    </ng-template>\n\n    <!-- Indicateur de frappe -->\n    <div *ngIf=\"isTyping\" class=\"futuristic-typing-indicator\">\n      <div class=\"futuristic-avatar\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          alt=\"User avatar\"\n        />\n      </div>\n      <div class=\"futuristic-typing-bubble\">\n        <div class=\"futuristic-typing-dots\">\n          <div class=\"futuristic-typing-dot\"></div>\n          <div\n            class=\"futuristic-typing-dot\"\n            style=\"animation-delay: 0.2s\"\n          ></div>\n          <div\n            class=\"futuristic-typing-dot\"\n            style=\"animation-delay: 0.4s\"\n          ></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Zone de saisie style WhatsApp -->\n  <div class=\"whatsapp-input-container\">\n    <!-- Aperçu du fichier -->\n    <div *ngIf=\"previewUrl\" class=\"whatsapp-file-preview\">\n      <img [src]=\"previewUrl\" class=\"whatsapp-preview-image\" />\n      <button (click)=\"removeAttachment()\" class=\"whatsapp-remove-button\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n\n    <!-- Sélecteur d'émojis -->\n    <div *ngIf=\"showEmojiPicker\" class=\"whatsapp-emoji-picker\">\n      <div class=\"whatsapp-emoji-categories\">\n        <button class=\"whatsapp-emoji-category active\">\n          <i class=\"far fa-smile\"></i>\n        </button>\n        <button class=\"whatsapp-emoji-category\">\n          <i class=\"fas fa-cat\"></i>\n        </button>\n        <button class=\"whatsapp-emoji-category\">\n          <i class=\"fas fa-hamburger\"></i>\n        </button>\n        <button class=\"whatsapp-emoji-category\">\n          <i class=\"fas fa-futbol\"></i>\n        </button>\n        <button class=\"whatsapp-emoji-category\">\n          <i class=\"fas fa-car\"></i>\n        </button>\n        <button class=\"whatsapp-emoji-category\">\n          <i class=\"fas fa-lightbulb\"></i>\n        </button>\n      </div>\n      <div class=\"whatsapp-emoji-list\">\n        <button\n          *ngFor=\"let emoji of commonEmojis\"\n          class=\"whatsapp-emoji-item\"\n          (click)=\"insertEmoji(emoji)\"\n        >\n          {{ emoji }}\n        </button>\n      </div>\n    </div>\n\n    <!-- Aperçu du message auquel on répond -->\n    <div\n      *ngIf=\"replyingToMessage\"\n      class=\"reply-preview bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 border-l-4 border-[#4f5fad] dark:border-[#6d78c9] p-3 mx-4 mb-2 rounded-r-lg\"\n    >\n      <div class=\"flex items-start justify-between\">\n        <div class=\"flex-1\">\n          <div\n            class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9] font-medium mb-1\"\n          >\n            Réponse à {{ replyingToMessage.sender?.username || \"Utilisateur\" }}\n          </div>\n          <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] line-clamp-2\">\n            {{ replyingToMessage.content || \"Message\" }}\n          </div>\n        </div>\n        <button\n          (click)=\"cancelReply()\"\n          class=\"ml-2 w-6 h-6 flex items-center justify-center text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\"\n        >\n          <i class=\"fas fa-times text-xs\"></i>\n        </button>\n      </div>\n    </div>\n\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      class=\"whatsapp-input-form\"\n    >\n      <!-- Boutons d'outils style WhatsApp -->\n      <div class=\"whatsapp-input-tools\">\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          class=\"whatsapp-tool-button\"\n          [ngClass]=\"{ active: showEmojiPicker }\"\n        >\n          <i class=\"far fa-smile\"></i>\n        </button>\n        <button\n          type=\"button\"\n          (click)=\"fileInput.click()\"\n          class=\"whatsapp-tool-button\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n          <input\n            #fileInput\n            type=\"file\"\n            (change)=\"onFileSelected($event)\"\n            class=\"hidden\"\n            accept=\"image/*\"\n          />\n        </button>\n      </div>\n\n      <!-- Composant d'enregistrement vocal -->\n      <app-voice-recorder\n        *ngIf=\"isRecordingVoice\"\n        (recordingComplete)=\"onVoiceRecordingComplete($event)\"\n        (recordingCancelled)=\"onVoiceRecordingCancelled()\"\n        [maxDuration]=\"60\"\n      ></app-voice-recorder>\n\n      <input\n        *ngIf=\"!isRecordingVoice\"\n        formControlName=\"content\"\n        type=\"text\"\n        placeholder=\"Message\"\n        (input)=\"onTyping()\"\n        class=\"whatsapp-input-field\"\n      />\n\n      <button\n        type=\"button\"\n        *ngIf=\"!isRecordingVoice && messageForm.get('content')?.value === ''\"\n        (click)=\"toggleVoiceRecording()\"\n        class=\"whatsapp-voice-button\"\n      >\n        <i class=\"fas fa-microphone\"></i>\n      </button>\n\n      <button\n        type=\"submit\"\n        *ngIf=\"!isRecordingVoice && messageForm.get('content')?.value !== ''\"\n        [disabled]=\"isUploading || (messageForm.invalid && !selectedFile)\"\n        class=\"whatsapp-send-button\"\n      >\n        <i *ngIf=\"!isUploading\" class=\"fas fa-paper-plane\"></i>\n        <i *ngIf=\"isUploading\" class=\"fas fa-spinner fa-spin\"></i>\n      </button>\n    </form>\n  </div>\n\n  <!-- Modal d'appel entrant -->\n  <div *ngIf=\"showCallModal && incomingCall\" class=\"whatsapp-call-modal\">\n    <div class=\"whatsapp-call-modal-content\">\n      <div class=\"whatsapp-call-header\">\n        <div class=\"whatsapp-call-avatar\">\n          <img\n            [src]=\"\n              incomingCall.caller?.image || 'assets/images/default-avatar.png'\n            \"\n            alt=\"Caller avatar\"\n          />\n        </div>\n        <h3 class=\"whatsapp-call-name\">{{ incomingCall.caller?.username }}</h3>\n        <p class=\"whatsapp-call-status\">\n          {{\n            incomingCall.type === \"AUDIO\"\n              ? \"Appel audio entrant\"\n              : \"Appel vidéo entrant\"\n          }}\n        </p>\n      </div>\n      <div class=\"whatsapp-call-actions\">\n        <button (click)=\"rejectCall()\" class=\"whatsapp-call-reject\">\n          <i class=\"fas fa-phone-slash\"></i>\n          <span>Rejeter</span>\n        </button>\n        <button (click)=\"acceptCall()\" class=\"whatsapp-call-accept\">\n          <i class=\"fas fa-phone\"></i>\n          <span>Accepter</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modal d'appel actif -->\n  <div\n    *ngIf=\"showActiveCallModal && activeCall\"\n    class=\"active-call-modal\"\n    [ngClass]=\"{ minimized: isCallMinimized }\"\n    (mousemove)=\"onCallMouseMove()\"\n  >\n    <!-- Interface d'appel vidéo -->\n    <div *ngIf=\"activeCall.type === 'VIDEO'\" class=\"video-call-interface\">\n      <!-- Vidéo distante (plein écran) -->\n      <video #remoteVideo class=\"remote-video\" autoplay playsinline></video>\n\n      <!-- Vidéo locale (petit écran) -->\n      <video\n        #localVideo\n        class=\"local-video\"\n        autoplay\n        muted\n        playsinline\n        [style.display]=\"isVideoEnabled ? 'block' : 'none'\"\n      ></video>\n\n      <!-- Avatar local si vidéo désactivée -->\n      <div *ngIf=\"!isVideoEnabled\" class=\"local-avatar\">\n        <img [src]=\"'assets/images/default-avatar.png'\" alt=\"Vous\" />\n      </div>\n    </div>\n\n    <!-- Interface d'appel audio -->\n    <div *ngIf=\"activeCall.type === 'AUDIO'\" class=\"audio-call-interface\">\n      <div class=\"audio-call-content\">\n        <div class=\"call-participant-avatar\">\n          <img\n            [src]=\"\n              activeCall.caller?.image ||\n              activeCall.recipient?.image ||\n              'assets/images/default-avatar.png'\n            \"\n            alt=\"Participant\"\n          />\n        </div>\n        <h3 class=\"call-participant-name\">\n          {{ activeCall.caller?.username || activeCall.recipient?.username }}\n        </h3>\n        <p class=\"call-status\">{{ formatCallDuration(callDuration) }}</p>\n        <div class=\"call-quality-indicator\">\n          <i\n            class=\"fas fa-signal\"\n            [ngClass]=\"{\n              'text-green-500': callQuality === 'excellent',\n              'text-yellow-500': callQuality === 'good',\n              'text-red-500': callQuality === 'poor',\n              'text-gray-500': callQuality === 'connecting'\n            }\"\n          ></i>\n          <span class=\"quality-text\">{{ callQuality }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Contrôles d'appel -->\n    <div\n      class=\"call-controls\"\n      [ngClass]=\"{ hidden: !showCallControls }\"\n      *ngIf=\"!isCallMinimized\"\n    >\n      <!-- Informations d'appel (pour vidéo) -->\n      <div *ngIf=\"activeCall.type === 'VIDEO'\" class=\"call-info\">\n        <span class=\"call-duration\">{{\n          formatCallDuration(callDuration)\n        }}</span>\n        <span class=\"call-participant\">\n          {{ activeCall.caller?.username || activeCall.recipient?.username }}\n        </span>\n      </div>\n\n      <!-- Boutons de contrôle -->\n      <div class=\"control-buttons\">\n        <!-- Bouton muet -->\n        <button\n          (click)=\"toggleCallMute()\"\n          class=\"control-btn\"\n          [ngClass]=\"{ active: isCallMuted }\"\n        >\n          <i\n            class=\"fas\"\n            [ngClass]=\"isCallMuted ? 'fa-microphone-slash' : 'fa-microphone'\"\n          ></i>\n        </button>\n\n        <!-- Bouton vidéo (seulement pour appels vidéo) -->\n        <button\n          *ngIf=\"activeCall.type === 'VIDEO'\"\n          (click)=\"toggleCallVideo()\"\n          class=\"control-btn\"\n          [ngClass]=\"{ active: !isVideoEnabled }\"\n        >\n          <i\n            class=\"fas\"\n            [ngClass]=\"isVideoEnabled ? 'fa-video' : 'fa-video-slash'\"\n          ></i>\n        </button>\n\n        <!-- Bouton raccrocher -->\n        <button (click)=\"endCall()\" class=\"control-btn end-call\">\n          <i class=\"fas fa-phone-slash\"></i>\n        </button>\n\n        <!-- Bouton minimiser -->\n        <button (click)=\"toggleCallMinimize()\" class=\"control-btn\">\n          <i\n            class=\"fas\"\n            [ngClass]=\"isCallMinimized ? 'fa-expand' : 'fa-compress'\"\n          ></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Interface minimisée -->\n    <div *ngIf=\"isCallMinimized\" class=\"minimized-call-interface\">\n      <div class=\"minimized-info\">\n        <span class=\"minimized-duration\">{{\n          formatCallDuration(callDuration)\n        }}</span>\n        <span class=\"minimized-participant\">\n          {{ activeCall.caller?.username || activeCall.recipient?.username }}\n        </span>\n      </div>\n      <div class=\"minimized-controls\">\n        <button (click)=\"toggleCallMinimize()\" class=\"minimized-btn\">\n          <i class=\"fas fa-expand\"></i>\n        </button>\n        <button (click)=\"endCall()\" class=\"minimized-btn end-call\">\n          <i class=\"fas fa-phone-slash\"></i>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modal de transfert de message -->\n  <div\n    *ngIf=\"showForwardModal\"\n    class=\"fixed inset-0 bg-black/50 dark:bg-black/70 flex items-center justify-center z-50 p-4\"\n    (click)=\"closeForwardModal()\"\n  >\n    <div\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-hidden\"\n      (click)=\"$event.stopPropagation()\"\n    >\n      <!-- En-tête du modal -->\n      <div\n        class=\"flex items-center justify-between p-4 border-b border-[#edf1f4]/50 dark:border-[#3a3a3a]\"\n      >\n        <h3 class=\"text-lg font-semibold text-[#6d6870] dark:text-[#a0a0a0]\">\n          Transférer le message\n        </h3>\n        <button\n          (click)=\"closeForwardModal()\"\n          class=\"w-8 h-8 flex items-center justify-center text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-full transition-colors\"\n        >\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <!-- Aperçu du message à transférer -->\n      <div\n        class=\"p-4 border-b border-[#edf1f4]/50 dark:border-[#3a3a3a] bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30\"\n      >\n        <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mb-2\">\n          Message à transférer :\n        </div>\n        <div\n          class=\"bg-white dark:bg-[#2a2a2a] rounded-lg p-3 border border-[#edf1f4] dark:border-[#3a3a3a]\"\n        >\n          <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] line-clamp-3\">\n            {{ forwardingMessage?.content || \"Message sans contenu\" }}\n          </div>\n          <div *ngIf=\"hasImage(forwardingMessage)\" class=\"mt-2\">\n            <div class=\"text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70\">\n              <i class=\"fas fa-image mr-1\"></i>\n              Image jointe\n            </div>\n          </div>\n          <div *ngIf=\"isVoiceMessage(forwardingMessage)\" class=\"mt-2\">\n            <div class=\"text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70\">\n              <i class=\"fas fa-microphone mr-1\"></i>\n              Message vocal\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Actions de sélection -->\n      <div class=\"p-4 border-b border-[#edf1f4]/50 dark:border-[#3a3a3a]\">\n        <div class=\"flex items-center justify-between mb-3\">\n          <span class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">\n            Sélectionner les conversations\n          </span>\n          <div class=\"flex space-x-2\">\n            <button\n              *ngIf=\"!areAllConversationsSelected()\"\n              (click)=\"selectAllConversations()\"\n              class=\"text-xs px-2 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad]/20 dark:hover:bg-[#6d78c9]/20 rounded transition-colors\"\n            >\n              Tout sélectionner\n            </button>\n            <button\n              *ngIf=\"areAllConversationsSelected()\"\n              (click)=\"deselectAllConversations()\"\n              class=\"text-xs px-2 py-1 bg-[#ff6b69]/10 text-[#ff6b69] hover:bg-[#ff6b69]/20 rounded transition-colors\"\n            >\n              Tout désélectionner\n            </button>\n          </div>\n        </div>\n        <div class=\"text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70\">\n          {{ selectedConversations.length }} conversation(s) sélectionnée(s)\n        </div>\n      </div>\n\n      <!-- Liste des conversations -->\n      <div class=\"max-h-60 overflow-y-auto\">\n        <!-- État de chargement -->\n        <div *ngIf=\"isLoadingConversations\" class=\"p-4 text-center\">\n          <div class=\"flex items-center justify-center space-x-2\">\n            <div\n              class=\"w-4 h-4 border-2 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\n            ></div>\n            <span class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\"\n              >Chargement des conversations...</span\n            >\n          </div>\n        </div>\n\n        <!-- Liste des conversations -->\n        <div\n          *ngIf=\"!isLoadingConversations && availableConversations.length > 0\"\n        >\n          <button\n            *ngFor=\"let conversation of availableConversations\"\n            (click)=\"\n              conversation.id && toggleConversationSelection(conversation.id)\n            \"\n            class=\"w-full p-3 flex items-center space-x-3 hover:bg-[#4f5fad]/5 dark:hover:bg-[#6d78c9]/5 transition-colors border-b border-[#edf1f4]/30 dark:border-[#3a3a3a]/30 last:border-b-0\"\n          >\n            <!-- Checkbox -->\n            <div class=\"flex-shrink-0\">\n              <div\n                class=\"w-5 h-5 rounded border-2 flex items-center justify-center transition-colors\"\n                [ngClass]=\"{\n                  'border-[#4f5fad] dark:border-[#6d78c9] bg-[#4f5fad] dark:bg-[#6d78c9]':\n                    conversation.id && isConversationSelected(conversation.id),\n                  'border-[#edf1f4] dark:border-[#3a3a3a] bg-transparent':\n                    !conversation.id || !isConversationSelected(conversation.id)\n                }\"\n              >\n                <i\n                  *ngIf=\"\n                    conversation.id && isConversationSelected(conversation.id)\n                  \"\n                  class=\"fas fa-check text-xs text-white\"\n                ></i>\n              </div>\n            </div>\n\n            <!-- Avatar -->\n            <div class=\"flex-shrink-0\">\n              <img\n                [src]=\"getConversationDisplayImage(conversation)\"\n                [alt]=\"getConversationDisplayName(conversation)\"\n                class=\"w-10 h-10 rounded-full object-cover\"\n                onerror=\"this.src='assets/images/default-avatar.png'\"\n              />\n            </div>\n\n            <!-- Informations de la conversation -->\n            <div class=\"flex-1 min-w-0 text-left\">\n              <div\n                class=\"font-medium text-[#6d6870] dark:text-[#a0a0a0] truncate\"\n              >\n                {{ getConversationDisplayName(conversation) }}\n              </div>\n              <div\n                class=\"text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70 truncate\"\n              >\n                {{ conversation.isGroup ? \"Groupe\" : \"Conversation privée\" }}\n                <span *ngIf=\"conversation.participants\">\n                  • {{ conversation.participants.length }} participant(s)</span\n                >\n              </div>\n            </div>\n          </button>\n        </div>\n\n        <!-- Aucune conversation disponible -->\n        <div\n          *ngIf=\"!isLoadingConversations && availableConversations.length === 0\"\n          class=\"p-4 text-center\"\n        >\n          <div class=\"text-[#6d6870]/70 dark:text-[#a0a0a0]/70\">\n            <i class=\"fas fa-comments text-2xl mb-2 block\"></i>\n            <div class=\"text-sm\">Aucune autre conversation disponible</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Actions du modal -->\n      <div\n        class=\"p-4 border-t border-[#edf1f4]/50 dark:border-[#3a3a3a] flex space-x-3\"\n      >\n        <button\n          (click)=\"closeForwardModal()\"\n          class=\"flex-1 px-4 py-2 text-sm bg-[#edf1f4] dark:bg-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 text-[#6d6870] dark:text-[#a0a0a0] rounded-lg transition-colors\"\n        >\n          Annuler\n        </button>\n        <button\n          (click)=\"forwardMessage()\"\n          [disabled]=\"selectedConversations.length === 0 || isForwarding\"\n          class=\"flex-1 px-4 py-2 text-sm bg-[#4f5fad] dark:bg-[#6d78c9] hover:bg-[#4f5fad]/80 dark:hover:bg-[#6d78c9]/80 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\"\n        >\n          <i *ngIf=\"isForwarding\" class=\"fas fa-spinner fa-spin\"></i>\n          <i *ngIf=\"!isForwarding\" class=\"fas fa-share\"></i>\n          <span>{{ isForwarding ? \"Transfert...\" : \"Transférer\" }}</span>\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Panneau de notifications avancé -->\n<div *ngIf=\"showNotificationPanel\" class=\"notification-panel-overlay\">\n  <div class=\"notification-panel\">\n    <!-- En-tête du panneau -->\n    <div class=\"notification-header\">\n      <div class=\"notification-title\">\n        <i class=\"fas fa-bell\"></i>\n        <span>Notifications</span>\n        <span *ngIf=\"unreadNotificationCount > 0\" class=\"notification-badge\">\n          {{ unreadNotificationCount }}\n        </span>\n      </div>\n      <div class=\"notification-actions\">\n        <button\n          (click)=\"toggleNotificationSettings()\"\n          class=\"notification-btn\"\n          title=\"Paramètres\"\n        >\n          <i class=\"fas fa-cog\"></i>\n        </button>\n        <button\n          (click)=\"loadNotifications(true)\"\n          class=\"notification-btn\"\n          [disabled]=\"isLoadingNotifications\"\n          title=\"Actualiser\"\n        >\n          <i\n            class=\"fas fa-sync-alt\"\n            [ngClass]=\"{ 'fa-spin': isLoadingNotifications }\"\n          ></i>\n        </button>\n        <button\n          (click)=\"toggleNotificationPanel()\"\n          class=\"notification-btn close-btn\"\n          title=\"Fermer\"\n        >\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Filtres et options -->\n    <div class=\"notification-filters\">\n      <div class=\"filter-tabs\">\n        <button\n          (click)=\"setNotificationFilter('all')\"\n          [ngClass]=\"{ active: notificationFilter === 'all' }\"\n          class=\"filter-tab\"\n        >\n          Toutes\n        </button>\n        <button\n          (click)=\"setNotificationFilter('unread')\"\n          [ngClass]=\"{ active: notificationFilter === 'unread' }\"\n          class=\"filter-tab\"\n        >\n          Non lues\n        </button>\n        <button\n          (click)=\"setNotificationFilter('read')\"\n          [ngClass]=\"{ active: notificationFilter === 'read' }\"\n          class=\"filter-tab\"\n        >\n          Lues\n        </button>\n      </div>\n\n      <!-- Actions de sélection -->\n      <div\n        class=\"selection-actions\"\n        *ngIf=\"getFilteredNotifications().length > 0\"\n      >\n        <div class=\"selection-controls\">\n          <label class=\"select-all-checkbox\">\n            <input\n              type=\"checkbox\"\n              [checked]=\"areAllNotificationsSelected()\"\n              (change)=\"toggleSelectAllNotifications()\"\n            />\n            <span class=\"checkmark\"></span>\n            <span class=\"select-text\">Tout sélectionner</span>\n          </label>\n        </div>\n\n        <div class=\"bulk-actions\" *ngIf=\"selectedNotifications.size > 0\">\n          <button\n            (click)=\"markSelectedAsRead()\"\n            [disabled]=\"isMarkingAsRead\"\n            class=\"action-btn mark-read-btn\"\n          >\n            <i class=\"fas fa-check\"></i>\n            Marquer comme lu\n          </button>\n          <button\n            (click)=\"showDeleteSelectedConfirmation()\"\n            [disabled]=\"isDeletingNotifications\"\n            class=\"action-btn delete-btn\"\n          >\n            <i class=\"fas fa-trash\"></i>\n            Supprimer\n          </button>\n        </div>\n      </div>\n\n      <!-- Action globale -->\n      <div class=\"global-actions\">\n        <button\n          *ngIf=\"unreadNotificationCount > 0\"\n          (click)=\"markAllAsRead()\"\n          [disabled]=\"isMarkingAsRead\"\n          class=\"action-btn mark-all-read-btn\"\n        >\n          <i class=\"fas fa-check-double\"></i>\n          Tout marquer comme lu\n        </button>\n        <button\n          *ngIf=\"notifications.length > 0\"\n          (click)=\"deleteAllNotifications()\"\n          [disabled]=\"isDeletingNotifications\"\n          class=\"action-btn delete-all-btn\"\n        >\n          <i\n            class=\"fas fa-trash-alt\"\n            [ngClass]=\"{ 'fa-spin': isDeletingNotifications }\"\n          ></i>\n          Supprimer tout\n        </button>\n      </div>\n    </div>\n\n    <!-- Liste des notifications -->\n    <div class=\"notification-list\">\n      <div\n        *ngIf=\"isLoadingNotifications && notifications.length === 0\"\n        class=\"loading-state\"\n      >\n        <i class=\"fas fa-spinner fa-spin\"></i>\n        <span>Chargement des notifications...</span>\n      </div>\n\n      <div\n        *ngIf=\"\n          !isLoadingNotifications && getFilteredNotifications().length === 0\n        \"\n        class=\"empty-state\"\n      >\n        <i class=\"fas fa-bell-slash\"></i>\n        <span>Aucune notification</span>\n      </div>\n\n      <div\n        *ngFor=\"\n          let notification of getFilteredNotifications();\n          trackBy: trackByNotificationId\n        \"\n        class=\"notification-item\"\n        [ngClass]=\"{\n          unread: !notification.isRead,\n          selected: selectedNotifications.has(notification.id)\n        }\"\n      >\n        <!-- Checkbox de sélection -->\n        <div class=\"notification-checkbox\">\n          <input\n            type=\"checkbox\"\n            [checked]=\"selectedNotifications.has(notification.id)\"\n            (change)=\"toggleNotificationSelection(notification.id)\"\n          />\n          <span class=\"checkmark\"></span>\n        </div>\n\n        <!-- Contenu de la notification -->\n        <div class=\"notification-content\">\n          <div\n            class=\"notification-icon\"\n            [ngClass]=\"getNotificationColor(notification.type)\"\n          >\n            <i [class]=\"getNotificationIcon(notification.type)\"></i>\n          </div>\n\n          <div class=\"notification-body\">\n            <div class=\"notification-text\">\n              <span class=\"notification-sender\" *ngIf=\"notification.senderId\">\n                {{ notification.senderId.username }}\n              </span>\n              <span class=\"notification-message\">{{\n                notification.content\n              }}</span>\n            </div>\n\n            <div class=\"notification-meta\">\n              <span class=\"notification-time\">\n                {{ formatNotificationDate(notification.timestamp) }}\n              </span>\n              <span *ngIf=\"notification.isRead\" class=\"read-indicator\">\n                <i class=\"fas fa-check\"></i>\n              </span>\n            </div>\n          </div>\n\n          <!-- Actions individuelles -->\n          <div class=\"notification-item-actions\">\n            <button\n              *ngIf=\"!notification.isRead\"\n              (click)=\"\n                markSelectedAsRead();\n                selectedNotifications.clear();\n                selectedNotifications.add(notification.id)\n              \"\n              class=\"item-action-btn\"\n              title=\"Marquer comme lu\"\n            >\n              <i class=\"fas fa-check\"></i>\n            </button>\n            <button\n              (click)=\"deleteNotification(notification.id)\"\n              class=\"item-action-btn delete\"\n              title=\"Supprimer\"\n            >\n              <i class=\"fas fa-trash\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Bouton charger plus -->\n      <div *ngIf=\"hasMoreNotifications\" class=\"load-more-container\">\n        <button\n          (click)=\"loadMoreNotifications()\"\n          [disabled]=\"isLoadingNotifications\"\n          class=\"load-more-btn\"\n        >\n          <i\n            class=\"fas fa-chevron-down\"\n            [ngClass]=\"{ 'fa-spin': isLoadingNotifications }\"\n          ></i>\n          Charger plus\n        </button>\n      </div>\n    </div>\n\n    <!-- Paramètres de notification -->\n    <div *ngIf=\"showNotificationSettings\" class=\"notification-settings\">\n      <div class=\"settings-header\">\n        <h4>Paramètres de notification</h4>\n        <button\n          (click)=\"toggleNotificationSettings()\"\n          class=\"close-settings-btn\"\n        >\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <div class=\"settings-content\">\n        <div class=\"setting-item\">\n          <label class=\"setting-label\">\n            <input\n              type=\"checkbox\"\n              [(ngModel)]=\"notificationSounds\"\n              (change)=\"saveNotificationSettings()\"\n            />\n            <span class=\"setting-checkmark\"></span>\n            <span class=\"setting-text\">Sons de notification</span>\n          </label>\n        </div>\n\n        <div class=\"setting-item\">\n          <label class=\"setting-label\">\n            <input\n              type=\"checkbox\"\n              [(ngModel)]=\"notificationPreview\"\n              (change)=\"saveNotificationSettings()\"\n            />\n            <span class=\"setting-checkmark\"></span>\n            <span class=\"setting-text\">Aperçu des notifications</span>\n          </label>\n        </div>\n\n        <div class=\"setting-item\">\n          <label class=\"setting-label\">\n            <input\n              type=\"checkbox\"\n              [(ngModel)]=\"autoMarkAsRead\"\n              (change)=\"saveNotificationSettings()\"\n            />\n            <span class=\"setting-checkmark\"></span>\n            <span class=\"setting-text\">Marquer automatiquement comme lu</span>\n          </label>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Panneaux consolidés avec *ngFor -->\n<div\n  *ngFor=\"let panel of getActivePanels()\"\n  class=\"user-status-panel-overlay\"\n  (click)=\"panel.closeAction()\"\n>\n  <div class=\"user-status-panel\" (click)=\"$event.stopPropagation()\">\n    <div class=\"status-panel-header\">\n      <h3>\n        <i [class]=\"panel.icon\"></i> {{ panel.title }}\n        <span *ngIf=\"panel.key === 'userStatus'\">\n          ({{ getOnlineUsersCount() }} en ligne)</span\n        >\n      </h3>\n      <button (click)=\"panel.closeAction()\" class=\"status-panel-btn close-btn\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n\n    <div class=\"status-user-list\">\n      <div class=\"empty-status-state\">\n        <i [class]=\"panel.icon\"></i>\n        <span>Fonctionnalité en cours de développement</span>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal de confirmation de suppression -->\n<div *ngIf=\"showDeleteConfirmModal\" class=\"delete-confirm-modal\">\n  <div class=\"delete-confirm-content\">\n    <div class=\"delete-confirm-header\">\n      <i class=\"fas fa-exclamation-triangle\"></i>\n      <h3>Confirmer la suppression</h3>\n    </div>\n    <div class=\"delete-confirm-body\">\n      <p>\n        Êtes-vous sûr de vouloir supprimer\n        {{ selectedNotifications.size }} notification(s) ?\n      </p>\n      <p class=\"warning-text\">Cette action est irréversible.</p>\n    </div>\n    <div class=\"delete-confirm-actions\">\n      <button (click)=\"cancelDeleteNotifications()\" class=\"cancel-btn\">\n        Annuler\n      </button>\n      <button\n        (click)=\"deleteSelectedNotifications()\"\n        [disabled]=\"isDeletingNotifications\"\n        class=\"confirm-delete-btn\"\n      >\n        <i\n          class=\"fas fa-trash\"\n          [ngClass]=\"{ 'fa-spin': isDeletingNotifications }\"\n        ></i>\n        Supprimer\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Templates réutilisables consolidés -->\n<ng-template #messageInfoTemplate let-message=\"message\">\n  <div class=\"futuristic-message-info\">\n    <span class=\"futuristic-message-time\">\n      {{ formatMessageTime(message?.timestamp) }}\n    </span>\n    <span\n      *ngIf=\"\n        message?.sender?.id === currentUserId ||\n        message?.sender?._id === currentUserId ||\n        message?.senderId === currentUserId\n      \"\n      class=\"futuristic-message-status\"\n    >\n      <i *ngIf=\"message?.isRead\" class=\"fas fa-check-double\"></i>\n      <i *ngIf=\"!message?.isRead\" class=\"fas fa-check\"></i>\n    </span>\n  </div>\n</ng-template>\n\n<ng-template #reactionButtonsTemplate let-message=\"message\">\n  <div\n    *ngIf=\"message.reactions && message.reactions.length > 0\"\n    class=\"flex flex-wrap gap-1 mt-2\"\n  >\n    <button\n      *ngFor=\"let reaction of getUniqueReactions(message)\"\n      (click)=\"message.id && onReactionClick(message.id, reaction.emoji)\"\n      class=\"flex items-center space-x-1 px-2 py-1 rounded-full text-xs transition-all duration-200\"\n      [ngClass]=\"{\n        'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] border border-[#4f5fad]/30 dark:border-[#6d78c9]/30':\n          hasUserReacted(message, reaction.emoji),\n        'bg-[#edf1f4]/50 dark:bg-[#3a3a3a]/50 text-[#6d6870] dark:text-[#a0a0a0] border border-[#edf1f4] dark:border-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10':\n          !hasUserReacted(message, reaction.emoji)\n      }\"\n      [title]=\"reaction.count + ' réaction(s)'\"\n    >\n      <span>{{ reaction.emoji }}</span>\n      <span class=\"font-medium\">{{ reaction.count }}</span>\n    </button>\n  </div>\n</ng-template>\n\n<!-- Template pour les boutons d'action consolidés -->\n<ng-template\n  #actionButtonTemplate\n  let-icon=\"icon\"\n  let-action=\"action\"\n  let-title=\"title\"\n  let-class=\"class\"\n>\n  <button\n    (click)=\"action()\"\n    [class]=\"'item-action-btn ' + (class || '')\"\n    [title]=\"title\"\n  >\n    <i [class]=\"icon\"></i>\n  </button>\n</ng-template>\n", "import { Component, OnDestroy, OnInit } from '@angular/core';\nimport { map, Subscription, BehaviorSubject, Observable } from 'rxjs';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { Conversation, Message } from 'src/app/models/message.model';\nimport { User } from '@app/models/user.model';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { MessageService } from '@app/services/message.service';\nimport { LoggerService } from 'src/app/services/logger.service';\nimport { ThemeService } from '@app/services/theme.service';\n@Component({\n  selector: 'app-messages-list',\n  templateUrl: './messages-list.component.html',\n  styleUrls: ['./messages-list.component.css'],\n})\nexport class MessagesListComponent implements OnInit, OnDestroy {\n  conversations: Conversation[] = [];\n  filteredConversations: Conversation[] = [];\n  loading = true;\n  error: any;\n  currentUserId: string | null = null;\n  searchQuery = '';\n  selectedConversationId: string | null = null;\n  isDarkMode$: Observable<boolean>;\n\n  private unreadCount = new BehaviorSubject<number>(0);\n  public unreadCount$ = this.unreadCount.asObservable();\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private MessageService: MessageService,\n    private authService: AuthuserService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private toastService: ToastService,\n    private logger: LoggerService,\n    private themeService: ThemeService\n  ) {\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n    if (!this.currentUserId) {\n      this.handleError('User not authenticated');\n      return;\n    }\n\n    this.loadConversations();\n    this.subscribeToUserStatus();\n    this.subscribeToConversationUpdates();\n\n    // Check for active conversation from route\n    this.route.firstChild?.params.subscribe((params) => {\n      this.selectedConversationId = params['conversationId'] || null;\n    });\n  }\n\n  loadConversations(): void {\n    this.loading = true;\n    this.error = null;\n\n    const sub = this.MessageService.getConversations().subscribe({\n      next: (conversations) => {\n        this.conversations = Array.isArray(conversations)\n          ? [...conversations]\n          : [];\n\n        this.filterConversations();\n        this.updateUnreadCount();\n        this.sortConversations();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.error = error;\n        this.loading = false;\n        this.toastService.showError('Failed to load conversations');\n      },\n    });\n    this.subscriptions.push(sub);\n  }\n\n  filterConversations(): void {\n    if (!this.searchQuery) {\n      this.filteredConversations = [...this.conversations];\n      return;\n    }\n\n    const query = this.searchQuery.toLowerCase();\n    this.filteredConversations = this.conversations.filter((conv) => {\n      const otherParticipant = conv.participants\n        ? this.getOtherParticipant(conv.participants)\n        : undefined;\n      return (\n        otherParticipant?.username.toLowerCase().includes(query) ||\n        conv.lastMessage?.content?.toLowerCase().includes(query) ||\n        false\n      );\n    });\n  }\n\n  private updateUnreadCount(): void {\n    const count = this.conversations.reduce(\n      (sum, conv) => sum + (conv.unreadCount || 0),\n      0\n    );\n    this.unreadCount.next(count);\n  }\n\n  sortConversations(): void {\n    this.conversations.sort((a, b) => {\n      const dateA = this.getConversationDate(a);\n      const dateB = this.getConversationDate(b);\n      return dateB.getTime() - dateA.getTime();\n    });\n    this.filterConversations();\n  }\n\n  private getConversationDate(conv: Conversation): Date {\n    // Utiliser une date par défaut si aucune date n'est disponible\n    const defaultDate = new Date(0); // 1970-01-01\n\n    if (conv.lastMessage?.timestamp) {\n      return typeof conv.lastMessage.timestamp === 'string'\n        ? new Date(conv.lastMessage.timestamp)\n        : conv.lastMessage.timestamp;\n    }\n\n    if (conv.updatedAt) {\n      return typeof conv.updatedAt === 'string'\n        ? new Date(conv.updatedAt)\n        : conv.updatedAt;\n    }\n\n    if (conv.createdAt) {\n      return typeof conv.createdAt === 'string'\n        ? new Date(conv.createdAt)\n        : conv.createdAt;\n    }\n\n    return defaultDate;\n  }\n\n  getOtherParticipant(participants: User[] | undefined): User | undefined {\n    if (!participants || !Array.isArray(participants)) {\n      return undefined;\n    }\n    return participants.find(\n      (p) => p._id !== this.currentUserId && p.id !== this.currentUserId\n    );\n  }\n\n  subscribeToUserStatus(): void {\n    const sub = this.MessageService.subscribeToUserStatus()\n      .pipe(map((user) => this.MessageService.normalizeUser(user)))\n      .subscribe({\n        next: (user: User) => {\n          if (user) {\n            this.updateUserStatus(user);\n          }\n        },\n        error: (error) => {\n          this.toastService.showError('Connection to status updates lost');\n        },\n      });\n    this.subscriptions.push(sub);\n  }\n\n  subscribeToConversationUpdates(): void {\n    const sub = this.MessageService.subscribeToConversationUpdates(\n      'global'\n    ).subscribe({\n      next: (updatedConv) => {\n        const index = this.conversations.findIndex(\n          (c) => c.id === updatedConv.id\n        );\n        if (index >= 0) {\n          this.conversations[index] = updatedConv;\n        } else {\n          this.conversations.unshift(updatedConv);\n        }\n        this.sortConversations();\n      },\n      error: (error) => {\n        // Handle error silently\n      },\n    });\n    this.subscriptions.push(sub);\n  }\n\n  updateUserStatus(updatedUser: User): void {\n    this.conversations = this.conversations.map((conv) => {\n      if (!conv.participants) {\n        return conv;\n      }\n      const participants = conv.participants.map((p) => {\n        const userIdMatches =\n          p._id === updatedUser._id || p.id === updatedUser._id;\n        return userIdMatches\n          ? {\n              ...p,\n              isOnline: updatedUser.isOnline,\n              lastActive: updatedUser.lastActive,\n            }\n          : p;\n      });\n      return { ...conv, participants };\n    });\n    this.filterConversations();\n  }\n\n  openConversation(conversationId: string | undefined): void {\n    if (!conversationId) {\n      return;\n    }\n\n    this.selectedConversationId = conversationId;\n    this.router.navigate(['chat', conversationId], { relativeTo: this.route });\n  }\n\n  startNewConversation(): void {\n    this.router.navigate(['/messages/users']);\n  }\n\n  formatLastActive(lastActive: string): string {\n    return this.MessageService.formatLastActive(lastActive);\n  }\n\n  private handleError(message: string, error?: any): void {\n    this.logger.error('MessagesListComponent', message, error);\n    this.error = message;\n    this.loading = false;\n    this.toastService.showError(message);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n}\n", "<div\n  class=\"flex h-screen futuristic-messages-page relative\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Sidebar -->\n  <div\n    class=\"w-full md:w-80 lg:w-96 futuristic-sidebar flex flex-col relative z-10 backdrop-blur-sm\"\n  >\n    <!-- Header -->\n    <div class=\"futuristic-header sticky top-0 z-10 backdrop-blur-sm\">\n      <!-- Decorative top border with gradient and glow -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\n      ></div>\n\n      <div class=\"flex justify-between items-center mb-4\">\n        <h1 class=\"futuristic-title text-xl font-bold\">Messages</h1>\n        <div class=\"flex items-center space-x-2\">\n          <button\n            (click)=\"startNewConversation()\"\n            class=\"p-2 rounded-full text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-all relative group overflow-hidden\"\n            title=\"Nouvelle conversation\"\n          >\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-full blur-md\"\n            ></div>\n            <i\n              class=\"fas fa-edit relative z-10 group-hover:scale-110 transition-transform\"\n            ></i>\n          </button>\n          <div *ngIf=\"unreadCount$ | async as count\" class=\"relative\">\n            <span class=\"futuristic-badge\">\n              {{ count }}\n            </span>\n            <!-- Glow effect -->\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30 rounded-full blur-md transform scale-150 -z-10\"\n            ></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Recherche -->\n      <div class=\"relative group\">\n        <input\n          [(ngModel)]=\"searchQuery\"\n          (ngModelChange)=\"filterConversations()\"\n          type=\"text\"\n          placeholder=\"Rechercher des conversations...\"\n          class=\"w-full pl-10 pr-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n        />\n        <div\n          class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n        >\n          <i\n            class=\"fas fa-search text-[#bdc6cc] dark:text-[#6d6870] group-focus-within:text-[#4f5fad] dark:group-focus-within:text-[#6d78c9] transition-colors\"\n          ></i>\n        </div>\n        <div\n          class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\n        >\n          <div\n            class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n          ></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Liste des conversations -->\n    <div class=\"flex-1 overflow-y-auto futuristic-conversations-list\">\n      <!-- État de chargement -->\n      <div\n        *ngIf=\"loading\"\n        class=\"flex flex-col items-center justify-center h-full p-4\"\n      >\n        <div class=\"futuristic-loading-circle\"></div>\n        <p class=\"futuristic-loading-text\">Chargement des conversations...</p>\n      </div>\n\n      <!-- État d'erreur -->\n      <div *ngIf=\"error\" class=\"futuristic-error-container\">\n        <div class=\"futuristic-error-icon\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n        </div>\n        <div class=\"flex-1\">\n          <h3 class=\"futuristic-error-title\">\n            Erreur de chargement des conversations\n          </h3>\n          <p class=\"futuristic-error-message\">\n            {{ error }}\n          </p>\n          <button (click)=\"loadConversations()\" class=\"futuristic-retry-button\">\n            <i class=\"fas fa-sync-alt mr-1.5\"></i> Réessayer\n          </button>\n        </div>\n      </div>\n\n      <!-- État vide -->\n      <div\n        *ngIf=\"!loading && filteredConversations.length === 0 && !searchQuery\"\n        class=\"futuristic-empty-state\"\n      >\n        <div class=\"futuristic-empty-icon\">\n          <i class=\"fas fa-comments\"></i>\n        </div>\n\n        <h3 class=\"futuristic-empty-title\">Aucune conversation</h3>\n\n        <p class=\"futuristic-empty-text\">\n          Démarrez une nouvelle conversation pour communiquer\n        </p>\n\n        <button\n          (click)=\"startNewConversation()\"\n          class=\"futuristic-start-button\"\n        >\n          <i class=\"fas fa-plus-circle mr-2\"></i>\n          Nouvelle Conversation\n        </button>\n      </div>\n\n      <!-- État sans résultats -->\n      <div\n        *ngIf=\"!loading && filteredConversations.length === 0 && searchQuery\"\n        class=\"futuristic-no-results\"\n      >\n        <div class=\"futuristic-empty-icon\">\n          <i class=\"fas fa-search\"></i>\n        </div>\n\n        <h3 class=\"futuristic-empty-title\">Aucun résultat trouvé</h3>\n\n        <p class=\"futuristic-empty-text\">Essayez un autre terme de recherche</p>\n      </div>\n\n      <!-- Conversations -->\n      <ul\n        *ngIf=\"!loading && filteredConversations.length > 0\"\n        class=\"futuristic-conversations\"\n      >\n        <li\n          *ngFor=\"let conv of filteredConversations\"\n          (click)=\"openConversation(conv.id)\"\n          class=\"futuristic-conversation-item\"\n          [ngClass]=\"{\n            'futuristic-conversation-selected':\n              selectedConversationId === conv.id\n          }\"\n        >\n          <div class=\"flex items-center\">\n            <!-- Avatar avec indicateur de statut -->\n            <div class=\"futuristic-avatar\">\n              <img\n                [src]=\"\n                  (conv.participants\n                    ? getOtherParticipant(conv.participants)?.image\n                    : null) || 'assets/images/default-avatar.png'\n                \"\n                alt=\"User avatar\"\n              />\n\n              <!-- Online indicator with glow -->\n              <div\n                *ngIf=\"\n                  conv.participants &&\n                  getOtherParticipant(conv.participants)?.isOnline\n                \"\n                class=\"futuristic-online-indicator\"\n              ></div>\n            </div>\n\n            <!-- Détails de la conversation -->\n            <div class=\"futuristic-conversation-details\">\n              <div class=\"futuristic-conversation-header\">\n                <h3 class=\"futuristic-conversation-name\">\n                  {{\n                    (conv.participants\n                      ? getOtherParticipant(conv.participants)?.username\n                      : null) || \"Utilisateur inconnu\"\n                  }}\n                </h3>\n                <span class=\"futuristic-conversation-time\">\n                  {{ (conv.lastMessage?.timestamp | date : \"shortTime\") || \"\" }}\n                </span>\n              </div>\n\n              <div class=\"futuristic-conversation-preview\">\n                <p class=\"futuristic-conversation-message\">\n                  <span\n                    *ngIf=\"conv.lastMessage?.sender?.id === currentUserId\"\n                    class=\"futuristic-you-prefix\"\n                    >Vous:\n                  </span>\n                  {{ conv.lastMessage?.content || \"Pas encore de messages\" }}\n                </p>\n                <div\n                  *ngIf=\"conv.unreadCount && conv.unreadCount > 0\"\n                  class=\"futuristic-unread-badge\"\n                >\n                  {{ conv.unreadCount }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </li>\n      </ul>\n    </div>\n  </div>\n\n  <!-- Zone de contenu principal -->\n  <div class=\"flex-1 hidden md:flex flex-col futuristic-main-area\">\n    <!-- Router outlet pour le composant de chat -->\n    <router-outlet></router-outlet>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: MessageLayoutComponent,\n    children: [\n      { path: '', redirectTo: 'conversations', pathMatch: 'full' },\n      {\n        path: 'conversations',\n        component: MessagesListComponent,\n        data: { title: 'Conversations' },\n      },\n      {\n        path: 'conversations/chat/:id',\n        component: MessageChatComponent,\n        data: { title: 'Chat' },\n      },\n      {\n        path: 'chat/:id',\n        component: MessageChatComponent,\n        data: { title: 'Chat' },\n      },\n      {\n        path: 'users',\n        component: UserListComponent,\n        data: { title: 'Utilisateurs' },\n      },\n\n      {\n        path: 'new',\n        redirectTo: 'users',\n        pathMatch: 'full',\n      },\n    ],\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class MessagesRoutingModule {}\n", "import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-messages-sidebar',\n  templateUrl: './messages-sidebar.component.html',\n  styleUrls: ['./messages-sidebar.component.css']\n})\nexport class MessagesSidebarComponent {\n\n}\n", "<p>messages-sidebar works!</p>\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\nimport { MessagesRoutingModule } from './messages-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ApolloModule } from 'apollo-angular';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\nimport { MessagesSidebarComponent } from './messages-sidebar/messages-sidebar.component';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { VoiceMessageModule } from 'src/app/components/voice-message/voice-message.module';\n\n@NgModule({\n  declarations: [\n    MessageChatComponent,\n    MessagesListComponent,\n    UserListComponent,\n    MessageLayoutComponent,\n    MessagesSidebarComponent,\n  ],\n  imports: [\n    CommonModule,\n    MessagesRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ApolloModule,\n    RouterModule,\n    VoiceMessageModule,\n  ],\n  providers: [UserStatusService, MessageService],\n})\nexport class MessagesModule {}\n", "// user-list.component.ts\nimport { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Subscription, interval, Observable } from 'rxjs';\nimport { User } from 'src/app/models/user.model';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { CallType } from 'src/app/models/message.model';\nimport { LoggerService } from 'src/app/services/logger.service';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { ThemeService } from '@app/services/theme.service';\n\n@Component({\n  selector: 'app-user-list',\n  templateUrl: './user-list.component.html',\n  styleUrls: ['./user-list.component.css'],\n})\nexport class UserListComponent implements OnInit, OnDestroy {\n  users: User[] = [];\n  loading = true;\n  currentUserId: string | null = null;\n  isDarkMode$: Observable<boolean>;\n\n  // Pagination\n  currentPage = 1;\n  pageSize = 10;\n  totalUsers = 0;\n  totalPages = 0;\n  hasNextPage = false;\n  hasPreviousPage = false;\n\n  // Sorting and filtering\n  sortBy = 'username';\n  sortOrder = 'asc';\n  filterForm = new FormGroup({\n    searchQuery: new FormControl(''),\n    isOnline: new FormControl<boolean | null>(null),\n  });\n\n  // Auto-refresh\n  autoRefreshEnabled = true;\n  autoRefreshInterval = 30000; // 30 seconds\n  private autoRefreshSubscription?: Subscription;\n\n  private loadingMore = false;\n  private subscriptions: Subscription = new Subscription();\n\n  constructor(\n    private MessageService: MessageService,\n    public router: Router,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private toastService: ToastService,\n    private logger: LoggerService,\n    private themeService: ThemeService\n  ) {\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n    this.setupFilterListeners();\n    this.setupAutoRefresh();\n    this.loadUsers();\n  }\n\n  private setupFilterListeners(): void {\n    // Subscribe to search query changes\n    const searchSub = this.filterForm\n      .get('searchQuery')!\n      .valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n\n    this.subscriptions.add(searchSub);\n\n    // Subscribe to online status filter changes\n    const onlineSub = this.filterForm\n      .get('isOnline')!\n      .valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n\n    this.subscriptions.add(onlineSub);\n  }\n\n  private setupAutoRefresh(): void {\n    if (this.autoRefreshEnabled) {\n      this.autoRefreshSubscription = interval(\n        this.autoRefreshInterval\n      ).subscribe(() => {\n        if (!this.loading && !this.filterForm.get('searchQuery')?.value) {\n          this.loadUsers(true);\n        }\n      });\n    }\n  }\n\n  toggleAutoRefresh(): void {\n    this.autoRefreshEnabled = !this.autoRefreshEnabled;\n\n    if (this.autoRefreshEnabled) {\n      this.setupAutoRefresh();\n    } else if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n      this.autoRefreshSubscription = undefined;\n    }\n  }\n\n  resetPagination(): void {\n    this.currentPage = 1;\n  }\n\n  // Get searchQuery from the form\n  get searchQuery(): string {\n    return this.filterForm.get('searchQuery')?.value || '';\n  }\n\n  // Set searchQuery in the form\n  set searchQuery(value: string) {\n    this.filterForm.get('searchQuery')?.setValue(value);\n  }\n\n  // Helper function for template type casting\n  $any(item: any): any {\n    return item;\n  }\n\n  loadUsers(forceRefresh = false): void {\n    if (this.loadingMore) return;\n\n    this.loading = true;\n\n    const searchQuery = this.filterForm.get('searchQuery')?.value || '';\n    const isOnline = this.filterForm.get('isOnline')?.value;\n\n    const sub = this.MessageService.getAllUsers(\n      forceRefresh,\n      searchQuery,\n      this.currentPage,\n      this.pageSize,\n      this.sortBy,\n      this.sortOrder,\n      isOnline === true\n    ).subscribe({\n      next: (users) => {\n        if (!Array.isArray(users)) {\n          this.users = [];\n          this.loading = false;\n          this.loadingMore = false;\n          this.toastService.showError('Failed to load users: Invalid data');\n          return;\n        }\n\n        // If first page, replace users array; otherwise append\n        if (this.currentPage === 1) {\n          // Filter out current user\n          this.users = users.filter((user) => {\n            if (!user) return false;\n            const userId = user.id || user._id;\n            return userId !== this.currentUserId;\n          });\n        } else {\n          // Append new users to existing array, avoiding duplicates and filtering out current user\n          const newUsers = users.filter((newUser) => {\n            if (!newUser) return false;\n            const userId = newUser.id || newUser._id;\n            return (\n              userId !== this.currentUserId &&\n              !this.users.some(\n                (existingUser) =>\n                  (existingUser.id || existingUser._id) === userId\n              )\n            );\n          });\n\n          this.users = [...this.users, ...newUsers];\n        }\n\n        // Update pagination metadata from service\n        const pagination = this.MessageService.currentUserPagination;\n        this.totalUsers = pagination.totalCount;\n        this.totalPages = pagination.totalPages;\n        this.hasNextPage = pagination.hasNextPage;\n        this.hasPreviousPage = pagination.hasPreviousPage;\n\n        this.loading = false;\n        this.loadingMore = false;\n      },\n      error: (error) => {\n        this.loading = false;\n        this.loadingMore = false;\n        this.toastService.showError(\n          `Failed to load users: ${error.message || 'Unknown error'}`\n        );\n\n        if (this.currentPage === 1) {\n          this.users = [];\n        }\n      },\n      complete: () => {\n        this.loading = false;\n        this.loadingMore = false;\n      },\n    });\n\n    this.subscriptions.add(sub);\n  }\n\n  startConversation(userId: string | undefined) {\n    if (!userId) {\n      this.toastService.showError(\n        'Cannot start conversation with undefined user'\n      );\n      return;\n    }\n\n    this.toastService.showInfo('Creating conversation...');\n\n    this.MessageService.createConversation(userId).subscribe({\n      next: (conversation) => {\n        if (!conversation || !conversation.id) {\n          this.toastService.showError(\n            'Failed to create conversation: Invalid response'\n          );\n          return;\n        }\n\n        this.router\n          .navigate(['/messages/conversations/chat', conversation.id])\n          .then((success) => {\n            if (!success) {\n              this.toastService.showError('Failed to open conversation');\n            }\n          });\n      },\n      error: (error) => {\n        this.toastService.showError(\n          `Failed to create conversation: ${error.message || 'Unknown error'}`\n        );\n      },\n    });\n  }\n\n  startAudioCall(userId: string): void {\n    if (!userId) return;\n\n    this.MessageService.initiateCall(userId, CallType.AUDIO).subscribe({\n      next: (call) => {\n        this.toastService.showSuccess('Audio call initiated');\n      },\n      error: (error) => {\n        this.toastService.showError('Failed to initiate audio call');\n      },\n    });\n  }\n\n  startVideoCall(userId: string): void {\n    if (!userId) return;\n\n    this.MessageService.initiateCall(userId, CallType.VIDEO).subscribe({\n      next: (call) => {\n        this.toastService.showSuccess('Video call initiated');\n      },\n      error: (error) => {\n        this.toastService.showError('Failed to initiate video call');\n      },\n    });\n  }\n\n  loadNextPage(): void {\n    if (this.hasNextPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage++;\n      this.loadUsers();\n    }\n  }\n\n  loadPreviousPage(): void {\n    if (this.hasPreviousPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage--;\n      this.loadUsers();\n    }\n  }\n\n  refreshUsers(): void {\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  clearFilters(): void {\n    this.filterForm.reset({\n      searchQuery: '',\n      isOnline: null,\n    });\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  changeSortOrder(field: string): void {\n    if (this.sortBy === field) {\n      // Toggle sort order if clicking the same field\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    } else {\n      // Set new sort field with default ascending order\n      this.sortBy = field;\n      this.sortOrder = 'asc';\n    }\n\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n    if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n    }\n  }\n}\n", "<div\n  class=\"flex flex-col h-full futuristic-users-container\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <!-- Gradient orbs -->\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Additional glow effects for dark mode -->\n    <div\n      class=\"absolute top-[40%] right-[30%] w-40 h-40 rounded-full bg-gradient-to-br from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\n    ></div>\n    <div\n      class=\"absolute bottom-[60%] left-[25%] w-32 h-32 rounded-full bg-gradient-to-tl from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\n    ></div>\n\n    <!-- Grid pattern for light mode -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-0\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n      </div>\n    </div>\n\n    <!-- Horizontal scan line effect for dark mode -->\n    <div class=\"absolute inset-0 opacity-0 dark:opacity-100 overflow-hidden\">\n      <div class=\"h-px w-full bg-[#00f7ff]/20 absolute animate-scan\"></div>\n    </div>\n  </div>\n  <!-- En-tête -->\n  <div class=\"futuristic-users-header\">\n    <div class=\"flex justify-between items-center mb-4\">\n      <h1 class=\"futuristic-title\">Nouvelle Conversation</h1>\n      <div class=\"flex space-x-2\">\n        <button\n          (click)=\"refreshUsers()\"\n          class=\"futuristic-action-button\"\n          title=\"Rafraîchir la liste\"\n        >\n          <i class=\"fas fa-sync-alt\"></i>\n        </button>\n        <button\n          (click)=\"goBackToConversations()\"\n          class=\"futuristic-action-button\"\n        >\n          <i class=\"fas fa-arrow-left\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Recherche et filtres -->\n    <div class=\"space-y-3\">\n      <!-- Recherche -->\n      <div class=\"relative\">\n        <input\n          [ngModel]=\"searchQuery\"\n          (ngModelChange)=\"searchQuery = $event\"\n          type=\"text\"\n          placeholder=\"Rechercher des utilisateurs...\"\n          class=\"w-full pl-10 pr-4 py-2 rounded-lg futuristic-input-field\"\n        />\n        <i\n          class=\"fas fa-search absolute left-3 top-3 text-[#6d6870] dark:text-[#a0a0a0]\"\n        ></i>\n      </div>\n\n      <!-- Filtres -->\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex items-center space-x-4\">\n          <!-- Filtre en ligne -->\n          <div class=\"flex items-center space-x-2\">\n            <label class=\"futuristic-checkbox-container\">\n              <input\n                type=\"checkbox\"\n                id=\"onlineFilter\"\n                class=\"futuristic-checkbox\"\n                [checked]=\"filterForm.get('isOnline')?.value === true\"\n                (change)=\"\n                  filterForm\n                    .get('isOnline')\n                    ?.setValue($any($event.target).checked ? true : null)\n                \"\n              />\n              <span class=\"futuristic-checkbox-checkmark\"></span>\n            </label>\n            <label for=\"onlineFilter\" class=\"futuristic-label\"\n              >En ligne uniquement</label\n            >\n          </div>\n\n          <!-- Options de tri -->\n          <div class=\"flex items-center space-x-2\">\n            <span class=\"futuristic-label\">Trier par:</span>\n            <select\n              (change)=\"changeSortOrder($any($event.target).value)\"\n              class=\"futuristic-select\"\n            >\n              <option [selected]=\"sortBy === 'username'\" value=\"username\">\n                Nom\n              </option>\n              <option [selected]=\"sortBy === 'email'\" value=\"email\">\n                Email\n              </option>\n              <option [selected]=\"sortBy === 'lastActive'\" value=\"lastActive\">\n                Dernière activité\n              </option>\n            </select>\n            <button\n              (click)=\"\n                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';\n                loadUsers(true)\n              \"\n              class=\"futuristic-sort-button\"\n              [title]=\"\n                sortOrder === 'asc' ? 'Ordre croissant' : 'Ordre décroissant'\n              \"\n            >\n              <i\n                [class]=\"\n                  sortOrder === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'\n                \"\n              ></i>\n            </button>\n          </div>\n        </div>\n\n        <!-- Effacer les filtres -->\n        <button (click)=\"clearFilters()\" class=\"futuristic-clear-button\">\n          Effacer les filtres\n        </button>\n      </div>\n\n      <!-- Info pagination -->\n      <div\n        *ngIf=\"totalUsers > 0\"\n        class=\"flex justify-between items-center futuristic-pagination-info\"\n      >\n        <span\n          >Affichage de {{ users.length }} sur\n          {{ totalUsers }} utilisateurs</span\n        >\n        <span>Page {{ currentPage }} sur {{ totalPages }}</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Liste des utilisateurs -->\n  <div\n    class=\"futuristic-users-list\"\n    (scroll)=\"\n      $any($event.target).scrollTop + $any($event.target).clientHeight >=\n        $any($event.target).scrollHeight - 200 && loadNextPage()\n    \"\n  >\n    <!-- État de chargement -->\n    <div *ngIf=\"loading && !users.length\" class=\"futuristic-loading-container\">\n      <div class=\"futuristic-loading-circle\"></div>\n      <div class=\"futuristic-loading-text\">Chargement des utilisateurs...</div>\n    </div>\n\n    <!-- État vide -->\n    <div *ngIf=\"!loading && users.length === 0\" class=\"futuristic-empty-state\">\n      <div class=\"futuristic-empty-icon\">\n        <i class=\"fas fa-users\"></i>\n      </div>\n      <h3 class=\"futuristic-empty-title\">Aucun utilisateur trouvé</h3>\n      <p class=\"futuristic-empty-text\">\n        Essayez un autre terme de recherche ou effacez les filtres\n      </p>\n    </div>\n\n    <!-- Liste des utilisateurs -->\n    <ul *ngIf=\"users.length > 0\" class=\"futuristic-users-grid\">\n      <li *ngFor=\"let user of users\" class=\"futuristic-user-card\">\n        <div\n          class=\"futuristic-user-content\"\n          (click)=\"startConversation(user.id || user._id)\"\n        >\n          <div class=\"futuristic-avatar\">\n            <img\n              [src]=\"user.image || 'assets/images/default-avatar.png'\"\n              alt=\"User avatar\"\n            />\n            <span\n              *ngIf=\"user.isOnline\"\n              class=\"futuristic-online-indicator\"\n            ></span>\n          </div>\n          <div class=\"futuristic-user-info\">\n            <h3 class=\"futuristic-username\">\n              {{ user.username }}\n            </h3>\n            <p class=\"futuristic-user-email\">{{ user.email }}</p>\n          </div>\n        </div>\n\n        <!-- Boutons d'appel -->\n        <div class=\"futuristic-call-buttons\">\n          <button\n            *ngIf=\"user.isOnline\"\n            (click)=\"startAudioCall(user.id || user._id)\"\n            class=\"futuristic-call-button\"\n            title=\"Appel audio\"\n          >\n            <i class=\"fas fa-phone\"></i>\n          </button>\n          <button\n            *ngIf=\"user.isOnline\"\n            (click)=\"startVideoCall(user.id || user._id)\"\n            class=\"futuristic-call-button\"\n            title=\"Appel vidéo\"\n          >\n            <i class=\"fas fa-video\"></i>\n          </button>\n        </div>\n      </li>\n    </ul>\n\n    <!-- Indicateur de chargement supplémentaire -->\n    <div *ngIf=\"loading && users.length > 0\" class=\"futuristic-loading-more\">\n      <div class=\"futuristic-loading-dots\">\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0s\"></div>\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.2s\"></div>\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.4s\"></div>\n      </div>\n      <div class=\"futuristic-loading-text\">\n        Chargement de plus d'utilisateurs...\n      </div>\n    </div>\n\n    <!-- Bouton de chargement supplémentaire -->\n    <div *ngIf=\"hasNextPage && !loading\" class=\"futuristic-load-more-container\">\n      <button (click)=\"loadNextPage()\" class=\"futuristic-load-more-button\">\n        <i class=\"fas fa-chevron-down mr-2\"></i>\n        Charger plus d'utilisateurs\n      </button>\n    </div>\n  </div>\n</div>\n", "import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period = 0, scheduler = asyncScheduler) {\n    if (period < 0) {\n        period = 0;\n    }\n    return timer(period, period, scheduler);\n}\n"], "names": ["BehaviorSubject", "ToastService", "constructor", "toastsSubject", "toasts$", "asObservable", "currentId", "show", "message", "type", "duration", "id", "toast", "currentToasts", "value", "next", "setTimeout", "dismiss", "showSuccess", "showError", "showWarning", "showInfo", "filter", "t", "clear", "factory", "ɵfac", "providedIn", "Validators", "Subscription", "MessageType", "CallType", "switchMap", "distinctUntilChanged", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "otherParticipant", "username", "isOnline", "formatLastActive", "lastActive", "ɵɵclassMap", "action_r38", "badge", "class", "ɵɵproperty", "ɵɵpureFunction1", "_c2", "animate", "count", "ɵɵelementContainerStart", "ɵɵlistener", "MessageChatComponent_ng_container_14_Template_button_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r42", "$implicit", "ɵɵresetView", "onClick", "ɵɵtemplate", "MessageChatComponent_ng_container_14_span_3_Template", "ɵɵelementContainerEnd", "activeClass", "isActive", "ɵɵpureFunction0", "_c3", "title", "icon", "MessageChatComponent_div_19_button_8_Template_button_click_0_listener", "_r46", "status_r44", "ctx_r45", "ɵɵnextContext", "updateUserStatus", "key", "toggleStatusSelector", "ctx_r43", "isUpdatingStatus", "_c4", "currentUserStatus", "color", "ɵɵtextInterpolate", "label", "description", "MessageChatComponent_div_19_button_8_Template", "MessageChatComponent_div_19_Template_button_click_10_listener", "_r48", "ctx_r47", "toggleUserStatusPanel", "ctx_r4", "getStatusColor", "getStatusText", "getStatusOptions", "getOnlineUsersCount", "MessageChatComponent_div_23_a_4_Template_a_click_0_listener", "_r52", "theme_r50", "ctx_r51", "changeTheme", "hoverColor", "MessageChatComponent_div_23_a_4_Template", "ctx_r5", "getThemeOptions", "MessageChatComponent_div_27_Template_button_click_4_listener", "_r54", "ctx_r53", "clearConversation", "MessageChatComponent_div_27_Template_button_click_9_listener", "ctx_r55", "exportConversation", "MessageChatComponent_div_27_Template_button_click_14_listener", "ctx_r56", "toggleConversationInfo", "MessageChatComponent_div_27_Template_button_click_19_listener", "ctx_r57", "toggleConversationSettings", "MessageChatComponent_div_28_button_7_Template_button_click_0_listener", "_r63", "ctx_r62", "clearSearch", "MessageChatComponent_div_28_div_10_button_4_Template_button_click_0_listener", "_r67", "result_r65", "ctx_r66", "navigateToMessage", "ctx_r64", "formatMessageTime", "timestamp", "highlightSearchTerms", "content", "searchQuery", "ɵɵsanitizeHtml", "MessageChatComponent_div_28_div_10_button_4_Template", "ctx_r60", "searchResults", "length", "ctx_r61", "MessageChatComponent_div_28_Template_input_ngModelChange_5_listener", "$event", "_r69", "ctx_r68", "MessageChatComponent_div_28_Template_input_input_5_listener", "ctx_r70", "onSearchInput", "MessageChatComponent_div_28_Template_input_keydown_5_listener", "ctx_r71", "onSearchKeyPress", "MessageChatComponent_div_28_div_6_Template", "MessageChatComponent_div_28_button_7_Template", "MessageChatComponent_div_28_Template_button_click_8_listener", "ctx_r72", "toggleSearchBar", "MessageChatComponent_div_28_div_10_Template", "MessageChatComponent_div_28_div_11_Template", "ctx_r7", "isSearching", "searchMode", "pinnedMessage_r76", "MessageChatComponent_div_29_div_10_button_1_Template_button_click_0_listener", "_r82", "ctx_r81", "scrollToPinnedMessage", "MessageChatComponent_div_29_div_10_button_1_span_11_Template", "MessageChatComponent_div_29_div_10_button_1_span_12_Template", "MessageChatComponent_div_29_div_10_button_1_span_13_Template", "sender", "image", "ɵɵsanitizeUrl", "ctx_r75", "hasImage", "isVoiceMessage", "pinnedAt", "formatMessageDate", "MessageChatComponent_div_29_div_10_button_1_Template", "ctx_r74", "pinnedMessages", "MessageChatComponent_div_29_Template_button_click_6_listener", "_r84", "ctx_r83", "togglePinnedMessages", "MessageChatComponent_div_29_div_9_Template", "MessageChatComponent_div_29_div_10_Template", "ctx_r8", "getPinnedMessagesCount", "ctx_r13", "error", "ctx_r88", "message_r86", "MessageChatComponent_ng_container_36_div_1_button_3_Template_button_click_0_listener", "_r98", "ctx_r96", "toggleReactionPicker", "MessageChatComponent_ng_container_36_div_1_div_4_button_2_Template_button_click_0_listener", "_r103", "emoji_r100", "ctx_r101", "reactToMessage", "MessageChatComponent_ng_container_36_div_1_div_4_button_2_Template", "ctx_r90", "availableReactions", "MessageChatComponent_ng_container_36_div_1_div_7_button_1_Template_button_click_0_listener", "_r115", "ctx_r113", "toggleMessageOptions", "MessageChatComponent_ng_container_36_div_1_div_7_div_2_button_13_Template_button_click_0_listener", "_r120", "ctx_r118", "startEditMessage", "MessageChatComponent_ng_container_36_div_1_div_7_div_2_button_14_Template_button_click_0_listener", "_r123", "ctx_r121", "showDeleteConfirmation", "MessageChatComponent_ng_container_36_div_1_div_7_div_2_Template_button_click_1_listener", "_r126", "ctx_r124", "startReplyToMessage", "MessageChatComponent_ng_container_36_div_1_div_7_div_2_Template_button_click_5_listener", "ctx_r127", "openForwardModal", "MessageChatComponent_ng_container_36_div_1_div_7_div_2_Template_button_click_9_listener", "ctx_r129", "showPinConfirmation", "MessageChatComponent_ng_container_36_div_1_div_7_div_2_button_13_Template", "MessageChatComponent_ng_container_36_div_1_div_7_div_2_button_14_Template", "ctx_r106", "getPinIcon", "getPinDisplayText", "canEditMessage", "MessageChatComponent_ng_container_36_div_1_div_7_div_3_Template_button_click_4_listener", "_r134", "ctx_r132", "cancelDeleteMessage", "MessageChatComponent_ng_container_36_div_1_div_7_div_3_Template_button_click_6_listener", "ctx_r135", "confirmDeleteMessage", "MessageChatComponent_ng_container_36_div_1_div_7_div_4_Template_button_click_4_listener", "_r140", "ctx_r138", "cancelPinConfirmation", "MessageChatComponent_ng_container_36_div_1_div_7_div_4_Template_button_click_6_listener", "ctx_r141", "togglePinMessage", "MessageChatComponent_ng_container_36_div_1_div_7_div_4_i_7_Template", "ctx_r108", "isMessagePinned", "isPinning", "replyTo", "MessageChatComponent_ng_container_36_div_1_div_7_div_5_div_1_Template", "MessageChatComponent_ng_container_36_div_1_div_7_div_5_div_2_Template", "MessageChatComponent_ng_container_36_div_1_div_7_div_5_span_3_Template", "MessageChatComponent_ng_container_36_div_1_div_7_div_5_span_4_Template", "MessageChatComponent_ng_container_36_div_1_div_7_div_5_span_5_Template", "ctx_r109", "isDeleted", "isEdited", "MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template_textarea_ngModelChange_1_listener", "_r154", "ctx_r153", "<PERSON><PERSON><PERSON><PERSON>", "MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template_textarea_keydown_1_listener", "ctx_r155", "onEditKeyPress", "MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template_button_click_3_listener", "ctx_r157", "cancelEditMessage", "MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template_button_click_5_listener", "ctx_r158", "saveEditMessage", "ctx_r110", "ɵɵelementContainer", "MessageChatComponent_ng_container_36_div_1_div_7_Template_div_mouseenter_0_listener", "_r162", "ctx_r160", "MessageChatComponent_ng_container_36_div_1_div_7_Template_div_mouseleave_0_listener", "ctx_r163", "MessageChatComponent_ng_container_36_div_1_div_7_button_1_Template", "MessageChatComponent_ng_container_36_div_1_div_7_div_2_Template", "MessageChatComponent_ng_container_36_div_1_div_7_div_3_Template", "MessageChatComponent_ng_container_36_div_1_div_7_div_4_Template", "MessageChatComponent_ng_container_36_div_1_div_7_div_5_Template", "MessageChatComponent_ng_container_36_div_1_div_7_div_6_Template", "MessageChatComponent_ng_container_36_div_1_div_7_ng_container_7_Template", "MessageChatComponent_ng_container_36_div_1_div_7_ng_container_8_Template", "ɵɵpureFunction4", "_c5", "isPending", "isError", "ctx_r92", "showMessageOptions", "showDeleteConfirm", "showPinConfirm", "editingMessageId", "_r34", "_c6", "_r32", "ɵɵstyleProp", "ctx_r166", "getVoiceBarHeight", "i_r169", "MessageChatComponent_ng_container_36_div_1_div_8_div_4_Template", "MessageChatComponent_ng_container_36_div_1_div_8_ng_container_8_Template", "MessageChatComponent_ng_container_36_div_1_div_8_ng_container_9_Template", "_c7", "ctx_r93", "currentUserId", "_id", "senderId", "_c8", "formatVoiceDuration", "getVoiceMessageDuration", "getVoiceMessageUrl", "MessageChatComponent_ng_container_36_div_1_div_9_ng_container_6_Template", "MessageChatComponent_ng_container_36_div_1_div_9_ng_container_7_Template", "ɵɵpureFunction3", "_c9", "ctx_r94", "getImageUrl", "MessageChatComponent_ng_container_36_div_1_div_1_Template", "MessageChatComponent_ng_container_36_div_1_button_3_Template", "MessageChatComponent_ng_container_36_div_1_div_4_Template", "MessageChatComponent_ng_container_36_div_1_div_5_Template", "MessageChatComponent_ng_container_36_div_1_div_7_Template", "MessageChatComponent_ng_container_36_div_1_div_8_Template", "MessageChatComponent_ng_container_36_div_1_div_9_Template", "ɵɵattribute", "ctx_r85", "shouldShowDateHeader", "i_r87", "ɵɵpureFunction2", "_c10", "showReactionPicker", "MessageChatComponent_ng_container_36_div_1_Template", "ctx_r14", "messages", "MessageChatComponent_ng_template_37_div_0_Template_button_click_7_listener", "_r176", "ctx_r175", "tmp_b_0", "messageForm", "get", "setValue", "MessageChatComponent_ng_template_37_div_0_Template", "ctx_r16", "loading", "ctx_r17", "MessageChatComponent_div_41_Template_button_click_2_listener", "_r178", "ctx_r177", "removeAttachment", "ctx_r18", "previewUrl", "MessageChatComponent_div_42_button_15_Template_button_click_0_listener", "_r182", "emoji_r180", "ctx_r181", "insert<PERSON><PERSON><PERSON>", "MessageChatComponent_div_42_button_15_Template", "ctx_r19", "commonEmojis", "MessageChatComponent_div_43_Template_button_click_7_listener", "_r184", "ctx_r183", "cancelReply", "ctx_r20", "replyingToMessage", "MessageChatComponent_app_voice_recorder_52_Template_app_voice_recorder_recordingComplete_0_listener", "_r186", "ctx_r185", "onVoiceRecordingComplete", "MessageChatComponent_app_voice_recorder_52_Template_app_voice_recorder_recordingCancelled_0_listener", "ctx_r187", "onVoiceRecordingCancelled", "MessageChatComponent_input_53_Template_input_input_0_listener", "_r189", "ctx_r188", "onTyping", "MessageChatComponent_button_54_Template_button_click_0_listener", "_r191", "ctx_r190", "toggleVoiceRecording", "MessageChatComponent_button_55_i_1_Template", "MessageChatComponent_button_55_i_2_Template", "ctx_r25", "isUploading", "invalid", "selectedFile", "MessageChatComponent_div_56_Template_button_click_10_listener", "_r195", "ctx_r194", "rejectCall", "MessageChatComponent_div_56_Template_button_click_14_listener", "ctx_r196", "acceptCall", "ctx_r26", "incomingCall", "caller", "MessageChatComponent_div_57_div_1_div_5_Template", "ctx_r197", "isVideoEnabled", "ctx_r198", "activeCall", "recipient", "formatCallDuration", "callDuration", "_c11", "callQuality", "ctx_r204", "MessageChatComponent_div_57_div_3_button_5_Template_button_click_0_listener", "_r207", "ctx_r206", "toggleCallVideo", "_c12", "ctx_r205", "MessageChatComponent_div_57_div_3_div_1_Template", "MessageChatComponent_div_57_div_3_Template_button_click_3_listener", "_r209", "ctx_r208", "toggleCallMute", "MessageChatComponent_div_57_div_3_button_5_Template", "MessageChatComponent_div_57_div_3_Template_button_click_6_listener", "ctx_r210", "endCall", "MessageChatComponent_div_57_div_3_Template_button_click_8_listener", "ctx_r211", "toggleCallMinimize", "_c13", "ctx_r199", "showCallControls", "isCallMuted", "isCallMinimized", "MessageChatComponent_div_57_div_4_Template_button_click_7_listener", "_r213", "ctx_r212", "MessageChatComponent_div_57_div_4_Template_button_click_9_listener", "ctx_r214", "ctx_r200", "MessageChatComponent_div_57_Template_div_mousemove_0_listener", "_r216", "ctx_r215", "onCallMouseMove", "MessageChatComponent_div_57_div_1_Template", "MessageChatComponent_div_57_div_2_Template", "MessageChatComponent_div_57_div_3_Template", "MessageChatComponent_div_57_div_4_Template", "_c14", "ctx_r27", "MessageChatComponent_div_58_button_20_Template_button_click_0_listener", "_r227", "ctx_r226", "selectAllConversations", "MessageChatComponent_div_58_button_21_Template_button_click_0_listener", "_r229", "ctx_r228", "deselectAllConversations", "conversation_r231", "participants", "MessageChatComponent_div_58_div_26_button_1_Template_button_click_0_listener", "_r236", "ctx_r235", "toggleConversationSelection", "MessageChatComponent_div_58_div_26_button_1_i_3_Template", "MessageChatComponent_div_58_div_26_button_1_span_11_Template", "_c15", "ctx_r230", "isConversationSelected", "getConversationDisplayImage", "getConversationDisplayName", "isGroup", "MessageChatComponent_div_58_div_26_button_1_Template", "ctx_r222", "availableConversations", "MessageChatComponent_div_58_Template_div_click_0_listener", "_r238", "ctx_r237", "closeForwardModal", "MessageChatComponent_div_58_Template_div_click_1_listener", "stopPropagation", "MessageChatComponent_div_58_Template_button_click_5_listener", "ctx_r240", "MessageChatComponent_div_58_div_13_Template", "MessageChatComponent_div_58_div_14_Template", "MessageChatComponent_div_58_button_20_Template", "MessageChatComponent_div_58_button_21_Template", "MessageChatComponent_div_58_div_25_Template", "MessageChatComponent_div_58_div_26_Template", "MessageChatComponent_div_58_div_27_Template", "MessageChatComponent_div_58_Template_button_click_29_listener", "ctx_r241", "MessageChatComponent_div_58_Template_button_click_31_listener", "ctx_r242", "forwardMessage", "MessageChatComponent_div_58_i_32_Template", "MessageChatComponent_div_58_i_33_Template", "ctx_r28", "forwardingMessage", "areAllConversationsSelected", "selectedConversations", "isLoadingConversations", "isForwarding", "ctx_r243", "unreadNotificationCount", "MessageChatComponent_div_59_div_23_div_7_Template_button_click_1_listener", "_r254", "ctx_r253", "markSelectedAsRead", "MessageChatComponent_div_59_div_23_div_7_Template_button_click_4_listener", "ctx_r255", "showDeleteSelectedConfirmation", "ctx_r252", "isMarkingAsRead", "isDeletingNotifications", "MessageChatComponent_div_59_div_23_Template_input_change_3_listener", "_r257", "ctx_r256", "toggleSelectAllNotifications", "MessageChatComponent_div_59_div_23_div_7_Template", "ctx_r244", "areAllNotificationsSelected", "selectedNotifications", "size", "MessageChatComponent_div_59_button_25_Template_button_click_0_listener", "_r259", "ctx_r258", "markAllAsRead", "ctx_r245", "MessageChatComponent_div_59_button_26_Template_button_click_0_listener", "_r261", "ctx_r260", "deleteAllNotifications", "ctx_r246", "_c16", "notification_r262", "MessageChatComponent_div_59_div_30_button_17_Template_button_click_0_listener", "_r268", "ctx_r267", "add", "MessageChatComponent_div_59_div_30_Template_input_change_2_listener", "_r271", "ctx_r270", "toggleNotificationSelection", "MessageChatComponent_div_59_div_30_span_9_Template", "MessageChatComponent_div_59_div_30_span_15_Template", "MessageChatComponent_div_59_div_30_button_17_Template", "MessageChatComponent_div_59_div_30_Template_button_click_18_listener", "ctx_r272", "deleteNotification", "_c17", "isRead", "ctx_r249", "has", "getNotificationColor", "getNotificationIcon", "formatNotificationDate", "MessageChatComponent_div_59_div_31_Template_button_click_1_listener", "_r274", "ctx_r273", "loadMoreNotifications", "ctx_r250", "isLoadingNotifications", "MessageChatComponent_div_59_div_32_Template_button_click_4_listener", "_r276", "ctx_r275", "toggleNotificationSettings", "MessageChatComponent_div_59_div_32_Template_input_ngModelChange_9_listener", "ctx_r277", "notificationSounds", "MessageChatComponent_div_59_div_32_Template_input_change_9_listener", "ctx_r278", "saveNotificationSettings", "MessageChatComponent_div_59_div_32_Template_input_ngModelChange_15_listener", "ctx_r279", "notificationPreview", "MessageChatComponent_div_59_div_32_Template_input_change_15_listener", "ctx_r280", "MessageChatComponent_div_59_div_32_Template_input_ngModelChange_21_listener", "ctx_r281", "autoMarkAsRead", "MessageChatComponent_div_59_div_32_Template_input_change_21_listener", "ctx_r282", "ctx_r251", "MessageChatComponent_div_59_span_7_Template", "MessageChatComponent_div_59_Template_button_click_9_listener", "_r284", "ctx_r283", "MessageChatComponent_div_59_Template_button_click_11_listener", "ctx_r285", "loadNotifications", "MessageChatComponent_div_59_Template_button_click_13_listener", "ctx_r286", "toggleNotificationPanel", "MessageChatComponent_div_59_Template_button_click_17_listener", "ctx_r287", "setNotificationFilter", "MessageChatComponent_div_59_Template_button_click_19_listener", "ctx_r288", "MessageChatComponent_div_59_Template_button_click_21_listener", "ctx_r289", "MessageChatComponent_div_59_div_23_Template", "MessageChatComponent_div_59_button_25_Template", "MessageChatComponent_div_59_button_26_Template", "MessageChatComponent_div_59_div_28_Template", "MessageChatComponent_div_59_div_29_Template", "MessageChatComponent_div_59_div_30_Template", "MessageChatComponent_div_59_div_31_Template", "MessageChatComponent_div_59_div_32_Template", "ctx_r29", "notificationFilter", "getFilteredNotifications", "notifications", "trackByNotificationId", "hasMoreNotifications", "showNotificationSettings", "ctx_r291", "MessageChatComponent_div_60_Template_div_click_0_listener", "_r293", "panel_r290", "closeAction", "MessageChatComponent_div_60_Template_div_click_1_listener", "MessageChatComponent_div_60_span_6_Template", "MessageChatComponent_div_60_Template_button_click_7_listener", "MessageChatComponent_div_61_Template_button_click_12_listener", "_r297", "ctx_r296", "cancelDeleteNotifications", "MessageChatComponent_div_61_Template_button_click_14_listener", "ctx_r298", "deleteSelectedNotifications", "ctx_r31", "MessageChatComponent_ng_template_62_span_3_i_1_Template", "MessageChatComponent_ng_template_62_span_3_i_2_Template", "message_r299", "MessageChatComponent_ng_template_62_span_3_Template", "ctx_r33", "MessageChatComponent_ng_template_64_div_0_button_1_Template_button_click_0_listener", "_r310", "reaction_r307", "message_r304", "ctx_r308", "onReactionClick", "emoji", "_c18", "ctx_r306", "hasUserReacted", "MessageChatComponent_ng_template_64_div_0_button_1_Template", "ctx_r305", "getUniqueReactions", "MessageChatComponent_ng_template_64_div_0_Template", "reactions", "MessageChatComponent_ng_template_66_Template_button_click_0_listener", "_r318", "action_r314", "action", "class_r316", "title_r315", "icon_r313", "MessageChatComponent", "MessageService", "getCommonEmojis", "getHeaderActions", "initiateCall", "showSearchBar", "showPinnedMessages", "showNotificationPanel", "toggleCallHistoryPanel", "showCallHistoryPanel", "toggleCallStatsPanel", "showCallStatsPanel", "toggleVoiceMessagesPanel", "showVoiceMessagesPanel", "voiceMessages", "route", "authService", "fb", "statusService", "router", "toastService", "logger", "cdr", "conversation", "currentUsername", "isTyping", "isRecordingVoice", "voiceRecordingDuration", "MAX_MESSAGES_PER_SIDE", "MAX_MESSAGES_TO_LOAD", "MAX_TOTAL_MESSAGES", "currentPage", "isLoadingMore", "hasMoreMessages", "subscriptions", "selectedTheme", "showThemeSelector", "showMainMenu", "showEmojiPicker", "showCallModal", "showActiveCallModal", "callTimer", "Set", "showDeleteConfirmModal", "showStatusSelector", "showForwardModal", "onlineUsers", "Map", "lastActivityTime", "Date", "autoAwayTimeout", "showUserStatusPanel", "callHistory", "autoAwayDelay", "localVideoElement", "remoteVideoElement", "statusFilterType", "isCurrentlyTyping", "TYPING_DELAY", "TYPING_TIMEOUT", "toggleMethods", "themeSelector", "togglePanel", "mainMenu", "emojiPicker", "theme", "localStorage", "setItem", "searchBar", "statusSelector", "notificationSettings", "userStatusPanel", "callMinimize", "callHistoryPanel", "callStatsPanel", "voiceMessagesPanel", "toggleThemeSelector", "toggleMainMenu", "toggleEmojiPicker", "conversationMethods", "showSettings", "serviceMethods", "index", "getMessageType", "seconds", "getMessageTypeClass", "callMethods", "toggleMute", "updateMedia", "toggleVideo", "toggleMinimize", "toggleMedia", "subscribe", "updateCallMedia", "timerMethods", "startCall", "setInterval", "stopCall", "clearInterval", "resetCall", "startCallTimerMethod", "stopCallTimerMethod", "resetCallStateMethod", "notificationUtilMethods", "loadMore", "updateCount", "n", "getFiltered", "setFilter", "updateNotificationCount", "notificationConfig", "NEW_MESSAGE", "FRIEND_REQUEST", "GROUP_INVITATION", "CALL_MISSED", "CALL_INCOMING", "SYSTEM", "notification", "statusConfig", "online", "text", "offline", "away", "busy", "themeConfig", "panelConfig", "statusMethods", "getText", "status", "getColor", "getIcon", "formatLastSeen", "getOnlineCount", "Array", "from", "values", "user", "trackById", "toString", "getStatusIcon", "trackByUserId", "messageMethods", "isPinned", "canEdit", "startEdit", "cancelEdit", "saveEdit", "messageId", "event", "shift<PERSON>ey", "preventDefault", "togglePin", "callUtils", "colors", "COMPLETED", "MISSED", "REJECTED", "getTypeIcon", "formatDuration", "minutes", "Math", "floor", "padStart", "formatDate", "call", "getCallStatusColor", "getCallTypeIcon", "formatCallDate", "trackByCallId", "utilityMethods", "map", "c", "trackByMessageId", "conversationUtilMethods", "toggleSelection", "conversationId", "indexOf", "splice", "push", "isSelected", "includes", "getDisplayImage", "getDisplayName", "name", "forward", "notificationMethods", "saveSettings", "cancelDelete", "searchMethods", "onInput", "target", "perform", "onKeyPress", "m", "toLowerCase", "performSearch", "confirmationMethods", "showPin", "cancelPin", "showDelete", "confirmDelete", "finalUtilityMethods", "query", "regex", "RegExp", "replace", "reactionMethods", "togglePicker", "reactTo", "toggleOptions", "group", "max<PERSON><PERSON><PERSON>", "ngOnInit", "getCurrentUserId", "savedTheme", "getItem", "subscribeToNotifications", "subscribeToUserStatus", "initializeUserStatus", "startActivityTracking", "document", "addEventListener", "onDocumentClick", "bind", "routeSub", "params", "pipe", "getConversation", "handleConversationLoaded", "handleError", "resetLoading", "handleSuccess", "callback", "getFileIcon", "mimeType", "getFileType", "find", "p", "conversationMessages", "sort", "a", "b", "timeA", "getTime", "timeB", "scrollToBottom", "markMessagesAsRead", "subscribeToConversationUpdates", "subscribeToNewMessages", "subscribeToTypingIndicators", "sub", "updatedConversation", "newMessage", "markMessageAsRead", "subscribeToTypingIndicator", "userId", "clearTimeout", "typingTimeout", "unreadMessages", "msg", "receiver", "for<PERSON>ach", "onFileSelected", "file", "files", "validTypes", "reader", "FileReader", "onload", "result", "readAsDataURL", "fileInput", "nativeElement", "typingTimer", "startTyping", "stopTyping", "panelName", "closeOthers", "panels", "menu", "search", "currentPanel", "Object", "panel", "confirm", "conversation<PERSON>ame", "groupName", "exportData", "createdAt", "exportedAt", "toISOString", "exportedBy", "blob", "Blob", "JSON", "stringify", "url", "window", "URL", "createObjectURL", "link", "createElement", "href", "safeFileName", "dateStr", "split", "download", "click", "revokeObjectURL", "sendMessage", "stopTypingIndicator", "tempMessage", "fileType", "startsWith", "attachments", "IMAGE", "FILE", "fileToSend", "reset", "sendSub", "undefined", "TEXT", "onScroll", "container", "scrollTop", "showLoadingIndicator", "oldScrollHeight", "scrollHeight", "firstVisibleMessage", "getFirstVisibleMessage", "loadMoreMessages", "requestAnimationFrame", "preserveScrollPosition", "messageElement", "findMessageElement", "scrollIntoView", "block", "newScrollHeight", "scrollDiff", "hideLoadingIndicator", "messagesContainer", "messageElements", "querySelectorAll", "i", "element", "rect", "getBoundingClientRect", "top", "bottom", "clientHeight", "getAttribute", "querySelector", "getElementById", "indicator", "className", "innerHTML", "prepend", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "oldMessages", "existingIds", "newMessages", "slice", "isSameTimestamp", "timestamp1", "timestamp2", "time1", "time2", "abs", "force", "isScrolledToBottom", "scrollTo", "behavior", "err", "audioBlob", "receiverId", "sendVoiceMessage", "openImageFullscreen", "imageUrl", "open", "ngAfterViewChecked", "some", "VOICE_MESSAGE", "detectChanges", "goBackToConversations", "navigate", "control", "currentValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputElement", "focus", "notificationSub", "subscribeToNewNotifications", "unshift", "play", "mark<PERSON><PERSON><PERSON>", "notificationsListSub", "notifications$", "notificationCountSub", "notificationCount$", "callSub", "incomingCall$", "activeCallSub", "activeCall$", "localStreamSub", "localStream$", "stream", "srcObject", "remoteStreamSub", "remoteStream$", "AUDIO", "VIDEO", "unsubscribe", "refresh", "loadSub", "getNotifications", "notificationId", "delete", "filteredNotifications", "allSelected", "every", "selectedIds", "mark<PERSON>ub", "readAt", "readCount", "unreadNotifications", "unreadIds", "deleteSub", "deleteMultipleNotifications", "deleteAllSub", "statusSub", "handleUserStatusUpdate", "set", "setOnlineSub", "setUserOnline", "events", "onUserActivity", "checkAndUpdateStatus", "now", "timeSinceLastActivity", "previousStatus", "updateObservable", "setUserOffline", "updateSub", "statusText", "loadOnlineUsers", "usersSub", "getAllUsers", "users", "entries", "config", "getActivePanels", "setStatus<PERSON>ilter", "getFilteredUsers", "closest", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "i2", "ActivatedRoute", "i3", "AuthuserService", "i4", "FormBuilder", "i5", "UserStatusService", "Router", "i6", "i7", "LoggerService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_Template_button_click_6_listener", "MessageChatComponent_span_11_Template", "MessageChatComponent_div_12_Template", "MessageChatComponent_ng_container_14_Template", "MessageChatComponent_Template_button_click_16_listener", "MessageChatComponent_span_18_Template", "MessageChatComponent_div_19_Template", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_div_23_Template", "MessageChatComponent_Template_button_click_25_listener", "MessageChatComponent_div_27_Template", "MessageChatComponent_div_28_Template", "MessageChatComponent_div_29_Template", "MessageChatComponent_Template_div_scroll_30_listener", "MessageChatComponent_div_32_Template", "MessageChatComponent_div_33_Template", "MessageChatComponent_div_34_Template", "MessageChatComponent_div_35_Template", "MessageChatComponent_ng_container_36_Template", "MessageChatComponent_ng_template_37_Template", "ɵɵtemplateRefExtractor", "MessageChatComponent_div_39_Template", "MessageChatComponent_div_41_Template", "MessageChatComponent_div_42_Template", "MessageChatComponent_div_43_Template", "MessageChatComponent_Template_form_ngSubmit_44_listener", "MessageChatComponent_Template_button_click_46_listener", "MessageChatComponent_Template_button_click_48_listener", "_r319", "_r21", "ɵɵreference", "MessageChatComponent_Template_input_change_50_listener", "MessageChatComponent_app_voice_recorder_52_Template", "MessageChatComponent_input_53_Template", "MessageChatComponent_button_54_Template", "MessageChatComponent_button_55_Template", "MessageChatComponent_div_56_Template", "MessageChatComponent_div_57_Template", "MessageChatComponent_div_58_Template", "MessageChatComponent_div_59_Template", "MessageChatComponent_div_60_Template", "MessageChatComponent_div_61_Template", "MessageChatComponent_ng_template_62_Template", "MessageChatComponent_ng_template_64_Template", "MessageChatComponent_ng_template_66_Template", "_c19", "_c20", "_r15", "tmp_29_0", "tmp_30_0", "count_r6", "MessagesListComponent_div_39_Template_button_click_8_listener", "_r8", "loadConversations", "ctx_r2", "MessagesListComponent_div_40_Template_button_click_7_listener", "_r10", "ctx_r9", "startNewConversation", "conv_r12", "unreadCount", "MessagesListComponent_ul_42_li_1_Template_li_click_0_listener", "_r18", "openConversation", "MessagesListComponent_ul_42_li_1_div_4_Template", "MessagesListComponent_ul_42_li_1_span_14_Template", "MessagesListComponent_ul_42_li_1_div_16_Template", "_c0", "ctx_r11", "selectedConversationId", "tmp_1_0", "getOtherParticipant", "tmp_2_0", "tmp_3_0", "ɵɵpipeBind2", "lastMessage", "MessagesListComponent_ul_42_li_1_Template", "filteredConversations", "MessagesListComponent", "themeService", "conversations", "unreadCount$", "isDarkMode$", "darkMode$", "<PERSON><PERSON><PERSON><PERSON>", "getConversations", "isArray", "filterConversations", "updateUnreadCount", "sortConversations", "conv", "reduce", "sum", "dateA", "getConversationDate", "dateB", "defaultDate", "updatedAt", "normalizeUser", "updatedConv", "findIndex", "updatedUser", "userIdMatches", "relativeTo", "ThemeService", "decls", "vars", "consts", "template", "MessagesListComponent_Template", "MessagesListComponent_Template_button_click_26_listener", "MessagesListComponent_div_29_Template", "MessagesListComponent_Template_input_ngModelChange_32_listener", "MessagesListComponent_div_38_Template", "MessagesListComponent_div_39_Template", "MessagesListComponent_div_40_Template", "MessagesListComponent_div_41_Template", "MessagesListComponent_ul_42_Template", "ɵɵclassProp", "ɵɵpipeBind1", "RouterModule", "UserListComponent", "MessageLayoutComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "data", "MessagesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "MessagesSidebarComponent", "MessagesSidebarComponent_Template", "CommonModule", "FormsModule", "ReactiveFormsModule", "ApolloModule", "VoiceMessageModule", "MessagesModule", "declarations", "interval", "FormControl", "FormGroup", "ɵɵtextInterpolate2", "ctx_r0", "totalUsers", "totalPages", "UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener", "_r13", "user_r7", "startAudioCall", "UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener", "_r16", "startVideoCall", "UserListComponent_ul_61_li_1_Template_div_click_1_listener", "startConversation", "UserListComponent_ul_61_li_1_span_4_Template", "UserListComponent_ul_61_li_1_button_11_Template", "UserListComponent_ul_61_li_1_button_12_Template", "email", "UserListComponent_ul_61_li_1_Template", "ctx_r3", "UserListComponent_div_63_Template_button_click_1_listener", "_r20", "loadNextPage", "pageSize", "hasNextPage", "hasPreviousPage", "sortBy", "sortOrder", "filterForm", "autoRefreshEnabled", "autoRefreshInterval", "loadingMore", "setupFilterListeners", "setupAutoRefresh", "loadUsers", "searchSub", "valueChanges", "resetPagination", "onlineSub", "autoRefreshSubscription", "toggleAutoRefresh", "$any", "item", "forceRefresh", "newUsers", "newUser", "existingUser", "pagination", "currentUserPagination", "totalCount", "complete", "createConversation", "then", "success", "loadPreviousPage", "refreshUsers", "clearFilters", "changeSortOrder", "field", "UserListComponent_Template", "UserListComponent_Template_button_click_27_listener", "UserListComponent_Template_button_click_29_listener", "UserListComponent_Template_input_ngModelChange_33_listener", "UserListComponent_Template_input_change_39_listener", "checked", "UserListComponent_Template_select_change_46_listener", "UserListComponent_Template_button_click_53_listener", "UserListComponent_Template_button_click_55_listener", "UserListComponent_div_57_Template", "UserListComponent_Template_div_scroll_58_listener", "UserListComponent_div_59_Template", "UserListComponent_div_60_Template", "UserListComponent_ul_61_Template", "UserListComponent_div_62_Template", "UserListComponent_div_63_Template", "asyncScheduler", "timer", "period", "scheduler"], "sourceRoot": "webpack:///", "x_google_ignoreList": [11]}