{"ast": null, "code": "import { Subscription, interval } from 'rxjs';\nimport { CallType } from 'src/app/models/message.model';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"src/app/services/toast.service\";\nimport * as i5 from \"src/app/services/logger.service\";\nimport * as i6 from \"@app/services/theme.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nfunction UserListComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Affichage de \", ctx_r0.users.length, \" sur \", ctx_r0.totalUsers, \" utilisateurs\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Page \", ctx_r0.currentPage, \" sur \", ctx_r0.totalPages, \"\");\n  }\n}\nfunction UserListComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"div\", 47);\n    i0.ɵɵtext(3, \"Chargement des utilisateurs...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 51);\n    i0.ɵɵtext(4, \"Aucun utilisateur trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 52);\n    i0.ɵɵtext(6, \" Essayez un autre terme de recherche ou effacez les filtres \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_ul_61_li_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 66);\n  }\n}\nfunction UserListComponent_ul_61_li_1_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const user_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.startAudioCall(user_r7.id || user_r7._id));\n    });\n    i0.ɵɵelement(1, \"i\", 68);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserListComponent_ul_61_li_1_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const user_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.startVideoCall(user_r7.id || user_r7._id));\n    });\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserListComponent_ul_61_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 55)(1, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ul_61_li_1_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const user_r7 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.startConversation(user_r7.id || user_r7._id));\n    });\n    i0.ɵɵelementStart(2, \"div\", 57);\n    i0.ɵɵelement(3, \"img\", 58);\n    i0.ɵɵtemplate(4, UserListComponent_ul_61_li_1_span_4_Template, 1, 0, \"span\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 60)(6, \"h3\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 62);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 63);\n    i0.ɵɵtemplate(11, UserListComponent_ul_61_li_1_button_11_Template, 2, 0, \"button\", 64);\n    i0.ɵɵtemplate(12, UserListComponent_ul_61_li_1_button_12_Template, 2, 0, \"button\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", user_r7.image || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r7.isOnline);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r7.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r7.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", user_r7.isOnline);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r7.isOnline);\n  }\n}\nfunction UserListComponent_ul_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 53);\n    i0.ɵɵtemplate(1, UserListComponent_ul_61_li_1_Template, 13, 6, \"li\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.users);\n  }\n}\nfunction UserListComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72);\n    i0.ɵɵelement(2, \"div\", 73)(3, \"div\", 74)(4, \"div\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 47);\n    i0.ɵɵtext(6, \" Chargement de plus d'utilisateurs... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_63_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.loadNextPage());\n    });\n    i0.ɵɵelement(2, \"i\", 78);\n    i0.ɵɵtext(3, \" Charger plus d'utilisateurs \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let UserListComponent = /*#__PURE__*/(() => {\n  class UserListComponent {\n    constructor(MessageService, router, route, authService, toastService, logger, themeService) {\n      this.MessageService = MessageService;\n      this.router = router;\n      this.route = route;\n      this.authService = authService;\n      this.toastService = toastService;\n      this.logger = logger;\n      this.themeService = themeService;\n      this.users = [];\n      this.loading = true;\n      this.currentUserId = null;\n      // Pagination\n      this.currentPage = 1;\n      this.pageSize = 10;\n      this.totalUsers = 0;\n      this.totalPages = 0;\n      this.hasNextPage = false;\n      this.hasPreviousPage = false;\n      // Sorting and filtering\n      this.sortBy = 'username';\n      this.sortOrder = 'asc';\n      this.filterForm = new FormGroup({\n        searchQuery: new FormControl(''),\n        isOnline: new FormControl(null)\n      });\n      // Auto-refresh\n      this.autoRefreshEnabled = true;\n      this.autoRefreshInterval = 30000; // 30 seconds\n      this.loadingMore = false;\n      this.subscriptions = new Subscription();\n      this.isDarkMode$ = this.themeService.darkMode$;\n    }\n    ngOnInit() {\n      this.currentUserId = this.authService.getCurrentUserId();\n      this.setupFilterListeners();\n      this.setupAutoRefresh();\n      this.loadUsers();\n    }\n    setupFilterListeners() {\n      // Subscribe to search query changes\n      const searchSub = this.filterForm.get('searchQuery').valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n      this.subscriptions.add(searchSub);\n      // Subscribe to online status filter changes\n      const onlineSub = this.filterForm.get('isOnline').valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n      this.subscriptions.add(onlineSub);\n    }\n    setupAutoRefresh() {\n      if (this.autoRefreshEnabled) {\n        this.autoRefreshSubscription = interval(this.autoRefreshInterval).subscribe(() => {\n          if (!this.loading && !this.filterForm.get('searchQuery')?.value) {\n            this.loadUsers(true);\n          }\n        });\n      }\n    }\n    toggleAutoRefresh() {\n      this.autoRefreshEnabled = !this.autoRefreshEnabled;\n      if (this.autoRefreshEnabled) {\n        this.setupAutoRefresh();\n      } else if (this.autoRefreshSubscription) {\n        this.autoRefreshSubscription.unsubscribe();\n        this.autoRefreshSubscription = undefined;\n      }\n    }\n    resetPagination() {\n      this.currentPage = 1;\n    }\n    // Get searchQuery from the form\n    get searchQuery() {\n      return this.filterForm.get('searchQuery')?.value || '';\n    }\n    // Set searchQuery in the form\n    set searchQuery(value) {\n      this.filterForm.get('searchQuery')?.setValue(value);\n    }\n    // Helper function for template type casting\n    $any(item) {\n      return item;\n    }\n    loadUsers(forceRefresh = false) {\n      if (this.loadingMore) return;\n      this.loading = true;\n      const searchQuery = this.filterForm.get('searchQuery')?.value || '';\n      const isOnline = this.filterForm.get('isOnline')?.value;\n      const sub = this.MessageService.getAllUsers(forceRefresh, searchQuery, this.currentPage, this.pageSize, this.sortBy, this.sortOrder, isOnline === true).subscribe({\n        next: users => {\n          if (!Array.isArray(users)) {\n            this.users = [];\n            this.loading = false;\n            this.loadingMore = false;\n            this.toastService.showError('Failed to load users: Invalid data');\n            return;\n          }\n          // If first page, replace users array; otherwise append\n          if (this.currentPage === 1) {\n            // Filter out current user\n            this.users = users.filter(user => {\n              if (!user) return false;\n              const userId = user.id || user._id;\n              return userId !== this.currentUserId;\n            });\n          } else {\n            // Append new users to existing array, avoiding duplicates and filtering out current user\n            const newUsers = users.filter(newUser => {\n              if (!newUser) return false;\n              const userId = newUser.id || newUser._id;\n              return userId !== this.currentUserId && !this.users.some(existingUser => (existingUser.id || existingUser._id) === userId);\n            });\n            this.users = [...this.users, ...newUsers];\n          }\n          // Update pagination metadata from service\n          const pagination = this.MessageService.currentUserPagination;\n          this.totalUsers = pagination.totalCount;\n          this.totalPages = pagination.totalPages;\n          this.hasNextPage = pagination.hasNextPage;\n          this.hasPreviousPage = pagination.hasPreviousPage;\n          this.loading = false;\n          this.loadingMore = false;\n        },\n        error: error => {\n          this.loading = false;\n          this.loadingMore = false;\n          this.toastService.showError(`Failed to load users: ${error.message || 'Unknown error'}`);\n          if (this.currentPage === 1) {\n            this.users = [];\n          }\n        },\n        complete: () => {\n          this.loading = false;\n          this.loadingMore = false;\n        }\n      });\n      this.subscriptions.add(sub);\n    }\n    startConversation(userId) {\n      if (!userId) {\n        this.toastService.showError('Cannot start conversation with undefined user');\n        return;\n      }\n      this.toastService.showInfo('Creating conversation...');\n      this.MessageService.createConversation(userId).subscribe({\n        next: conversation => {\n          if (!conversation || !conversation.id) {\n            this.toastService.showError('Failed to create conversation: Invalid response');\n            return;\n          }\n          this.router.navigate(['/messages/conversations/chat', conversation.id]).then(success => {\n            if (!success) {\n              this.toastService.showError('Failed to open conversation');\n            }\n          });\n        },\n        error: error => {\n          this.toastService.showError(`Failed to create conversation: ${error.message || 'Unknown error'}`);\n        }\n      });\n    }\n    startAudioCall(userId) {\n      if (!userId) return;\n      this.MessageService.initiateCall(userId, CallType.AUDIO).subscribe({\n        next: call => {\n          this.toastService.showSuccess('Audio call initiated');\n        },\n        error: error => {\n          this.toastService.showError('Failed to initiate audio call');\n        }\n      });\n    }\n    startVideoCall(userId) {\n      if (!userId) return;\n      this.MessageService.initiateCall(userId, CallType.VIDEO).subscribe({\n        next: call => {\n          this.toastService.showSuccess('Video call initiated');\n        },\n        error: error => {\n          this.toastService.showError('Failed to initiate video call');\n        }\n      });\n    }\n    loadNextPage() {\n      if (this.hasNextPage && !this.loading) {\n        this.loadingMore = true;\n        this.currentPage++;\n        this.loadUsers();\n      }\n    }\n    loadPreviousPage() {\n      if (this.hasPreviousPage && !this.loading) {\n        this.loadingMore = true;\n        this.currentPage--;\n        this.loadUsers();\n      }\n    }\n    refreshUsers() {\n      this.resetPagination();\n      this.loadUsers(true);\n    }\n    clearFilters() {\n      this.filterForm.reset({\n        searchQuery: '',\n        isOnline: null\n      });\n      this.resetPagination();\n      this.loadUsers(true);\n    }\n    changeSortOrder(field) {\n      if (this.sortBy === field) {\n        // Toggle sort order if clicking the same field\n        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n      } else {\n        // Set new sort field with default ascending order\n        this.sortBy = field;\n        this.sortOrder = 'asc';\n      }\n      this.resetPagination();\n      this.loadUsers(true);\n    }\n    /**\n     * Navigue vers la liste des conversations\n     */\n    goBackToConversations() {\n      this.router.navigate(['/messages/conversations']);\n    }\n    ngOnDestroy() {\n      this.subscriptions.unsubscribe();\n      if (this.autoRefreshSubscription) {\n        this.autoRefreshSubscription.unsubscribe();\n      }\n    }\n    static {\n      this.ɵfac = function UserListComponent_Factory(t) {\n        return new (t || UserListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i5.LoggerService), i0.ɵɵdirectiveInject(i6.ThemeService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UserListComponent,\n        selectors: [[\"app-user-list\"]],\n        decls: 64,\n        vars: 18,\n        consts: [[1, \"flex\", \"flex-col\", \"h-full\", \"futuristic-users-container\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/10\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/10\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"top-[40%]\", \"right-[30%]\", \"w-40\", \"h-40\", \"rounded-full\", \"bg-gradient-to-br\", \"from-transparent\", \"to-transparent\", \"dark:from-[#00f7ff]/5\", \"dark:to-transparent\", \"blur-3xl\", \"opacity-0\", \"dark:opacity-100\"], [1, \"absolute\", \"bottom-[60%]\", \"left-[25%]\", \"w-32\", \"h-32\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-transparent\", \"to-transparent\", \"dark:from-[#00f7ff]/5\", \"dark:to-transparent\", \"blur-3xl\", \"opacity-0\", \"dark:opacity-100\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-0\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\"], [1, \"absolute\", \"inset-0\", \"opacity-0\", \"dark:opacity-100\", \"overflow-hidden\"], [1, \"h-px\", \"w-full\", \"bg-[#00f7ff]/20\", \"absolute\", \"animate-scan\"], [1, \"futuristic-users-header\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-4\"], [1, \"futuristic-title\"], [1, \"flex\", \"space-x-2\"], [\"title\", \"Rafra\\u00EEchir la liste\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"space-y-3\"], [1, \"relative\"], [\"type\", \"text\", \"placeholder\", \"Rechercher des utilisateurs...\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-2\", \"rounded-lg\", \"futuristic-input-field\", 3, \"ngModel\", \"ngModelChange\"], [1, \"fas\", \"fa-search\", \"absolute\", \"left-3\", \"top-3\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"futuristic-checkbox-container\"], [\"type\", \"checkbox\", \"id\", \"onlineFilter\", 1, \"futuristic-checkbox\", 3, \"checked\", \"change\"], [1, \"futuristic-checkbox-checkmark\"], [\"for\", \"onlineFilter\", 1, \"futuristic-label\"], [1, \"futuristic-label\"], [1, \"futuristic-select\", 3, \"change\"], [\"value\", \"username\", 3, \"selected\"], [\"value\", \"email\", 3, \"selected\"], [\"value\", \"lastActive\", 3, \"selected\"], [1, \"futuristic-sort-button\", 3, \"title\", \"click\"], [1, \"futuristic-clear-button\", 3, \"click\"], [\"class\", \"flex justify-between items-center futuristic-pagination-info\", 4, \"ngIf\"], [1, \"futuristic-users-list\", 3, \"scroll\"], [\"class\", \"futuristic-loading-container\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-users-grid\", 4, \"ngIf\"], [\"class\", \"futuristic-loading-more\", 4, \"ngIf\"], [\"class\", \"futuristic-load-more-container\", 4, \"ngIf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"futuristic-pagination-info\"], [1, \"futuristic-loading-container\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-users\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-users-grid\"], [\"class\", \"futuristic-user-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-user-card\"], [1, \"futuristic-user-content\", 3, \"click\"], [1, \"futuristic-avatar\"], [\"alt\", \"User avatar\", 3, \"src\"], [\"class\", \"futuristic-online-indicator\", 4, \"ngIf\"], [1, \"futuristic-user-info\"], [1, \"futuristic-username\"], [1, \"futuristic-user-email\"], [1, \"futuristic-call-buttons\"], [\"class\", \"futuristic-call-button\", \"title\", \"Appel audio\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-call-button\", \"title\", \"Appel vid\\u00E9o\", 3, \"click\", 4, \"ngIf\"], [1, \"futuristic-online-indicator\"], [\"title\", \"Appel audio\", 1, \"futuristic-call-button\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"futuristic-call-button\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [1, \"futuristic-loading-more\"], [1, \"futuristic-loading-dots\"], [1, \"futuristic-loading-dot\", 2, \"animation-delay\", \"0s\"], [1, \"futuristic-loading-dot\", 2, \"animation-delay\", \"0.2s\"], [1, \"futuristic-loading-dot\", 2, \"animation-delay\", \"0.4s\"], [1, \"futuristic-load-more-container\"], [1, \"futuristic-load-more-button\", 3, \"click\"], [1, \"fas\", \"fa-chevron-down\", \"mr-2\"]],\n        template: function UserListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵpipe(1, \"async\");\n            i0.ɵɵelementStart(2, \"div\", 1);\n            i0.ɵɵelement(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7);\n            i0.ɵɵelement(9, \"div\", 8)(10, \"div\", 8)(11, \"div\", 8)(12, \"div\", 8)(13, \"div\", 8)(14, \"div\", 8)(15, \"div\", 8)(16, \"div\", 8)(17, \"div\", 8)(18, \"div\", 8)(19, \"div\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 9);\n            i0.ɵɵelement(21, \"div\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 11)(23, \"div\", 12)(24, \"h1\", 13);\n            i0.ɵɵtext(25, \"Nouvelle Conversation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 14)(27, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_27_listener() {\n              return ctx.refreshUsers();\n            });\n            i0.ɵɵelement(28, \"i\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_29_listener() {\n              return ctx.goBackToConversations();\n            });\n            i0.ɵɵelement(30, \"i\", 18);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"div\", 19)(32, \"div\", 20)(33, \"input\", 21);\n            i0.ɵɵlistener(\"ngModelChange\", function UserListComponent_Template_input_ngModelChange_33_listener($event) {\n              return ctx.searchQuery = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(34, \"i\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 23)(36, \"div\", 24)(37, \"div\", 25)(38, \"label\", 26)(39, \"input\", 27);\n            i0.ɵɵlistener(\"change\", function UserListComponent_Template_input_change_39_listener($event) {\n              let tmp_b_0;\n              return (tmp_b_0 = ctx.filterForm.get(\"isOnline\")) == null ? null : tmp_b_0.setValue($event.target.checked ? true : null);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(40, \"span\", 28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"label\", 29);\n            i0.ɵɵtext(42, \"En ligne uniquement\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(43, \"div\", 25)(44, \"span\", 30);\n            i0.ɵɵtext(45, \"Trier par:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"select\", 31);\n            i0.ɵɵlistener(\"change\", function UserListComponent_Template_select_change_46_listener($event) {\n              return ctx.changeSortOrder($event.target.value);\n            });\n            i0.ɵɵelementStart(47, \"option\", 32);\n            i0.ɵɵtext(48, \" Nom \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"option\", 33);\n            i0.ɵɵtext(50, \" Email \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"option\", 34);\n            i0.ɵɵtext(52, \" Derni\\u00E8re activit\\u00E9 \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"button\", 35);\n            i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_53_listener() {\n              ctx.sortOrder = ctx.sortOrder === \"asc\" ? \"desc\" : \"asc\";\n              return ctx.loadUsers(true);\n            });\n            i0.ɵɵelement(54, \"i\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(55, \"button\", 36);\n            i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_55_listener() {\n              return ctx.clearFilters();\n            });\n            i0.ɵɵtext(56, \" Effacer les filtres \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(57, UserListComponent_div_57_Template, 5, 4, \"div\", 37);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(58, \"div\", 38);\n            i0.ɵɵlistener(\"scroll\", function UserListComponent_Template_div_scroll_58_listener($event) {\n              return $event.target.scrollTop + $event.target.clientHeight >= $event.target.scrollHeight - 200 && ctx.loadNextPage();\n            });\n            i0.ɵɵtemplate(59, UserListComponent_div_59_Template, 4, 0, \"div\", 39);\n            i0.ɵɵtemplate(60, UserListComponent_div_60_Template, 7, 0, \"div\", 40);\n            i0.ɵɵtemplate(61, UserListComponent_ul_61_Template, 2, 1, \"ul\", 41);\n            i0.ɵɵtemplate(62, UserListComponent_div_62_Template, 7, 0, \"div\", 42);\n            i0.ɵɵtemplate(63, UserListComponent_div_63_Template, 4, 0, \"div\", 43);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 16, ctx.isDarkMode$));\n            i0.ɵɵadvance(33);\n            i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"checked\", ((tmp_2_0 = ctx.filterForm.get(\"isOnline\")) == null ? null : tmp_2_0.value) === true);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"selected\", ctx.sortBy === \"username\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"selected\", ctx.sortBy === \"email\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"selected\", ctx.sortBy === \"lastActive\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"title\", ctx.sortOrder === \"asc\" ? \"Ordre croissant\" : \"Ordre d\\u00E9croissant\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassMap(ctx.sortOrder === \"asc\" ? \"fas fa-sort-up\" : \"fas fa-sort-down\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.totalUsers > 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading && !ctx.users.length);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.users.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.users.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading && ctx.users.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.hasNextPage && !ctx.loading);\n          }\n        },\n        dependencies: [i7.NgForOf, i7.NgIf, i8.NgSelectOption, i8.ɵNgSelectMultipleOption, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i7.AsyncPipe],\n        styles: [\":not(.dark)[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]{background-color:#f0f4f8;color:#6d6870;position:relative;overflow:hidden}.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]{background-color:var(--dark-bg);color:var(--text-light);position:relative;overflow:hidden}@keyframes _ngcontent-%COMP%_grid-pulse{0%{opacity:.3}50%{opacity:.5}to{opacity:.3}}@keyframes _ngcontent-%COMP%_scan{0%{top:-10%;opacity:.5}50%{opacity:.8}to{top:110%;opacity:.5}}.animate-scan[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_scan 8s linear infinite;box-shadow:0 0 10px #00f7ff80}:not(.dark)[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background-image:linear-gradient(rgba(79,95,173,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(79,95,173,.03) 1px,transparent 1px);background-size:20px 20px;pointer-events:none;z-index:0}.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background-image:linear-gradient(rgba(0,247,255,.07) 1px,transparent 1px),linear-gradient(90deg,rgba(0,247,255,.07) 1px,transparent 1px),linear-gradient(rgba(0,247,255,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(0,247,255,.03) 1px,transparent 1px);background-size:100px 100px,100px 100px,20px 20px,20px 20px;pointer-events:none;z-index:0;animation:_ngcontent-%COMP%_grid-pulse 4s infinite ease-in-out}.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background:repeating-linear-gradient(to bottom,transparent,transparent 50px,rgba(0,247,255,.03) 50px,rgba(0,247,255,.03) 51px);pointer-events:none;z-index:0}:not(.dark)[_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid rgba(79,95,173,.2);background-color:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);position:sticky;top:0;z-index:10;box-shadow:0 4px 20px #0000001a;position:relative}.dark[_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid rgba(0,247,255,.2);background-color:#1e1e1ee6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);position:sticky;top:0;z-index:10;box-shadow:0 4px 20px #0000004d;position:relative}:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;background:linear-gradient(135deg,#4f5fad,#7826b5);-webkit-background-clip:text;background-clip:text;color:transparent;text-shadow:0 0 10px rgba(79,95,173,.5)}.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;background:linear-gradient(135deg,var(--accent-color),var(--secondary-color));-webkit-background-clip:text;background-clip:text;color:transparent;text-shadow:0 0 10px rgba(0,247,255,.5)}:not(.dark)[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;background-color:#4f5fad1a;color:#4f5fad;border:none;border-radius:50%;cursor:pointer;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover{background-color:#4f5fad33;transform:translateY(-2px);box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;background-color:#00f7ff1a;color:var(--accent-color);border:none;border-radius:50%;cursor:pointer;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:translateY(-2px);box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]{background-color:#4f5fad0d;border:1px solid rgba(79,95,173,.2);border-radius:var(--border-radius-md);color:#6d6870;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus, :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus{background-color:#4f5fad1a;border-color:#4f5fad;box-shadow:0 0 15px #4f5fad66;outline:none}:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder, :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder{color:#6d6870}.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]{background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.2);border-radius:var(--border-radius-md);color:var(--text-light);transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus{background-color:#00f7ff1a;border-color:var(--accent-color);box-shadow:var(--glow-effect);outline:none}.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder, .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder{color:var(--text-dim)}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%]{position:relative;display:inline-block;width:18px;height:18px;cursor:pointer}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]{position:absolute;opacity:0;cursor:pointer;height:0;width:0}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:18px;width:18px;background-color:#4f5fad0d;border:1px solid rgba(79,95,173,.2);border-radius:4px;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]{background-color:#4f5fad;box-shadow:0 0 15px #4f5fad66}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;display:none}:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after{display:block;left:6px;top:2px;width:5px;height:10px;border:solid white;border-width:0 2px 2px 0;transform:rotate(45deg)}:not(.dark)[_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%]{color:#6d6870;font-size:.875rem;transition:color var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%]{position:relative;display:inline-block;width:18px;height:18px;cursor:pointer}.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]{position:absolute;opacity:0;cursor:pointer;height:0;width:0}.dark[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:18px;width:18px;background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.2);border-radius:4px;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]{background-color:var(--accent-color);box-shadow:var(--glow-effect)}.dark[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;display:none}.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after{display:block;left:6px;top:2px;width:5px;height:10px;border:solid white;border-width:0 2px 2px 0;transform:rotate(45deg)}.dark[_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.875rem;transition:color var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]{background-color:#4f5fad0d;border:1px solid rgba(79,95,173,.2);border-radius:var(--border-radius-md);color:#6d6870;padding:.25rem 2rem .25rem .5rem;font-size:.875rem;appearance:none;background-image:url(\\\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234f5fad' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\\\");background-repeat:no-repeat;background-position:right .5rem center;background-size:1em;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus, :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus{background-color:#4f5fad1a;border-color:#4f5fad;box-shadow:0 0 15px #4f5fad66;outline:none}:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%]{background-color:#fff;color:#6d6870}.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]{background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.2);border-radius:var(--border-radius-md);color:var(--text-light);padding:.25rem 2rem .25rem .5rem;font-size:.875rem;appearance:none;background-image:url(\\\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300f7ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\\\");background-repeat:no-repeat;background-position:right .5rem center;background-size:1em;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus{background-color:#00f7ff1a;border-color:var(--accent-color);box-shadow:var(--glow-effect);outline:none}.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%]{background-color:var(--dark-bg);color:var(--text-light)}:not(.dark)[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]{width:28px;height:28px;display:flex;align-items:center;justify-content:center;background-color:#4f5fad0d;color:#4f5fad;border:none;border-radius:var(--border-radius-sm);cursor:pointer;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover{background-color:#4f5fad1a;box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]{width:28px;height:28px;display:flex;align-items:center;justify-content:center;background-color:#00f7ff0d;color:var(--accent-color);border:none;border-radius:var(--border-radius-sm);cursor:pointer;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a;box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]{font-size:.75rem;color:#4f5fad;background:none;border:none;cursor:pointer;transition:all var(--transition-fast);padding:.25rem .5rem;border-radius:var(--border-radius-sm)}:not(.dark)[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover{color:#7826b5;background-color:#4f5fad0d}.dark[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]{font-size:.75rem;color:var(--accent-color);background:none;border:none;cursor:pointer;transition:all var(--transition-fast);padding:.25rem .5rem;border-radius:var(--border-radius-sm)}.dark[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover{color:var(--secondary-color);background-color:#00f7ff0d}:not(.dark)[_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%]{font-size:.75rem;color:#6d6870}.dark[_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%]{font-size:.75rem;color:var(--text-dim)}:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1rem;scrollbar-width:thin;scrollbar-color:#4f5fad transparent;position:relative;z-index:1}:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:#4f5fad;border-radius:10px}.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1rem;scrollbar-width:thin;scrollbar-color:var(--accent-color) transparent;position:relative;z-index:1}.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:var(--accent-color);border-radius:10px}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:2rem}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;border:2px solid transparent;border-top-color:#4f5fad;border-bottom-color:#7826b5;animation:_ngcontent-%COMP%_futuristic-spin-light 1.2s linear infinite;box-shadow:0 0 15px #4f5fad4d}@keyframes _ngcontent-%COMP%_futuristic-spin-light{0%{transform:rotate(0)}to{transform:rotate(360deg)}}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%]{margin-top:1rem;color:#6d6870;font-size:.875rem;text-align:center}.dark[_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:2rem}.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;border:2px solid transparent;border-top-color:var(--accent-color);border-bottom-color:var(--secondary-color);animation:_ngcontent-%COMP%_futuristic-spin-dark 1.2s linear infinite;box-shadow:0 0 15px #00f7ff4d}@keyframes _ngcontent-%COMP%_futuristic-spin-dark{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.dark[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%]{margin-top:1rem;color:var(--text-dim);font-size:.875rem;text-align:center}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:2rem;text-align:center}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{font-size:3rem;color:#4f5fad;margin-bottom:1rem;opacity:.5}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#4f5fad;margin-bottom:.5rem}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%]{color:#6d6870;font-size:.875rem}.dark[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:2rem;text-align:center}.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{font-size:3rem;color:var(--accent-color);margin-bottom:1rem;opacity:.5}.dark[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:var(--text-light);margin-bottom:.5rem}.dark[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.875rem}:not(.dark)[_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1rem;list-style:none;padding:0;margin:0}:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]{background-color:#4f5fad0d;border:1px solid rgba(79,95,173,.1);border-radius:var(--border-radius-md);overflow:hidden;transition:all var(--transition-fast);display:flex;flex-direction:column}:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 0 15px #4f5fad66;background-color:#4f5fad1a;border-color:#4f5fad4d}:not(.dark)[_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%]{display:flex;align-items:center;padding:1rem;cursor:pointer;flex:1}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;flex-shrink:0;margin-right:1rem}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%;border:2px solid rgba(79,95,173,.3);transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-color:#4f5fad;box-shadow:0 0 15px #4f5fad80}:not(.dark)[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:0;right:0;width:12px;height:12px;background-color:#4caf50;border-radius:50%;border:2px solid #ffffff;box-shadow:0 0 8px #4caf50cc;animation:_ngcontent-%COMP%_pulse-light 2s infinite}@keyframes _ngcontent-%COMP%_pulse-light{0%{box-shadow:0 0 #4caf5066}70%{box-shadow:0 0 0 6px #4caf5000}to{box-shadow:0 0 #4caf5000}}:not(.dark)[_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%]{flex:1;min-width:0}:not(.dark)[_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;color:#4f5fad;margin-bottom:.25rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}:not(.dark)[_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%]{font-size:.75rem;color:#6d6870;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}:not(.dark)[_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding:.5rem 1rem;background-color:#4f5fad0d;border-top:1px solid rgba(79,95,173,.1)}:not(.dark)[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]{width:32px;height:32px;display:flex;align-items:center;justify-content:center;background-color:#4f5fad1a;color:#4f5fad;border:none;border-radius:50%;cursor:pointer;transition:all var(--transition-fast);margin-left:.5rem}:not(.dark)[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover{background-color:#4f5fad33;transform:translateY(-2px);box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1rem;list-style:none;padding:0;margin:0}.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]{background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.1);border-radius:var(--border-radius-md);overflow:hidden;transition:all var(--transition-fast);display:flex;flex-direction:column}.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:var(--glow-effect);background-color:#00f7ff1a;border-color:#00f7ff4d}.dark[_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%]{display:flex;align-items:center;padding:1rem;cursor:pointer;flex:1}.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;flex-shrink:0;margin-right:1rem}.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%;border:2px solid rgba(0,247,255,.3);transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-color:var(--accent-color);box-shadow:0 0 15px #00f7ff80}.dark[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:0;right:0;width:12px;height:12px;background-color:var(--success-color);border-radius:50%;border:2px solid var(--dark-bg);box-shadow:0 0 8px #00ff80cc;animation:_ngcontent-%COMP%_pulse-dark 2s infinite}@keyframes _ngcontent-%COMP%_pulse-dark{0%{box-shadow:0 0 #00ff8066}70%{box-shadow:0 0 0 6px #00ff8000}to{box-shadow:0 0 #00ff8000}}.dark[_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%]{flex:1;min-width:0}.dark[_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;color:var(--text-light);margin-bottom:.25rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.dark[_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%]{font-size:.75rem;color:var(--text-dim);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.dark[_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding:.5rem 1rem;background-color:#0003;border-top:1px solid rgba(0,247,255,.1)}.dark[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]{width:32px;height:32px;display:flex;align-items:center;justify-content:center;background-color:#00f7ff1a;color:var(--accent-color);border:none;border-radius:50%;cursor:pointer;transition:all var(--transition-fast);margin-left:.5rem}.dark[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:translateY(-2px);box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:1rem}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:.5rem}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%]{width:8px;height:8px;margin:0 4px;background-color:#4f5fad;border-radius:50%;animation:_ngcontent-%COMP%_dot-pulse-light 1.4s infinite ease-in-out;box-shadow:0 0 8px #4f5fad80}@keyframes _ngcontent-%COMP%_dot-pulse-light{0%,to{transform:scale(.5);opacity:.5}50%{transform:scale(1);opacity:1}}:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:1rem}:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,rgba(79,95,173,.1),rgba(79,95,173,.2));color:#4f5fad;border:1px solid rgba(79,95,173,.3);border-radius:var(--border-radius-md);font-size:.875rem;cursor:pointer;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(79,95,173,.2),rgba(79,95,173,.3));transform:translateY(-2px);box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:1rem}.dark[_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:.5rem}.dark[_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%]{width:8px;height:8px;margin:0 4px;background-color:var(--accent-color);border-radius:50%;animation:_ngcontent-%COMP%_dot-pulse-dark 1.4s infinite ease-in-out;box-shadow:0 0 8px #00f7ff80}@keyframes _ngcontent-%COMP%_dot-pulse-dark{0%,to{transform:scale(.5);opacity:.5}50%{transform:scale(1);opacity:1}}.dark[_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:1rem}.dark[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,rgba(0,247,255,.1),rgba(0,247,255,.2));color:var(--accent-color);border:1px solid rgba(0,247,255,.3);border-radius:var(--border-radius-md);font-size:.875rem;cursor:pointer;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(0,247,255,.2),rgba(0,247,255,.3));transform:translateY(-2px);box-shadow:var(--glow-effect)}\"]\n      });\n    }\n  }\n  return UserListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}