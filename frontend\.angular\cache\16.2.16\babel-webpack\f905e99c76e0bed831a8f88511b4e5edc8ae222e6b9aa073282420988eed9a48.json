{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/reunion.service\";\nimport * as i3 from \"@angular/platform-browser\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../../pipes/highlight-presence.pipe\";\nfunction ReunionDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionDetailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ReunionDetailComponent_div_7_li_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const participant_r6 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", participant_r6.username, \" (\", participant_r6.email, \") \");\n  }\n}\nfunction ReunionDetailComponent_div_7_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h2\", 21);\n    i0.ɵɵtext(2, \"Lieu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 22);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 17);\n    i0.ɵɵelement(5, \"path\", 26)(6, \"path\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"span\", 19);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.reunion.lieu);\n  }\n}\nfunction ReunionDetailComponent_div_7_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h2\", 21);\n    i0.ɵɵtext(2, \"Lien Visio:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 28);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 29);\n    i0.ɵɵelement(5, \"path\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Rejoindre la r\\u00E9union \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r5.reunion.lienVisio, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ReunionDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\")(3, \"h1\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"p\", 13);\n    i0.ɵɵpipe(6, \"highlightPresence\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ReunionDetailComponent_div_7_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.editReunion());\n    });\n    i0.ɵɵtext(8, \" Modifier \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"div\", 16);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 17);\n    i0.ɵɵelement(12, \"path\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\", 19);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 20)(17, \"h2\", 21);\n    i0.ɵɵtext(18, \"Cr\\u00E9ateur:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 22)(20, \"span\", 19);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 15)(23, \"h2\", 21);\n    i0.ɵɵtext(24, \"Participants:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"ul\", 23);\n    i0.ɵɵtemplate(26, ReunionDetailComponent_div_7_li_26_Template, 2, 2, \"li\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 15)(28, \"h2\", 21);\n    i0.ɵɵtext(29, \"Planning:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 19)(31, \"p\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\");\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"date\");\n    i0.ɵɵpipe(36, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(37, ReunionDetailComponent_div_7_div_37_Template, 9, 1, \"div\", 25);\n    i0.ɵɵtemplate(38, ReunionDetailComponent_div_7_div_38_Template, 7, 1, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.reunion.titre);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(6, 13, ctx_r2.reunion.description), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind2(15, 15, ctx_r2.reunion.date, \"fullDate\"), \", \", ctx_r2.reunion.heureDebut, \" - \", ctx_r2.reunion.heureFin, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.reunion.createur == null ? null : ctx_r2.reunion.createur.username, \" (\", ctx_r2.reunion.createur == null ? null : ctx_r2.reunion.createur.email, \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.reunion.participants);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Du \", i0.ɵɵpipeBind2(35, 18, ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.dateDebut, \"mediumDate\"), \" au \", i0.ɵɵpipeBind2(36, 21, ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.dateFin, \"mediumDate\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.reunion.lieu);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.reunion.lienVisio);\n  }\n}\nexport let ReunionDetailComponent = /*#__PURE__*/(() => {\n  class ReunionDetailComponent {\n    constructor(route, router, reunionService, sanitizer) {\n      this.route = route;\n      this.router = router;\n      this.reunionService = reunionService;\n      this.sanitizer = sanitizer;\n      this.reunion = null;\n      this.loading = true;\n      this.error = null;\n    }\n    ngOnInit() {\n      this.loadReunionDetails();\n    }\n    loadReunionDetails() {\n      const id = this.route.snapshot.paramMap.get('id');\n      if (!id) {\n        this.error = 'ID de réunion non fourni';\n        this.loading = false;\n        return;\n      }\n      this.reunionService.getReunionById(id).subscribe({\n        next: response => {\n          this.reunion = response.reunion;\n          this.loading = false;\n        },\n        error: err => {\n          this.error = err.error?.message || 'Erreur lors du chargement';\n          this.loading = false;\n          console.error('Erreur:', err);\n        }\n      });\n    }\n    formatDescription(description) {\n      if (!description) return this.sanitizer.bypassSecurityTrustHtml('');\n      // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n      const formattedText = description.replace(/\\(presence obligatoire\\)/gi, '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>');\n      // Sanitize le HTML pour éviter les problèmes de sécurité\n      return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n    }\n    editReunion() {\n      if (this.reunion) {\n        this.router.navigate(['/reunions/edit', this.reunion._id]);\n      }\n    }\n    static {\n      this.ɵfac = function ReunionDetailComponent_Factory(t) {\n        return new (t || ReunionDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ReunionService), i0.ɵɵdirectiveInject(i3.DomSanitizer));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReunionDetailComponent,\n        selectors: [[\"app-reunion-detail\"]],\n        decls: 8,\n        vars: 3,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"mb-4\", \"flex\", \"items-center\", \"text-purple-600\", \"hover:text-purple-800\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-lg shadow-md p-6\", 4, \"ngIf\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-b-2\", \"border-purple-600\", \"mx-auto\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-start\", \"mb-4\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\"], [1, \"text-gray-600\", \"mt-1\", 3, \"innerHTML\"], [1, \"px-4\", \"py-2\", \"bg-blue-500\", \"text-white\", \"rounded\", \"hover:bg-blue-600\", \"transition-colors\", 3, \"click\"], [1, \"mb-6\"], [1, \"flex\", \"items-center\", \"mb-2\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-gray-500\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-gray-700\"], [1, \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"mb-2\", \"text-gray-800\"], [1, \"flex\", \"items-center\"], [1, \"list-disc\", \"pl-5\"], [\"class\", \"text-gray-700\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [\"target\", \"_blank\", 1, \"text-blue-600\", \"hover:underline\", \"flex\", \"items-center\", 3, \"href\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"]],\n        template: function ReunionDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n            i0.ɵɵlistener(\"click\", function ReunionDetailComponent_Template_button_click_1_listener() {\n              return ctx.router.navigate([\"/reunions\"]);\n            });\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(2, \"svg\", 2);\n            i0.ɵɵelement(3, \"path\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(4, \" Retour aux r\\u00E9unions \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, ReunionDetailComponent_div_5_Template, 2, 0, \"div\", 4);\n            i0.ɵɵtemplate(6, ReunionDetailComponent_div_6_Template, 2, 1, \"div\", 5);\n            i0.ɵɵtemplate(7, ReunionDetailComponent_div_7_Template, 39, 24, \"div\", 6);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunion);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i4.DatePipe, i5.HighlightPresencePipe]\n      });\n    }\n  }\n  return ReunionDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}