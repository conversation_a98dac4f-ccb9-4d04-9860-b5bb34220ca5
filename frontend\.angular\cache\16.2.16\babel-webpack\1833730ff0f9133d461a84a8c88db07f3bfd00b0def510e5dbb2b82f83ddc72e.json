{"ast": null, "code": "import { catchError, finalize, takeUntil } from 'rxjs/operators';\nimport { Subject, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/rendus.service\";\nimport * as i2 from \"../../../../services/evaluation.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction EvaluationsListComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EvaluationsListComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_4_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.loadEvaluations());\n    });\n    i0.ɵɵtext(5, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction EvaluationsListComponent_div_5_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", groupe_r10);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(groupe_r10);\n  }\n}\nfunction EvaluationsListComponent_div_5_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const projet_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", projet_r11._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(projet_r11.titre);\n  }\n}\nfunction EvaluationsListComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"div\")(3, \"label\", 15);\n    i0.ɵɵtext(4, \"Recherche\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 16);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_5_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.searchTerm = $event);\n    })(\"input\", function EvaluationsListComponent_div_5_Template_input_input_5_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\")(7, \"label\", 17);\n    i0.ɵɵtext(8, \"Groupe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"select\", 18);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_5_Template_select_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.filterGroupe = $event);\n    })(\"change\", function EvaluationsListComponent_div_5_Template_select_change_9_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.applyFilters());\n    });\n    i0.ɵɵelementStart(10, \"option\", 19);\n    i0.ɵɵtext(11, \"Tous les groupes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, EvaluationsListComponent_div_5_option_12_Template, 2, 2, \"option\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\")(14, \"label\", 21);\n    i0.ɵɵtext(15, \"Projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"select\", 22);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_5_Template_select_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.filterProjet = $event);\n    })(\"change\", function EvaluationsListComponent_div_5_Template_select_change_16_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.applyFilters());\n    });\n    i0.ɵɵelementStart(17, \"option\", 19);\n    i0.ɵɵtext(18, \"Tous les projets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EvaluationsListComponent_div_5_option_19_Template, 2, 2, \"option\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 23)(21, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_5_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.resetFilters());\n    });\n    i0.ɵɵtext(22, \" R\\u00E9initialiser les filtres \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.filterGroupe);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.groupes);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.filterProjet);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.projets);\n  }\n}\nfunction EvaluationsListComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.updateMissingGroups());\n    });\n    i0.ɵɵtext(2, \" Mettre \\u00E0 jour les groupes manquants \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EvaluationsListComponent_div_7_tr_17_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const evaluation_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" ID: \", evaluation_r23.projet || \"Non disponible\", \" \");\n  }\n}\nfunction EvaluationsListComponent_div_7_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 34)(2, \"div\", 35)(3, \"div\")(4, \"div\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"td\", 34)(9, \"div\", 38);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\", 34)(12, \"div\", 38);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, EvaluationsListComponent_div_7_tr_17_div_14_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 34)(16, \"div\", 38);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\", 34)(19, \"span\", 40);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"td\", 41)(22, \"div\", 42)(23, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_7_tr_17_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const evaluation_r23 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.viewEvaluationDetails(evaluation_r23.rendu));\n    });\n    i0.ɵɵtext(24, \" Voir \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_7_tr_17_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const evaluation_r23 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.editEvaluation(evaluation_r23.rendu));\n    });\n    i0.ɵɵtext(26, \" Modifier \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const evaluation_r23 = ctx.$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", (evaluation_r23.etudiant == null ? null : evaluation_r23.etudiant.nom) || \"N/A\", \" \", (evaluation_r23.etudiant == null ? null : evaluation_r23.etudiant.prenom) || \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (evaluation_r23.etudiant == null ? null : evaluation_r23.etudiant.email) || \"Email non disponible\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((evaluation_r23.etudiant == null ? null : evaluation_r23.etudiant.groupe) || \"Non sp\\u00E9cifi\\u00E9\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (evaluation_r23.projetDetails == null ? null : evaluation_r23.projetDetails.titre) || (evaluation_r23.renduDetails == null ? null : evaluation_r23.renduDetails.projet == null ? null : evaluation_r23.renduDetails.projet.titre) || \"Projet inconnu\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(evaluation_r23.projetDetails == null ? null : evaluation_r23.projetDetails.titre) && !(evaluation_r23.renduDetails == null ? null : evaluation_r23.renduDetails.projet == null ? null : evaluation_r23.renduDetails.projet.titre));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r22.formatDate(evaluation_r23.dateEvaluation));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r22.getScoreClass(ctx_r22.getScoreTotal(evaluation_r23)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.getScoreTotal(evaluation_r23), \"/20 \");\n  }\n}\nfunction EvaluationsListComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"table\", 29)(2, \"thead\", 30)(3, \"tr\")(4, \"th\", 31);\n    i0.ɵɵtext(5, \" \\u00C9tudiant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 31);\n    i0.ɵɵtext(7, \" Groupe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 31);\n    i0.ɵɵtext(9, \" Projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 31);\n    i0.ɵɵtext(11, \" Date d'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 31);\n    i0.ɵɵtext(13, \" Score \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 31);\n    i0.ɵɵtext(15, \" Actions \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\", 32);\n    i0.ɵɵtemplate(17, EvaluationsListComponent_div_7_tr_17_Template, 27, 9, \"tr\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.filteredEvaluations);\n  }\n}\nfunction EvaluationsListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"p\", 47);\n    i0.ɵɵtext(2, \"Aucune \\u00E9valuation trouv\\u00E9e\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let EvaluationsListComponent = /*#__PURE__*/(() => {\n  class EvaluationsListComponent {\n    constructor(rendusService, evaluationService, router) {\n      this.rendusService = rendusService;\n      this.evaluationService = evaluationService;\n      this.router = router;\n      this.evaluations = [];\n      this.filteredEvaluations = [];\n      this.isLoading = true;\n      this.error = '';\n      this.searchTerm = '';\n      this.filterGroupe = '';\n      this.filterProjet = '';\n      this.groupes = [];\n      this.projets = [];\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.loadEvaluations();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadEvaluations() {\n      this.isLoading = true;\n      this.error = '';\n      this.evaluationService.getAllEvaluations().pipe(takeUntil(this.destroy$), catchError(err => {\n        this.error = 'Impossible de charger les évaluations. Veuillez réessayer plus tard.';\n        this.isLoading = false;\n        return of([]);\n      }), finalize(() => {\n        this.isLoading = false;\n      })).subscribe({\n        next: evaluations => {\n          if (!Array.isArray(evaluations)) {\n            this.error = 'Format de données incorrect. Veuillez réessayer plus tard.';\n            return;\n          }\n          this.evaluations = evaluations.map(evaluation => {\n            const evalWithDetails = evaluation;\n            if (!evalWithDetails.projetDetails || !evalWithDetails.projetDetails.titre) {\n              if (evalWithDetails.renduDetails && evalWithDetails.renduDetails.projet) {\n                evalWithDetails.projetDetails = evalWithDetails.renduDetails.projet;\n              }\n            }\n            return evalWithDetails;\n          });\n          this.extractGroupesAndProjets();\n          this.applyFilters();\n        }\n      });\n    }\n    extractGroupesAndProjets() {\n      const groupesSet = new Set();\n      this.evaluations.forEach(evaluation => {\n        const groupe = evaluation.etudiant?.groupe;\n        if (groupe && groupe.trim() !== '') {\n          groupesSet.add(groupe);\n        }\n      });\n      this.groupes = Array.from(groupesSet).sort();\n      const projetsMap = new Map();\n      this.evaluations.forEach(evaluation => {\n        if (evaluation.projetDetails && evaluation.projetDetails._id) {\n          projetsMap.set(evaluation.projetDetails._id, evaluation.projetDetails);\n        }\n      });\n      this.projets = Array.from(projetsMap.values());\n    }\n    applyFilters() {\n      let results = this.evaluations;\n      // Filtre par terme de recherche\n      if (this.searchTerm.trim() !== '') {\n        const term = this.searchTerm.toLowerCase().trim();\n        results = results.filter(evaluation => evaluation.etudiant?.nom?.toLowerCase().includes(term) || evaluation.etudiant?.prenom?.toLowerCase().includes(term) || evaluation.projetDetails?.titre?.toLowerCase().includes(term));\n      }\n      // Filtre par groupe\n      if (this.filterGroupe) {\n        results = results.filter(evaluation => evaluation.etudiant?.groupe === this.filterGroupe);\n      }\n      // Filtre par projet\n      if (this.filterProjet) {\n        results = results.filter(evaluation => evaluation.projetDetails?._id === this.filterProjet);\n      }\n      this.filteredEvaluations = results;\n    }\n    onSearchChange() {\n      this.applyFilters();\n    }\n    resetFilters() {\n      this.searchTerm = '';\n      this.filterGroupe = '';\n      this.filterProjet = '';\n      this.applyFilters();\n    }\n    editEvaluation(renduId) {\n      this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\n    }\n    viewEvaluationDetails(renduId) {\n      this.router.navigate(['/admin/projects/evaluation-details', renduId]);\n    }\n    getScoreTotal(evaluation) {\n      if (!evaluation.scores) return 0;\n      const scores = evaluation.scores;\n      return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n    }\n    getScoreClass(score) {\n      if (score >= 16) return 'text-green-600 bg-green-100';\n      if (score >= 12) return 'text-blue-600 bg-blue-100';\n      if (score >= 8) return 'text-yellow-600 bg-yellow-100';\n      return 'text-red-600 bg-red-100';\n    }\n    formatDate(date) {\n      if (!date) return 'Non disponible';\n      return new Date(date).toLocaleDateString();\n    }\n    // Ajouter cette méthode pour mettre à jour les groupes manquants\n    updateMissingGroups() {\n      if (!confirm('Voulez-vous mettre à jour les groupes manquants des étudiants?')) {\n        return;\n      }\n      this.isLoading = true;\n      this.evaluationService.updateMissingGroups().subscribe({\n        next: response => {\n          alert(`${response.updatedCount} étudiants mis à jour avec leur groupe.`);\n          this.loadEvaluations();\n        },\n        error: err => {\n          alert('Erreur lors de la mise à jour des groupes.');\n          this.isLoading = false;\n        }\n      });\n    }\n    static {\n      this.ɵfac = function EvaluationsListComponent_Factory(t) {\n        return new (t || EvaluationsListComponent)(i0.ɵɵdirectiveInject(i1.RendusService), i0.ɵɵdirectiveInject(i2.EvaluationService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EvaluationsListComponent,\n        selectors: [[\"app-evaluations-list\"]],\n        decls: 9,\n        vars: 6,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"text-2xl\", \"font-bold\", \"mb-6\"], [\"class\", \"flex justify-center items-center py-10\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-lg shadow-md p-4 mb-6\", 4, \"ngIf\"], [\"class\", \"flex justify-end mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white shadow-md rounded-lg overflow-hidden\", 4, \"ngIf\"], [\"class\", \"bg-white shadow-md rounded-lg p-6 text-center\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-10\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-indigo-500\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"bg-red-200\", \"hover:bg-red-300\", \"text-red-800\", \"font-bold\", \"py-1\", \"px-4\", \"rounded\", \"focus:outline-none\", \"focus:shadow-outline\", 3, \"click\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-4\", \"mb-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-4\", \"gap-4\"], [\"for\", \"search\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [\"type\", \"text\", \"id\", \"search\", \"placeholder\", \"Nom, pr\\u00E9nom, projet...\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"shadow-sm\", \"focus:outline-none\", \"focus:ring-indigo-500\", \"focus:border-indigo-500\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"for\", \"groupe\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [\"id\", \"groupe\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"shadow-sm\", \"focus:outline-none\", \"focus:ring-indigo-500\", \"focus:border-indigo-500\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"projet\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [\"id\", \"projet\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"shadow-sm\", \"focus:outline-none\", \"focus:ring-indigo-500\", \"focus:border-indigo-500\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"flex\", \"items-end\"], [1, \"w-full\", \"px-4\", \"py-2\", \"bg-gray-200\", \"text-gray-700\", \"rounded-md\", \"hover:bg-gray-300\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-gray-500\", 3, \"click\"], [3, \"value\"], [1, \"flex\", \"justify-end\", \"mb-4\"], [1, \"bg-indigo-600\", \"hover:bg-indigo-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\", \"focus:outline-none\", \"focus:shadow-outline\", 3, \"click\"], [1, \"bg-white\", \"shadow-md\", \"rounded-lg\", \"overflow-hidden\"], [1, \"min-w-full\", \"divide-y\", \"divide-gray-200\"], [1, \"bg-gray-50\"], [\"scope\", \"col\", 1, \"px-6\", \"py-3\", \"text-left\", \"text-xs\", \"font-medium\", \"text-gray-500\", \"uppercase\", \"tracking-wider\"], [1, \"bg-white\", \"divide-y\", \"divide-gray-200\"], [4, \"ngFor\", \"ngForOf\"], [1, \"px-6\", \"py-4\", \"whitespace-nowrap\"], [1, \"flex\", \"items-center\"], [1, \"text-sm\", \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"text-sm\", \"text-gray-900\"], [\"class\", \"text-xs text-red-500\", 4, \"ngIf\"], [1, \"px-2\", \"py-1\", \"rounded-full\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [1, \"px-6\", \"py-4\", \"whitespace-nowrap\", \"text-sm\", \"font-medium\"], [1, \"flex\", \"space-x-2\"], [1, \"text-indigo-600\", \"hover:text-indigo-900\", 3, \"click\"], [1, \"text-green-600\", \"hover:text-green-900\", 3, \"click\"], [1, \"text-xs\", \"text-red-500\"], [1, \"bg-white\", \"shadow-md\", \"rounded-lg\", \"p-6\", \"text-center\"], [1, \"text-gray-500\"]],\n        template: function EvaluationsListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\", 1);\n            i0.ɵɵtext(2, \"Liste des \\u00E9valuations\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(3, EvaluationsListComponent_div_3_Template, 2, 0, \"div\", 2);\n            i0.ɵɵtemplate(4, EvaluationsListComponent_div_4_Template, 6, 1, \"div\", 3);\n            i0.ɵɵtemplate(5, EvaluationsListComponent_div_5_Template, 23, 5, \"div\", 4);\n            i0.ɵɵtemplate(6, EvaluationsListComponent_div_6_Template, 3, 0, \"div\", 5);\n            i0.ɵɵtemplate(7, EvaluationsListComponent_div_7_Template, 18, 1, \"div\", 6);\n            i0.ɵɵtemplate(8, EvaluationsListComponent_div_8_Template, 3, 0, \"div\", 7);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.filteredEvaluations.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.filteredEvaluations.length === 0);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n        styles: [\".evaluations-container[_ngcontent-%COMP%]{padding:20px}.filters[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:15px;margin-bottom:20px;padding:15px;background-color:#f5f5f5;border-radius:5px}.filter-item[_ngcontent-%COMP%]{flex:1;min-width:200px}.evaluations-table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse;margin-top:20px}.evaluations-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .evaluations-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:12px;text-align:left;border-bottom:1px solid #ddd}.evaluations-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:#f2f2f2;font-weight:700}.evaluations-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#f5f5f5}.actions-cell[_ngcontent-%COMP%]{display:flex;gap:10px}.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:50px 0}.no-evaluations[_ngcontent-%COMP%]{text-align:center;margin:50px 0;color:#666}.score-badge[_ngcontent-%COMP%]{padding:5px 10px;border-radius:15px;font-weight:700;display:inline-block}.score-high[_ngcontent-%COMP%]{background-color:#c8e6c9;color:#2e7d32}.score-medium[_ngcontent-%COMP%]{background-color:#fff9c4;color:#f57f17}.score-low[_ngcontent-%COMP%]{background-color:#ffcdd2;color:#c62828}\"]\n      });\n    }\n  }\n  return EvaluationsListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}