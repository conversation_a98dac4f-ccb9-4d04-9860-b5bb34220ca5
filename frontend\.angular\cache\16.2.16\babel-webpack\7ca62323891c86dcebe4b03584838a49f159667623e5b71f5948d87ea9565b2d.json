{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, tap, map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let EquipeService = /*#__PURE__*/(() => {\n  class EquipeService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.urlBackend}teams`;\n      console.log('API URL:', this.apiUrl);\n    }\n    getEquipes() {\n      console.log('Fetching teams from:', this.apiUrl);\n      return this.http.get(this.apiUrl).pipe(tap(data => console.log('Teams received:', data)), catchError(this.handleError));\n    }\n    getEquipe(id) {\n      console.log(`Fetching team with id ${id} from: ${this.apiUrl}/${id}`);\n      return this.http.get(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Team received:', data)), catchError(this.handleError));\n    }\n    addEquipe(equipe) {\n      console.log('Adding team:', equipe);\n      return this.http.post(this.apiUrl, equipe).pipe(tap(data => console.log('Team added, response:', data)), catchError(this.handleError));\n    }\n    updateEquipe(id, equipe) {\n      console.log(`Updating team with id ${id}:`, equipe);\n      return this.http.put(`${this.apiUrl}/${id}`, equipe).pipe(tap(data => console.log('Team updated, response:', data)), catchError(this.handleError));\n    }\n    deleteEquipe(id) {\n      console.log(`Deleting team with id ${id}`);\n      console.log(`API URL: ${this.apiUrl}/${id}`);\n      return this.http.delete(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Team deleted, response:', data)), catchError(error => {\n        console.error('Error deleting team:', error);\n        console.error('Request URL:', `${this.apiUrl}/${id}`);\n        return this.handleError(error);\n      }));\n    }\n    addMembreToEquipe(teamId, membre) {\n      console.log(`Adding member to team ${teamId}:`, membre);\n      // Créer l'objet attendu par le backend\n      const memberData = {\n        userId: membre.id,\n        role: membre.role || 'membre' // Utiliser le rôle spécifié ou \"membre\" par défaut\n      };\n\n      console.log('Sending to backend:', memberData);\n      console.log('Team ID type:', typeof teamId, 'value:', teamId);\n      console.log('User ID type:', typeof membre.id, 'value:', membre.id);\n      // Utiliser la route directe pour ajouter un membre à une équipe\n      return this.http.post(`${this.apiUrl}/${teamId}/members`, memberData).pipe(tap(data => console.log('Member added, response:', data)), catchError(this.handleError));\n    }\n    removeMembreFromEquipe(teamId, membreId) {\n      console.log(`Removing member ${membreId} from team ${teamId}`);\n      console.log(`API URL: ${this.apiUrl}/${teamId}/members/${membreId}`);\n      // Utiliser la route directe pour supprimer un membre d'une équipe\n      return this.http.delete(`${this.apiUrl}/${teamId}/members/${membreId}`).pipe(tap(data => console.log('Member removed, response:', data)), catchError(error => {\n        console.error('Error removing member:', error);\n        console.error('Request URL:', `${this.apiUrl}/${teamId}/members/${membreId}`);\n        return this.handleError(error);\n      }));\n    }\n    /**\n     * Récupère les détails des membres d'une équipe\n     * @param teamId ID de l'équipe\n     * @returns Observable contenant la liste des membres avec leurs détails\n     */\n    getTeamMembers(teamId) {\n      console.log(`Fetching team members for team ${teamId}`);\n      // Utiliser la route de l'équipe pour récupérer les détails de l'équipe, qui contient les membres\n      return this.http.get(`${this.apiUrl}/${teamId}`).pipe(map(team => {\n        console.log('Team data received:', team);\n        // Transformer les IDs des membres en objets avec l'ID et le rôle\n        if (team && team.members) {\n          return team.members.map(memberId => ({\n            user: memberId,\n            role: 'membre',\n            _id: memberId // Utiliser l'ID du membre comme ID du TeamMember\n          }));\n        }\n\n        return [];\n      }), tap(data => console.log('Team members processed:', data)), catchError(this.handleError));\n    }\n    handleError(error) {\n      let errorMessage = '';\n      if (error.error instanceof ErrorEvent) {\n        // Erreur côté client\n        errorMessage = `Erreur client: ${error.error.message}`;\n      } else {\n        // Erreur côté serveur\n        const status = error.status;\n        const message = error.error?.message || error.statusText;\n        errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\n        // Log des détails supplémentaires pour le débogage\n        console.error('Error details:', {\n          status: error.status,\n          statusText: error.statusText,\n          url: error.url,\n          error: error.error\n        });\n        if (status === 0) {\n          console.error(\"Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau.\");\n        }\n      }\n      console.error('API Error:', errorMessage);\n      return throwError(() => new Error(errorMessage));\n    }\n    static {\n      this.ɵfac = function EquipeService_Factory(t) {\n        return new (t || EquipeService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: EquipeService,\n        factory: EquipeService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return EquipeService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}