{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/projets.service\";\nimport * as i4 from \"src/app/services/rendus.service\";\nimport * as i5 from \"src/app/services/authuser.service\";\nimport * as i6 from \"@angular/common\";\nfunction ProjectSubmissionComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_5_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \" La description est requise et doit contenir au moins 10 caract\\u00E8res. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_5_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \" Veuillez s\\u00E9lectionner au moins un fichier. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_5_div_28_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r8 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", file_r8.name, \" (\", (file_r8.size / 1024).toFixed(2), \" KB) \");\n  }\n}\nfunction ProjectSubmissionComponent_div_5_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"p\", 9);\n    i0.ɵɵtext(2, \"Fichiers s\\u00E9lectionn\\u00E9s:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 23);\n    i0.ɵɵtemplate(4, ProjectSubmissionComponent_div_5_div_28_li_4_Template, 2, 2, \"li\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedFiles);\n  }\n}\nfunction ProjectSubmissionComponent_div_5_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumettre le projet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_5_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumission en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 7)(2, \"h2\", 8);\n    i0.ɵɵtext(3, \" Informations sur le projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"span\", 9);\n    i0.ɵɵtext(6, \"Titre:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"span\", 9);\n    i0.ɵɵtext(10, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\")(13, \"span\", 9);\n    i0.ɵɵtext(14, \"Date limite:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"form\", 10);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectSubmissionComponent_div_5_Template_form_ngSubmit_17_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onSubmit());\n    });\n    i0.ɵɵelementStart(18, \"div\", 11)(19, \"label\", 12);\n    i0.ɵɵtext(20, \"Description de votre travail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"textarea\", 13);\n    i0.ɵɵtemplate(22, ProjectSubmissionComponent_div_5_div_22_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 15)(24, \"label\", 16);\n    i0.ɵɵtext(25, \"Fichiers du projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 17);\n    i0.ɵɵlistener(\"change\", function ProjectSubmissionComponent_div_5_Template_input_change_26_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onFileChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, ProjectSubmissionComponent_div_5_div_27_Template, 2, 0, \"div\", 14);\n    i0.ɵɵtemplate(28, ProjectSubmissionComponent_div_5_div_28_Template, 5, 1, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 19)(30, \"button\", 20);\n    i0.ɵɵtemplate(31, ProjectSubmissionComponent_div_5_span_31_Template, 2, 0, \"span\", 4);\n    i0.ɵɵtemplate(32, ProjectSubmissionComponent_div_5_span_32_Template, 2, 0, \"span\", 4);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_4_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.projet.titre, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.projet.description, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 10, ctx_r1.projet.dateLimite, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.submissionForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r1.submissionForm.get(\"description\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r1.submissionForm.get(\"description\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFiles.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFiles.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.submissionForm.invalid || ctx_r1.selectedFiles.length === 0 || ctx_r1.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n  }\n}\n// Composant pour soumettre un projet\nexport let ProjectSubmissionComponent = /*#__PURE__*/(() => {\n  class ProjectSubmissionComponent {\n    constructor(fb, route, router, projetService, rendusService, authService) {\n      this.fb = fb;\n      this.route = route;\n      this.router = router;\n      this.projetService = projetService;\n      this.rendusService = rendusService;\n      this.authService = authService;\n      this.projetId = '';\n      this.selectedFiles = [];\n      this.isLoading = true;\n      this.isSubmitting = false;\n      this.submissionForm = this.fb.group({\n        description: ['', [Validators.required, Validators.minLength(10)]]\n      });\n    }\n    ngOnInit() {\n      this.projetId = this.route.snapshot.paramMap.get('id') || '';\n      this.loadProjetDetails();\n    }\n    loadProjetDetails() {\n      this.isLoading = true;\n      this.projetService.getProjetById(this.projetId).subscribe({\n        next: projet => {\n          this.projet = projet;\n          this.isLoading = false;\n        },\n        error: err => {\n          console.error('Erreur lors du chargement du projet', err);\n          this.isLoading = false;\n          this.router.navigate(['/projects']);\n        }\n      });\n    }\n    onFileChange(event) {\n      const input = event.target;\n      if (input.files) {\n        this.selectedFiles = Array.from(input.files);\n      }\n    }\n    onSubmit() {\n      if (this.submissionForm.invalid || this.selectedFiles.length === 0) {\n        return;\n      }\n      this.isSubmitting = true;\n      const formData = new FormData();\n      formData.append('projet', this.projetId);\n      formData.append('etudiant', this.authService.getCurrentUserId() || '');\n      formData.append('description', this.submissionForm.value.description);\n      this.selectedFiles.forEach(file => {\n        formData.append('fichiers', file);\n      });\n      this.rendusService.submitRendu(formData).subscribe({\n        next: response => {\n          alert('Votre projet a été soumis avec succès');\n          this.router.navigate(['/projects']);\n        },\n        error: err => {\n          console.error('Erreur lors de la soumission du projet', err);\n          alert('Une erreur est survenue lors de la soumission du projet');\n          this.isSubmitting = false;\n        }\n      });\n    }\n    static {\n      this.ɵfac = function ProjectSubmissionComponent_Factory(t) {\n        return new (t || ProjectSubmissionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ProjetService), i0.ɵɵdirectiveInject(i4.RendusService), i0.ɵɵdirectiveInject(i5.AuthuserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectSubmissionComponent,\n        selectors: [[\"app-project-submission\"]],\n        decls: 6,\n        vars: 2,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"p-4\", \"md:p-6\"], [1, \"max-w-4xl\", \"mx-auto\", \"bg-white\", \"rounded-xl\", \"shadow-md\", \"p-6\"], [1, \"text-2xl\", \"font-bold\", \"mb-6\", \"text-[#4f5fad]\"], [\"class\", \"flex justify-center my-8\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\"], [1, \"mb-6\", \"p-4\", \"bg-[#edf1f4]\", \"rounded-lg\"], [1, \"text-xl\", \"font-semibold\", \"mb-2\", \"text-[#4f5fad]\"], [1, \"font-medium\", \"text-[#6d6870]\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [\"for\", \"description\", 1, \"block\", \"text-[#6d6870]\", \"font-medium\", \"mb-2\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"5\", \"placeholder\", \"D\\u00E9crivez votre travail, les fonctionnalit\\u00E9s impl\\u00E9ment\\u00E9es, les difficult\\u00E9s rencontr\\u00E9es, etc.\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-[#bdc6cc]\", \"rounded\", \"focus:outline-none\", \"focus:ring-1\", \"focus:ring-[#4f5fad]/20\", \"focus:border-[#4f5fad]\"], [\"class\", \"text-[#ff6b69] mt-1\", 4, \"ngIf\"], [1, \"mb-6\"], [\"for\", \"fichiers\", 1, \"block\", \"text-[#6d6870]\", \"font-medium\", \"mb-2\"], [\"type\", \"file\", \"id\", \"fichiers\", \"multiple\", \"\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-[#bdc6cc]\", \"rounded\", \"focus:outline-none\", \"focus:ring-1\", \"focus:ring-[#4f5fad]/20\", \"focus:border-[#4f5fad]\", 3, \"change\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [1, \"flex\", \"justify-end\"], [\"type\", \"submit\", 1, \"px-6\", \"py-2\", \"bg-[#4f5fad]\", \"text-white\", \"rounded\", \"hover:bg-[#3d4a85]\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"text-[#ff6b69]\", \"mt-1\"], [1, \"mt-2\"], [1, \"list-disc\", \"pl-5\"], [4, \"ngFor\", \"ngForOf\"]],\n        template: function ProjectSubmissionComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵtext(3, \"Soumettre un projet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, ProjectSubmissionComponent_div_4_Template, 2, 0, \"div\", 3);\n            i0.ɵɵtemplate(5, ProjectSubmissionComponent_div_5_Template, 33, 13, \"div\", 4);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.projet && !ctx.isLoading);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.DatePipe],\n        styles: [\".submission-form[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}.form-section[_ngcontent-%COMP%]{margin-bottom:2rem}.file-upload[_ngcontent-%COMP%]{border:2px dashed #ccc;padding:1.5rem;text-align:center;border-radius:.5rem;margin-bottom:1rem}.file-upload[_ngcontent-%COMP%]:hover{border-color:#6366f1}.file-list[_ngcontent-%COMP%]{margin-top:1rem}.file-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:.5rem;background-color:#f9fafb;border-radius:.25rem;margin-bottom:.5rem}\"]\n      });\n    }\n  }\n  return ProjectSubmissionComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}