{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/data.service\";\nimport * as i3 from \"@app/services/planning.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction PlanningFormComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.errorMessage, \" \");\n  }\n}\nfunction PlanningFormComponent_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Titre is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent_div_13_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"At least 3 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, PlanningFormComponent_div_13_span_1_Template, 2, 0, \"span\", 23);\n    i0.ɵɵtemplate(2, PlanningFormComponent_div_13_span_2_Template, 2, 0, \"span\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.planningForm.get(\"titre\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.planningForm.get(\"titre\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction PlanningFormComponent_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r7._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r7.username);\n  }\n}\nfunction PlanningFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \" Please select at least one participant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent__svg_svg_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 25);\n    i0.ɵɵelement(1, \"circle\", 26)(2, \"path\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\nexport let PlanningFormComponent = /*#__PURE__*/(() => {\n  class PlanningFormComponent {\n    constructor(fb, userService, planningService, router) {\n      this.fb = fb;\n      this.userService = userService;\n      this.planningService = planningService;\n      this.router = router;\n      this.isLoading = false;\n      this.errorMessage = null;\n      this.users$ = this.userService.getAllUsers();\n    }\n    ngOnInit() {\n      this.planningForm = this.fb.group({\n        titre: ['', [Validators.required, Validators.minLength(3)]],\n        description: [''],\n        lieu: [''],\n        dateDebut: ['', Validators.required],\n        dateFin: ['', Validators.required],\n        participants: [[], Validators.required]\n      });\n    }\n    submit() {\n      console.log('Submit method called');\n      console.log('Form valid:', this.planningForm.valid);\n      console.log('Form values:', this.planningForm.value);\n      if (this.planningForm.valid) {\n        this.isLoading = true;\n        this.errorMessage = null;\n        // Extract form values\n        const formValues = this.planningForm.value;\n        // Create a simplified planning object with just the fields the API expects\n        const planningData = {\n          titre: formValues.titre,\n          description: formValues.description || '',\n          dateDebut: formValues.dateDebut,\n          dateFin: formValues.dateFin,\n          lieu: formValues.lieu || '',\n          participants: formValues.participants || []\n        };\n        console.log('Planning data to submit:', planningData);\n        // Call the createPlanning method to add the new planning\n        this.planningService.createPlanning(planningData).subscribe({\n          next: newPlanning => {\n            console.log('Planning created successfully:', newPlanning);\n            this.isLoading = false;\n            // Navigate to plannings list page after successful creation\n            this.router.navigate(['/plannings']);\n          },\n          error: error => {\n            console.error('Error creating planning:', error);\n            console.error('Error details:', error.error || error.message || error);\n            this.isLoading = false;\n            // If there's a specific error message from the server, display it\n            if (error.error && error.error.message) {\n              console.error('Server error message:', error.error.message);\n              this.errorMessage = error.error.message;\n            } else {\n              this.errorMessage = 'Une erreur est survenue lors de la création du planning';\n            }\n          }\n        });\n      } else {\n        console.log('Form validation errors:', this.getFormValidationErrors());\n      }\n    }\n    // Helper method to get form validation errors\n    getFormValidationErrors() {\n      const errors = {};\n      Object.keys(this.planningForm.controls).forEach(key => {\n        const control = this.planningForm.get(key);\n        if (control && control.errors) {\n          errors[key] = control.errors;\n        }\n      });\n      return errors;\n    }\n    static {\n      this.ɵfac = function PlanningFormComponent_Factory(t) {\n        return new (t || PlanningFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataService), i0.ɵɵdirectiveInject(i3.PlanningService), i0.ɵɵdirectiveInject(i4.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PlanningFormComponent,\n        selectors: [[\"app-planning-form\"]],\n        decls: 41,\n        vars: 12,\n        consts: [[1, \"bg-gray-50\", \"p-5\", \"sm:p-6\", \"rounded-xl\"], [1, \"mb-4\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\", \"mt-1\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-y-4\", \"gap-x-4\", \"sm:grid-cols-6\"], [1, \"sm:col-span-3\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"class\", \"text-sm text-red-600 mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"type\", \"date\", \"formControlName\", \"dateDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"type\", \"date\", \"formControlName\", \"dateFin\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"sm:col-span-6\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"mt-6\", \"flex\", \"justify-end\"], [\"type\", \"submit\", 1, \"ml-3\", \"inline-flex\", \"justify-center\", \"py-2\", \"px-4\", \"border\", \"border-transparent\", \"shadow-sm\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-offset-2\", \"focus:ring-purple-500\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-colors\", 3, \"disabled\"], [\"class\", \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"mb-4\", \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\"], [1, \"text-sm\", \"text-red-600\", \"mt-1\"], [4, \"ngIf\"], [3, \"value\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-2\", \"h-4\", \"w-4\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"]],\n        template: function PlanningFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n            i0.ɵɵtext(3, \"Planning Form\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\", 3);\n            i0.ɵɵtext(5, \"Fill in the details for the event\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"form\", 4);\n            i0.ɵɵlistener(\"ngSubmit\", function PlanningFormComponent_Template_form_ngSubmit_6_listener() {\n              return ctx.submit();\n            });\n            i0.ɵɵtemplate(7, PlanningFormComponent_div_7_Template, 2, 1, \"div\", 5);\n            i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"label\", 8);\n            i0.ɵɵtext(11, \"Titre\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(12, \"input\", 9);\n            i0.ɵɵtemplate(13, PlanningFormComponent_div_13_Template, 3, 2, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 7)(15, \"label\", 8);\n            i0.ɵɵtext(16, \"Lieu\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"input\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 7)(19, \"label\", 8);\n            i0.ɵɵtext(20, \"Date de d\\u00E9but\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(21, \"input\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 7)(23, \"label\", 8);\n            i0.ɵɵtext(24, \"Date de fin\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"input\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 14)(27, \"label\", 8);\n            i0.ɵɵtext(28, \"Participants\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"select\", 15);\n            i0.ɵɵtemplate(30, PlanningFormComponent_option_30_Template, 2, 2, \"option\", 16);\n            i0.ɵɵpipe(31, \"async\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(32, PlanningFormComponent_div_32_Template, 2, 0, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"div\", 14)(34, \"label\", 8);\n            i0.ɵɵtext(35, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(36, \"textarea\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"div\", 18)(38, \"button\", 19);\n            i0.ɵɵtemplate(39, PlanningFormComponent__svg_svg_39_Template, 3, 0, \"svg\", 20);\n            i0.ɵɵtext(40);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_5_0;\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formGroup\", ctx.planningForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"border-red-300\", ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(31, 10, ctx.users$));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", ctx.planningForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Saving...\" : \"Save Planning\", \" \");\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.AsyncPipe]\n      });\n    }\n  }\n  return PlanningFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}