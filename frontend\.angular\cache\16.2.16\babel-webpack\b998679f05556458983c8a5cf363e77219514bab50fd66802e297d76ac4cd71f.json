{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nexport let HighlightPresencePipe = /*#__PURE__*/(() => {\n  class HighlightPresencePipe {\n    constructor(sanitizer) {\n      this.sanitizer = sanitizer;\n    }\n    transform(value) {\n      if (!value) {\n        return this.sanitizer.bypassSecurityTrustHtml('');\n      }\n      // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n      const formattedText = value.replace(/\\(presence obligatoire\\)/gi, '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>');\n      // Sanitize le HTML pour éviter les problèmes de sécurité\n      return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n    }\n    static {\n      this.ɵfac = function HighlightPresencePipe_Factory(t) {\n        return new (t || HighlightPresencePipe)(i0.ɵɵdirectiveInject(i1.<PERSON><PERSON><PERSON><PERSON><PERSON>, 16));\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"highlightPresence\",\n        type: HighlightPresencePipe,\n        pure: true\n      });\n    }\n  }\n  return HighlightPresencePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}