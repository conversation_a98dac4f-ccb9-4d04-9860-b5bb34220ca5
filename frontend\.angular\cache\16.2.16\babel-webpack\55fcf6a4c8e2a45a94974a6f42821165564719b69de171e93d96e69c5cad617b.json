{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction EquipeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.error = \"\");\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction EquipeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"span\", 42);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EquipeComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtext(1, \" Aucune \\u00E9quipe trouv\\u00E9e. Cr\\u00E9ez votre premi\\u00E8re \\u00E9quipe ci-dessous. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_12_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_12_tr_15_Template_button_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const equipe_r12 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.editEquipe(equipe_r12));\n    });\n    i0.ɵɵtext(11, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_12_tr_15_Template_button_click_12_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const equipe_r12 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r12._id && ctx_r15.deleteEquipe(equipe_r12._id));\n    });\n    i0.ɵɵtext(13, \" Supprimer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_12_tr_15_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const equipe_r12 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.showMembreModal(equipe_r12));\n    });\n    i0.ɵɵtext(15, \" G\\u00E9rer membres \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const equipe_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(equipe_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(equipe_r12.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(equipe_r12.admin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", (equipe_r12.members == null ? null : equipe_r12.members.length) || 0, \" membres\");\n  }\n}\nfunction EquipeComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"table\", 45)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Admin\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Membres\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, EquipeComponent_div_12_tr_15_Template, 16, 4, \"tr\", 46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.equipes);\n  }\n}\nfunction EquipeComponent_span_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 50);\n  }\n}\nfunction EquipeComponent_button_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_button_41_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.cancelEdit());\n    });\n    i0.ɵɵtext(1, \" Annuler \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_50_div_6_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 61)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_50_div_6_li_2_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const membreId_r24 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.removeMembreFromEquipe(ctx_r25.selectedEquipe._id, membreId_r24));\n    });\n    i0.ɵɵtext(4, \" Retirer \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const membreId_r24 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(membreId_r24);\n  }\n}\nfunction EquipeComponent_div_50_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"ul\", 59);\n    i0.ɵɵtemplate(2, EquipeComponent_div_50_div_6_li_2_Template, 5, 1, \"li\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.selectedEquipe.members);\n  }\n}\nfunction EquipeComponent_div_50_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 23);\n    i0.ɵɵtext(1, \"Aucun membre dans cette \\u00E9quipe\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"h6\");\n    i0.ɵɵtext(5, \"Membres actuels:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, EquipeComponent_div_50_div_6_Template, 3, 1, \"div\", 52);\n    i0.ɵɵtemplate(7, EquipeComponent_div_50_ng_template_7_Template, 2, 0, \"ng-template\", null, 53, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 14)(10, \"h6\");\n    i0.ɵɵtext(11, \"Ajouter un membre:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 54);\n    i0.ɵɵelement(13, \"input\", 55, 56);\n    i0.ɵɵelementStart(15, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function EquipeComponent_div_50_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const _r22 = i0.ɵɵreference(14);\n      const ctx_r27 = i0.ɵɵnextContext();\n      ctx_r27.addMembreToEquipe(ctx_r27.selectedEquipe._id, _r22.value);\n      return i0.ɵɵresetView(_r22.value = \"\");\n    });\n    i0.ɵɵtext(16, \" Ajouter \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"small\", 23);\n    i0.ɵɵtext(18, \"Entrez l'ID du membre \\u00E0 ajouter \\u00E0 l'\\u00E9quipe\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 58)(20, \"p\", 12)(21, \"strong\");\n    i0.ɵɵtext(22, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Pour ajouter un membre, vous devez d'abord cr\\u00E9er le membre dans la section des membres. \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r20 = i0.ɵɵreference(8);\n    const _r22 = i0.ɵɵreference(14);\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u00C9quipe: \", ctx_r8.selectedEquipe.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.selectedEquipe.members && ctx_r8.selectedEquipe.members.length > 0)(\"ngIfElse\", _r20);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", !ctx_r8.selectedEquipe || !ctx_r8.selectedEquipe._id || !_r22.value);\n  }\n}\nexport let EquipeComponent = /*#__PURE__*/(() => {\n  class EquipeComponent {\n    constructor(equipeService, membreService) {\n      this.equipeService = equipeService;\n      this.membreService = membreService;\n      this.equipes = [];\n      this.newEquipe = {\n        name: '',\n        description: ''\n      };\n      this.selectedEquipe = null;\n      this.isEditing = false;\n      this.membres = [];\n      this.loading = false;\n      this.error = '';\n    }\n    ngOnInit() {\n      this.loadEquipes();\n      this.loadMembres();\n    }\n    loadEquipes() {\n      this.loading = true;\n      this.equipeService.getEquipes().subscribe({\n        next: data => {\n          console.log('Loaded equipes:', data);\n          this.equipes = data;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading equipes:', error);\n          this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n          this.loading = false;\n        }\n      });\n    }\n    loadMembres() {\n      this.loading = true;\n      this.membreService.getMembres().subscribe({\n        next: data => {\n          console.log('Loaded membres:', data);\n          this.membres = data;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading membres:', error);\n          this.error = 'Erreur lors du chargement des membres: ' + error.message;\n          this.loading = false;\n        }\n      });\n    }\n    addEquipe() {\n      console.log('Adding equipe:', this.newEquipe);\n      if (!this.newEquipe.name) {\n        console.error('Team name is required');\n        this.error = 'Le nom de l\\'équipe est requis';\n        return;\n      }\n      this.loading = true;\n      this.error = '';\n      this.equipeService.addEquipe(this.newEquipe).subscribe({\n        next: response => {\n          console.log('Equipe added successfully:', response);\n          this.loadEquipes();\n          this.newEquipe = {\n            name: '',\n            description: ''\n          }; // Clear input\n          this.loading = false;\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe créée avec succès!';\n          this.error = ''; // Effacer les erreurs précédentes\n          alert(successMessage);\n        },\n        error: error => {\n          console.error('Error adding equipe:', error);\n          this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n    editEquipe(equipe) {\n      this.isEditing = true;\n      // Créer une copie profonde pour éviter de modifier l'objet original\n      this.newEquipe = {\n        _id: equipe._id,\n        name: equipe.name || '',\n        description: equipe.description || '',\n        admin: equipe.admin,\n        members: equipe.members ? [...equipe.members] : []\n      };\n    }\n    cancelEdit() {\n      this.isEditing = false;\n      this.newEquipe = {\n        name: '',\n        description: ''\n      };\n      this.error = ''; // Effacer les erreurs\n    }\n\n    updateSelectedEquipe() {\n      if (!this.newEquipe.name) {\n        console.error('Team name is required');\n        this.error = 'Le nom de l\\'équipe est requis';\n        return;\n      }\n      if (this.newEquipe._id) {\n        this.loading = true;\n        this.error = '';\n        this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n          next: updatedEquipe => {\n            console.log('Team updated successfully:', updatedEquipe);\n            this.loadEquipes();\n            this.isEditing = false;\n            this.newEquipe = {\n              name: '',\n              description: ''\n            };\n            this.loading = false;\n            // Afficher un message de succès temporaire\n            const successMessage = 'Équipe mise à jour avec succès!';\n            alert(successMessage);\n          },\n          error: error => {\n            console.error('Error updating team:', error);\n            this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n            this.loading = false;\n          }\n        });\n      } else {\n        this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n      }\n    }\n    deleteEquipe(id) {\n      if (!id) {\n        console.error('ID is undefined');\n        this.error = 'ID de l\\'équipe non défini';\n        return;\n      }\n      if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n        this.loading = true;\n        this.error = '';\n        this.equipeService.deleteEquipe(id).subscribe({\n          next: response => {\n            console.log('Team deleted successfully:', response);\n            // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n            if (this.isEditing && this.newEquipe._id === id) {\n              this.isEditing = false;\n              this.newEquipe = {\n                name: '',\n                description: ''\n              };\n            }\n            this.loadEquipes();\n            this.loading = false;\n            // Afficher un message de succès\n            alert('Équipe supprimée avec succès');\n          },\n          error: error => {\n            console.error('Error deleting team:', error);\n            this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n            this.loading = false;\n          }\n        });\n      }\n    }\n    showMembreModal(equipe) {\n      this.selectedEquipe = equipe;\n      // Ouvrir le modal avec Bootstrap 5\n      const modalRef = document.getElementById('membreModal');\n      if (modalRef) {\n        try {\n          // Ensure Bootstrap is properly loaded\n          if (typeof window !== 'undefined' && window.bootstrap) {\n            const modal = new window.bootstrap.Modal(modalRef);\n            modal.show();\n          } else {\n            console.error('Bootstrap is not loaded properly');\n            alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n          }\n        } catch (error) {\n          console.error('Error showing modal:', error);\n        }\n      } else {\n        console.error('Modal element not found');\n      }\n    }\n    addMembreToEquipe(teamId, membreId) {\n      if (!teamId) {\n        console.error('Team ID is undefined');\n        alert('ID de l\\'équipe non défini');\n        return;\n      }\n      if (!membreId || membreId.trim() === '') {\n        console.error('Member ID is empty');\n        alert('L\\'ID du membre est requis');\n        return;\n      }\n      this.loading = true;\n      // Create a proper Membre object that matches what the API expects\n      const membre = {\n        id: membreId\n      };\n      this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n        next: response => {\n          console.log('Member added successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n          // Afficher un message de succès\n          alert('Membre ajouté avec succès à l\\'équipe');\n        },\n        error: error => {\n          console.error('Error adding member:', error);\n          this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n    removeMembreFromEquipe(teamId, membreId) {\n      if (!teamId) {\n        console.error('Team ID is undefined');\n        alert('ID de l\\'équipe non défini');\n        return;\n      }\n      if (!membreId) {\n        console.error('Member ID is undefined');\n        alert('ID du membre non défini');\n        return;\n      }\n      if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n        this.loading = true;\n        this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n          next: response => {\n            console.log('Member removed successfully:', response);\n            this.loadEquipes();\n            this.loading = false;\n            // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n            if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n              const updatedEquipe = this.equipes.find(e => e._id === teamId);\n              if (updatedEquipe) {\n                this.selectedEquipe = updatedEquipe;\n              }\n            }\n          },\n          error: error => {\n            console.error('Error removing member:', error);\n            this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n            alert(this.error);\n            this.loading = false;\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function EquipeComponent_Factory(t) {\n        return new (t || EquipeComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EquipeComponent,\n        selectors: [[\"app-equipe\"]],\n        decls: 54,\n        vars: 14,\n        consts: [[1, \"container\", \"mt-4\"], [\"class\", \"alert alert-danger alert-dismissible fade show\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mb-4\", 4, \"ngIf\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"card\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"card-body\"], [1, \"mb-3\"], [\"for\", \"name\", 1, \"form-label\"], [1, \"text-danger\"], [\"type\", \"text\", \"id\", \"name\", \"required\", \"\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", 1, \"form-control\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [1, \"invalid-feedback\"], [\"for\", \"description\", 1, \"form-label\"], [\"id\", \"description\", \"rows\", \"3\", \"placeholder\", \"Entrez une description pour cette \\u00E9quipe\", 1, \"form-control\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [1, \"text-muted\"], [1, \"d-flex\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-secondary ms-2\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"membreModal\", \"tabindex\", \"-1\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\"], [4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"alert-dismissible\", \"fade\", \"show\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"d-flex\", \"justify-content-center\", \"mb-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-sm\", \"btn-info\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"ms-2\", 3, \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"input-group\", \"mb-2\"], [\"type\", \"text\", \"placeholder\", \"ID du membre\", 1, \"form-control\"], [\"membreIdInput\", \"\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"alert\", \"alert-info\", \"mt-3\"], [1, \"list-group\"], [\"class\", \"list-group-item d-flex justify-content-between align-items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", 3, \"click\"]],\n        template: function EquipeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r29 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, EquipeComponent_div_1_Template, 3, 1, \"div\", 1);\n            i0.ɵɵtemplate(2, EquipeComponent_div_2_Template, 4, 0, \"div\", 2);\n            i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h2\");\n            i0.ɵɵtext(7, \"Liste des \\u00E9quipes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function EquipeComponent_Template_button_click_8_listener() {\n              return ctx.loadEquipes();\n            });\n            i0.ɵɵelement(9, \"i\", 7);\n            i0.ɵɵtext(10, \" Rafra\\u00EEchir \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(11, EquipeComponent_div_11_Template, 2, 0, \"div\", 8);\n            i0.ɵɵtemplate(12, EquipeComponent_div_12_Template, 16, 1, \"div\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 3)(14, \"div\", 4)(15, \"div\", 10)(16, \"div\", 11)(17, \"h3\", 12);\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 13)(20, \"form\")(21, \"div\", 14)(22, \"label\", 15);\n            i0.ɵɵtext(23, \"Nom de l'\\u00E9quipe \");\n            i0.ɵɵelementStart(24, \"span\", 16);\n            i0.ɵɵtext(25, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"input\", 17, 18);\n            i0.ɵɵlistener(\"input\", function EquipeComponent_Template_input_input_26_listener() {\n              i0.ɵɵrestoreView(_r29);\n              const _r4 = i0.ɵɵreference(27);\n              return i0.ɵɵresetView(ctx.newEquipe.name = _r4.value);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"div\", 19);\n            i0.ɵɵtext(29, \"Le nom de l'\\u00E9quipe est requis\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"div\", 14)(31, \"label\", 20);\n            i0.ɵɵtext(32, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"textarea\", 21, 22);\n            i0.ɵɵlistener(\"input\", function EquipeComponent_Template_textarea_input_33_listener() {\n              i0.ɵɵrestoreView(_r29);\n              const _r5 = i0.ɵɵreference(34);\n              return i0.ɵɵresetView(ctx.newEquipe.description = _r5.value);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"small\", 23);\n            i0.ɵɵtext(36, \"Une br\\u00E8ve description de l'\\u00E9quipe et de son objectif\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"div\", 24)(38, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function EquipeComponent_Template_button_click_38_listener() {\n              return ctx.isEditing ? ctx.updateSelectedEquipe() : ctx.addEquipe();\n            });\n            i0.ɵɵtemplate(39, EquipeComponent_span_39_Template, 1, 0, \"span\", 26);\n            i0.ɵɵtext(40);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(41, EquipeComponent_button_41_Template, 2, 0, \"button\", 27);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(42, \"div\", 28)(43, \"div\", 29)(44, \"div\", 30)(45, \"div\", 31)(46, \"h5\", 32);\n            i0.ɵɵtext(47, \"G\\u00E9rer les membres de l'\\u00E9quipe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(48, \"button\", 33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"div\", 34);\n            i0.ɵɵtemplate(50, EquipeComponent_div_50_Template, 24, 4, \"div\", 35);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 36)(52, \"button\", 37);\n            i0.ɵɵtext(53, \" Fermer \");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.equipes.length === 0 && !ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.equipes.length > 0);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Modifier une \\u00E9quipe\" : \"Cr\\u00E9er une \\u00E9quipe\", \" \");\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassProp(\"is-invalid\", !ctx.newEquipe.name && (ctx.isEditing || ctx.newEquipe.name === \"\"));\n            i0.ɵɵproperty(\"value\", ctx.newEquipe.name);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"value\", ctx.newEquipe.description || \"\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", !ctx.newEquipe.name || ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditing ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er\", \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isEditing);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipe);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.NgForm],\n        styles: [\".container[_ngcontent-%COMP%]{max-width:1200px}.card[_ngcontent-%COMP%]{border:none;box-shadow:0 2px 4px #0000001a;border-radius:8px}.card-header[_ngcontent-%COMP%]{border-radius:8px 8px 0 0!important}.table[_ngcontent-%COMP%]{margin-bottom:0}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-top:none;font-weight:600;color:#495057}.btn-sm[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.875rem}.spinner-border-sm[_ngcontent-%COMP%]{width:1rem;height:1rem}.alert[_ngcontent-%COMP%]{border:none;border-radius:6px}.modal-content[_ngcontent-%COMP%]{border:none;border-radius:8px;box-shadow:0 10px 30px #0003}.list-group-item[_ngcontent-%COMP%]{border:1px solid #e9ecef;border-radius:4px;margin-bottom:.5rem}.list-group-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.form-control[_ngcontent-%COMP%]:focus{border-color:#80bdff;box-shadow:0 0 0 .2rem #007bff40}.btn[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 .2rem #007bff40}.text-muted[_ngcontent-%COMP%]{color:#6c757d!important}\"]\n      });\n    }\n  }\n  return EquipeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}