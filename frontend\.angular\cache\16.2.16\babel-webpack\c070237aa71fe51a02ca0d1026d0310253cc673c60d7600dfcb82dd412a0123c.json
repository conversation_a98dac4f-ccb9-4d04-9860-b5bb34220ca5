{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"@angular/common\";\nfunction EquipeListComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"div\", 24)(3, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 26);\n    i0.ɵɵtext(5, \" Chargement des \\u00E9quipes... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeListComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵelement(4, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 32)(6, \"h3\", 33);\n    i0.ɵɵtext(7, \" Erreur de chargement des \\u00E9quipes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 34);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_32_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.loadEquipes());\n    });\n    i0.ɵɵelement(11, \"i\", 36);\n    i0.ɵɵtext(12, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EquipeListComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 40);\n    i0.ɵɵtext(4, \" Aucune \\u00E9quipe trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 41);\n    i0.ɵɵtext(6, \" Commencez par cr\\u00E9er une nouvelle \\u00E9quipe pour organiser vos projets et membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_33_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToAddEquipe());\n    });\n    i0.ɵɵelement(8, \"i\", 43);\n    i0.ɵɵtext(9, \" Cr\\u00E9er une \\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeListComponent_div_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"div\", 9)(3, \"div\", 47);\n    i0.ɵɵelementStart(4, \"div\", 48)(5, \"div\", 49)(6, \"div\", 32)(7, \"h3\", 50);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 51)(10, \"span\", 52);\n    i0.ɵɵelement(11, \"i\", 53);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 54);\n    i0.ɵɵelement(14, \"i\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"p\", 56);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"slice\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 57)(19, \"div\", 58)(20, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_20_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r10.navigateToEquipeDetail(equipe_r9._id));\n    });\n    i0.ɵɵelement(21, \"i\", 60);\n    i0.ɵɵtext(22, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 61)(24, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_24_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r12.navigateToEditEquipe(equipe_r9._id));\n    });\n    i0.ɵɵelement(25, \"i\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_26_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r13.navigateToTasks(equipe_r9._id));\n    });\n    i0.ɵɵelement(27, \"i\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_28_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r14.deleteEquipe(equipe_r9._id));\n    });\n    i0.ɵɵelement(29, \"i\", 67);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const equipe_r9 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r9.name, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (equipe_r9.members == null ? null : equipe_r9.members.length) || 0, \" membre(s) \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r9.description && equipe_r9.description.length > 80 ? i0.ɵɵpipeBind3(17, 3, equipe_r9.description, 0, 80) + \"...\" : equipe_r9.description || \"Aucune description disponible\", \" \");\n  }\n}\nfunction EquipeListComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, EquipeListComponent_div_34_div_1_Template, 30, 7, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.equipes);\n  }\n}\nexport let EquipeListComponent = /*#__PURE__*/(() => {\n  class EquipeListComponent {\n    constructor(equipeService, router, notificationService) {\n      this.equipeService = equipeService;\n      this.router = router;\n      this.notificationService = notificationService;\n      this.equipes = [];\n      this.loading = false;\n      this.error = null;\n    }\n    ngOnInit() {\n      this.loadEquipes();\n    }\n    loadEquipes() {\n      this.loading = true;\n      this.error = null;\n      this.equipeService.getEquipes().pipe(finalize(() => this.loading = false)).subscribe({\n        next: data => {\n          console.log('Équipes chargées:', data);\n          this.equipes = data;\n          // Trier les équipes par nom\n          this.equipes.sort((a, b) => {\n            if (a.name && b.name) {\n              return a.name.localeCompare(b.name);\n            }\n            return 0;\n          });\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des équipes:', error);\n          this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n          this.notificationService.showError('Erreur lors du chargement des équipes');\n        }\n      });\n    }\n    navigateToAddEquipe() {\n      this.router.navigate(['/equipes/ajouter']);\n    }\n    navigateToEditEquipe(id) {\n      this.router.navigate(['/equipes/modifier', id]);\n    }\n    navigateToEquipeDetail(id) {\n      this.router.navigate(['/equipes/detail', id]);\n    }\n    deleteEquipe(id) {\n      if (!id) {\n        console.error('ID est indéfini');\n        this.notificationService.showError('ID d\\'équipe invalide');\n        return;\n      }\n      // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\n      const equipe = this.equipes.find(e => e._id === id);\n      const equipeName = equipe?.name || 'cette équipe';\n      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\n        this.loading = true;\n        this.equipeService.deleteEquipe(id).pipe(finalize(() => this.loading = false)).subscribe({\n          next: () => {\n            console.log('Équipe supprimée avec succès');\n            this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\n            this.loadEquipes();\n          },\n          error: error => {\n            console.error('Erreur lors de la suppression de l\\'équipe:', error);\n            this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\n            this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\n          }\n        });\n      }\n    }\n    navigateToTasks(id) {\n      if (!id) {\n        console.error('ID est indéfini');\n        this.notificationService.showError('ID d\\'équipe invalide');\n        return;\n      }\n      const equipe = this.equipes.find(e => e._id === id);\n      const equipeName = equipe?.name || 'cette équipe';\n      // Naviguer vers la page des tâches de l'équipe\n      this.router.navigate(['/tasks', id]);\n    }\n    static {\n      this.ɵfac = function EquipeListComponent_Factory(t) {\n        return new (t || EquipeListComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.NotificationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EquipeListComponent,\n        selectors: [[\"app-equipe-list\"]],\n        decls: 35,\n        vars: 4,\n        consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-3xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\"], [1, \"relative\", \"overflow-hidden\", \"group\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-2\", \"group-hover:rotate-90\", \"transition-transform\", \"duration-300\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"w-12\", \"h-12\", \"border-3\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#00f7ff]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-4\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"tracking-wide\"], [1, \"mb-6\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-xl\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"mt-3\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-3\", \"py-1.5\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"hover:bg-[#ff6b69]/30\", \"dark:hover:bg-[#ff3b30]/30\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-1.5\"], [1, \"text-center\", \"py-16\"], [1, \"w-20\", \"h-20\", \"mx-auto\", \"mb-6\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"opacity-70\"], [1, \"fas\", \"fa-users\", \"text-5xl\"], [1, \"text-xl\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus-circle\", \"mr-2\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"xl:grid-cols-3\", \"gap-6\"], [\"class\", \"group bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden hover:shadow-xl dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)] transition-all duration-300 hover:-translate-y-2 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm\", 4, \"ngFor\", \"ngForOf\"], [1, \"group\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"hover:shadow-xl\", \"dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)]\", \"transition-all\", \"duration-300\", \"hover:-translate-y-2\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"backdrop-blur-sm\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"p-6\"], [1, \"flex\", \"items-start\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"group-hover:scale-[1.02]\", \"transition-transform\", \"duration-300\", \"origin-left\"], [1, \"flex\", \"items-center\", \"text-xs\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-2\", \"py-1\", \"rounded-full\", \"font-medium\"], [1, \"fas\", \"fa-users\", \"mr-1\"], [1, \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [1, \"fas\", \"fa-users\", \"text-white\", \"text-lg\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"line-clamp-2\", \"mb-4\"], [1, \"px-6\", \"pb-6\"], [1, \"flex\", \"items-center\", \"justify-between\", \"pt-4\", \"border-t\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\"], [1, \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"hover:text-[#7826b5]\", \"dark:hover:text-[#4f5fad]\", \"text-sm\", \"font-medium\", \"transition-colors\", \"flex\", \"items-center\", \"group/details\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"mr-1\", \"group-hover/details:scale-110\", \"transition-transform\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"title\", \"Modifier l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#00f7ff]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [\"title\", \"G\\u00E9rer les t\\u00E2ches\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#00ff9d]\", \"hover:bg-[#00ff9d]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-tasks\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff3b30]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff3b30]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-trash\"]],\n        template: function EquipeListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n            i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n            i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h1\", 14);\n            i0.ɵɵtext(25, \" \\u00C9quipes \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"p\", 15);\n            i0.ɵɵtext(27, \" G\\u00E9rez vos \\u00E9quipes et leurs membres avec style futuriste \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function EquipeListComponent_Template_button_click_28_listener() {\n              return ctx.navigateToAddEquipe();\n            });\n            i0.ɵɵelement(29, \"i\", 17);\n            i0.ɵɵtext(30, \" Nouvelle \\u00C9quipe \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(31, EquipeListComponent_div_31_Template, 6, 0, \"div\", 18);\n            i0.ɵɵtemplate(32, EquipeListComponent_div_32_Template, 13, 1, \"div\", 19);\n            i0.ɵɵtemplate(33, EquipeListComponent_div_33_Template, 10, 0, \"div\", 20);\n            i0.ɵɵtemplate(34, EquipeListComponent_div_34_Template, 2, 1, \"div\", 21);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(31);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.equipes.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.equipes.length > 0);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i4.SlicePipe],\n        styles: [\".hover-shadow[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 20px #0000001a!important}.transition[_ngcontent-%COMP%]{transition:all .3s ease}.card-header.bg-primary[_ngcontent-%COMP%]{background:linear-gradient(45deg,#007bff,#6610f2)!important}\"]\n      });\n    }\n  }\n  return EquipeListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}