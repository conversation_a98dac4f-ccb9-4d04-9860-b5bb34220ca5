{"ast": null, "code": "import { CalendarView } from 'angular-calendar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/planning.service\";\nimport * as i3 from \"@app/services/authuser.service\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"angular-calendar\";\nimport * as i7 from \"../../../../pipes/highlight-presence.pipe\";\nfunction PlanningDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningDetailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction PlanningDetailComponent_div_7_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 16);\n    i0.ɵɵelement(2, \"path\", 31)(3, \"path\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 18);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.planning.lieu);\n  }\n}\nfunction PlanningDetailComponent_div_7_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"span\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const participant_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(participant_r6.username);\n  }\n}\nfunction PlanningDetailComponent_div_7_div_38_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 39)(1, \"div\");\n    i0.ɵɵelement(2, \"strong\", 40);\n    i0.ɵɵpipe(3, \"highlightPresence\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const event_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(3, 3, event_r8.title), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(6, 5, event_r8.start, \"shortTime\"), \" - \", i0.ɵɵpipeBind2(7, 8, event_r8.end, \"shortTime\"), \" \");\n  }\n}\nfunction PlanningDetailComponent_div_7_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"hr\", 35);\n    i0.ɵɵelementStart(2, \"h3\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ul\", 37);\n    i0.ɵɵtemplate(6, PlanningDetailComponent_div_7_div_38_li_6_Template, 8, 11, \"li\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" D\\u00E9tails pour le \", i0.ɵɵpipeBind2(4, 2, ctx_r5.selectedDate, \"fullDate\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.selectedDayEvents);\n  }\n}\nfunction PlanningDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"h1\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"p\", 12);\n    i0.ɵɵpipe(4, \"highlightPresence\");\n    i0.ɵɵelementStart(5, \"div\", 13)(6, \"h2\", 14);\n    i0.ɵɵtext(7, \"Informations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 15);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 16);\n    i0.ɵɵelement(10, \"path\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"span\", 18);\n    i0.ɵɵtext(12, \" Du \");\n    i0.ɵɵelementStart(13, \"strong\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" au \");\n    i0.ɵɵelementStart(17, \"strong\");\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(20, PlanningDetailComponent_div_7_div_20_Template, 6, 1, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"h2\", 14);\n    i0.ɵɵtext(23, \"Participants\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 20);\n    i0.ɵɵtemplate(25, PlanningDetailComponent_div_7_div_25_Template, 3, 1, \"div\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\")(27, \"div\", 22)(28, \"h2\", 23);\n    i0.ɵɵtext(29, \"R\\u00E9unions associ\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.nouvelleReunion());\n    });\n    i0.ɵɵtext(31, \" Nouvelle R\\u00E9union \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 25)(33, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.editPlanning());\n    });\n    i0.ɵɵtext(34, \" Modifier Planning \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.deletePlanning());\n    });\n    i0.ɵɵtext(36, \" Supprimer Planning \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"mwl-calendar-month-view\", 28);\n    i0.ɵɵlistener(\"dayClicked\", function PlanningDetailComponent_div_7_Template_mwl_calendar_month_view_dayClicked_37_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.handleDayClick($event.day));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, PlanningDetailComponent_div_7_div_38_Template, 7, 5, \"div\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.planning.titre);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 9, ctx_r2.planning.description), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 11, ctx_r2.planning.dateDebut, \"mediumDate\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 14, ctx_r2.planning.dateFin, \"mediumDate\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.planning.lieu);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.planning.participants);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"viewDate\", ctx_r2.viewDate)(\"events\", ctx_r2.events);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDayEvents.length > 0);\n  }\n}\nexport let PlanningDetailComponent = /*#__PURE__*/(() => {\n  class PlanningDetailComponent {\n    constructor(route, router, planningService, authService, cdr, sanitizer) {\n      this.route = route;\n      this.router = router;\n      this.planningService = planningService;\n      this.authService = authService;\n      this.cdr = cdr;\n      this.sanitizer = sanitizer;\n      this.planning = null;\n      this.loading = true;\n      this.error = null;\n      this.isCreator = false;\n      this.selectedDayEvents = [];\n      this.selectedDate = null;\n      // Calendar setup\n      this.view = CalendarView.Month;\n      this.viewDate = new Date();\n      this.events = [];\n    }\n    ngOnInit() {\n      this.loadPlanningDetails();\n    }\n    loadPlanningDetails() {\n      const id = this.route.snapshot.paramMap.get('id');\n      if (!id) {\n        this.error = 'ID de planning non fourni';\n        this.loading = false;\n        return;\n      }\n      this.planningService.getPlanningById(id).subscribe({\n        next: planning => {\n          this.planning = planning.planning;\n          this.isCreator = planning.planning.createur._id === this.authService.getCurrentUserId();\n          this.loading = false;\n          this.events = this.planning.reunions.map(reunion => {\n            const startStr = `${reunion.date.substring(0, 10)}T${reunion.heureDebut}:00`;\n            const endStr = `${reunion.date.substring(0, 10)}T${reunion.heureFin}:00`;\n            return {\n              start: new Date(startStr),\n              end: new Date(endStr),\n              title: reunion.titre,\n              allDay: false\n            };\n          });\n          this.cdr.detectChanges();\n        },\n        error: err => {\n          this.error = err.error?.message || 'Erreur lors du chargement';\n          this.loading = false;\n          console.error('Erreur:', err);\n        }\n      });\n    }\n    handleDayClick(day) {\n      this.selectedDate = day.date;\n      this.selectedDayEvents = day.events; // These come from your `events` array\n    }\n\n    editPlanning() {\n      if (this.planning) {\n        this.router.navigate(['/plannings/edit', this.planning._id]);\n      }\n    }\n    deletePlanning() {\n      if (this.planning && confirm('Supprimer définitivement ce planning ?')) {\n        this.planningService.deletePlanning(this.planning._id).subscribe({\n          next: () => this.router.navigate(['/plannings']),\n          error: err => this.error = err.error?.message || 'Erreur lors de la suppression'\n        });\n      }\n    }\n    nouvelleReunion() {\n      if (this.planning) {\n        // Rediriger vers le formulaire de création de réunion avec l'ID du planning préselectionné\n        this.router.navigate(['/reunions/nouvelleReunion'], {\n          queryParams: {\n            planningId: this.planning._id\n          }\n        });\n      }\n    }\n    formatDescription(description) {\n      // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n      const formattedText = description.replace(/\\(presence obligatoire\\)/gi, '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>');\n      // Sanitize le HTML pour éviter les problèmes de sécurité\n      return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n    }\n    static {\n      this.ɵfac = function PlanningDetailComponent_Factory(t) {\n        return new (t || PlanningDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.PlanningService), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.DomSanitizer));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PlanningDetailComponent,\n        selectors: [[\"app-planning-detail\"]],\n        decls: 8,\n        vars: 3,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"mb-4\", \"flex\", \"items-center\", \"text-purple-600\", \"hover:text-purple-800\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-lg shadow-md p-6\", 4, \"ngIf\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-b-2\", \"border-purple-600\", \"mx-auto\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"mb-2\"], [1, \"text-gray-600\", \"mb-4\", 3, \"innerHTML\"], [1, \"mb-6\"], [1, \"text-lg\", \"font-semibold\", \"mb-3\", \"text-gray-800\"], [1, \"flex\", \"items-center\", \"mb-2\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-gray-500\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-gray-700\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"flex items-center bg-gray-100 rounded-full px-3 py-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-3\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"px-4\", \"py-2\", \"bg-purple-600\", \"text-white\", \"rounded\", \"hover:bg-purple-700\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"justify-end\", \"space-x-2\", \"mb-3\"], [1, \"px-4\", \"py-2\", \"bg-blue-500\", \"text-white\", \"rounded\", \"hover:bg-blue-600\", \"transition-colors\", 3, \"click\"], [1, \"px-4\", \"py-2\", \"bg-red-500\", \"text-white\", \"rounded\", \"hover:bg-red-600\", \"transition-colors\", 3, \"click\"], [3, \"viewDate\", \"events\", \"dayClicked\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"flex\", \"items-center\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"items-center\", \"bg-gray-100\", \"rounded-full\", \"px-3\", \"py-1\"], [1, \"mt-4\"], [1, \"my-2\", \"border-gray-300\"], [1, \"text-md\", \"font-medium\", \"text-gray-700\", \"mb-2\"], [1, \"space-y-2\"], [\"class\", \"p-2 border rounded bg-gray-50\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-2\", \"border\", \"rounded\", \"bg-gray-50\"], [3, \"innerHTML\"]],\n        template: function PlanningDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n            i0.ɵɵlistener(\"click\", function PlanningDetailComponent_Template_button_click_1_listener() {\n              return ctx.router.navigate([\"/plannings\"]);\n            });\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(2, \"svg\", 2);\n            i0.ɵɵelement(3, \"path\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(4, \" Retour aux plannings \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, PlanningDetailComponent_div_5_Template, 2, 0, \"div\", 4);\n            i0.ɵɵtemplate(6, PlanningDetailComponent_div_6_Template, 2, 1, \"div\", 5);\n            i0.ɵɵtemplate(7, PlanningDetailComponent_div_7_Template, 39, 17, \"div\", 6);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.planning);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i6.CalendarMonthViewComponent, i5.DatePipe, i7.HighlightPresencePipe]\n      });\n    }\n  }\n  return PlanningDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}