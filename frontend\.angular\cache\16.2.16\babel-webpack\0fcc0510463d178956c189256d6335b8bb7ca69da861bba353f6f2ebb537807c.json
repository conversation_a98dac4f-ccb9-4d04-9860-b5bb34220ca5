{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/reunion.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../../pipes/highlight-presence.pipe\";\nfunction ReunionListComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionListComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Erreur lors du chargement des r\\u00E9unions: \", ctx_r1.error.message, \" \");\n  }\n}\nfunction ReunionListComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 9);\n    i0.ɵɵelement(2, \"path\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"h3\", 11);\n    i0.ɵɵtext(4, \"Aucune r\\u00E9union pr\\u00E9vue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 12);\n    i0.ɵɵtext(6, \"Commencez par planifier une nouvelle r\\u00E9union.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReunionListComponent_div_7_div_1_div_20_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const participant_r9 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", participant_r9.username, \" (\", participant_r9.email, \")\");\n  }\n}\nfunction ReunionListComponent_div_7_div_1_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"strong\");\n    i0.ɵɵtext(2, \"Participants:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 30);\n    i0.ɵɵtemplate(4, ReunionListComponent_div_7_div_1_div_20_li_4_Template, 2, 2, \"li\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reunion_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", reunion_r5.participants);\n  }\n}\nfunction ReunionListComponent_div_7_div_1_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"strong\");\n    i0.ɵɵtext(2, \"Lien Visio:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 32);\n    i0.ɵɵtext(4, \"Rejoindre la r\\u00E9union\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reunion_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"href\", reunion_r5.lienVisio, i0.ɵɵsanitizeUrl);\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/reunions\", a1];\n};\nfunction ReunionListComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\")(3, \"h3\", 17)(4, \"a\", 18);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"p\", 19);\n    i0.ɵɵpipe(7, \"highlightPresence\");\n    i0.ɵɵelementStart(8, \"div\", 20);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 21);\n    i0.ɵɵelement(10, \"path\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 23)(17, \"strong\");\n    i0.ɵɵtext(18, \"Createur:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, ReunionListComponent_div_7_div_1_div_20_Template, 5, 1, \"div\", 24);\n    i0.ɵɵelementStart(21, \"div\", 23)(22, \"strong\");\n    i0.ɵɵtext(23, \"Planning:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24);\n    i0.ɵɵelement(25, \"br\");\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"date\");\n    i0.ɵɵelement(28, \"br\");\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, ReunionListComponent_div_7_div_1_div_31_Template, 5, 1, \"div\", 24);\n    i0.ɵɵelementStart(32, \"div\", 25)(33, \"div\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(34, \"svg\", 21);\n    i0.ɵɵelement(35, \"path\", 27)(36, \"path\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(38, \"a\", 29);\n    i0.ɵɵlistener(\"click\", function ReunionListComponent_div_7_div_1_Template_a_click_38_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const reunion_r5 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.editReunion(reunion_r5._id));\n    });\n    i0.ɵɵtext(39, \" Modifier \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const reunion_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(30, _c0, reunion_r5.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(reunion_r5.titre);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(7, 17, reunion_r5.description), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind2(12, 19, reunion_r5.date, \"medium\"), \" - \", reunion_r5.heureDebut, \" - \", reunion_r5.heureFin, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"px-2 py-1 text-xs rounded-full \" + ctx_r4.getStatutClass(reunion_r5.statut));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 22, reunion_r5.statut), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", reunion_r5.createur.username, \" (\", reunion_r5.createur.email, \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", reunion_r5.participants.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", reunion_r5.planning.titre, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Date de d\\u00E9but: \", i0.ɵɵpipeBind2(27, 24, reunion_r5.planning.dateDebut, \"mediumDate\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Date de fin: \", i0.ɵɵpipeBind2(30, 27, reunion_r5.planning.dateFin, \"mediumDate\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", reunion_r5.lienVisio);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", reunion_r5.lieu || \"Lieu non sp\\u00E9cifi\\u00E9\", \" \");\n  }\n}\nfunction ReunionListComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, ReunionListComponent_div_7_div_1_Template, 40, 32, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.reunions);\n  }\n}\nexport let ReunionListComponent = /*#__PURE__*/(() => {\n  class ReunionListComponent {\n    constructor(reunionService, router, authService, sanitizer) {\n      this.reunionService = reunionService;\n      this.router = router;\n      this.authService = authService;\n      this.sanitizer = sanitizer;\n      this.reunions = [];\n      this.loading = true;\n    }\n    ngOnInit() {\n      this.loadReunions();\n    }\n    loadReunions() {\n      const userId = this.authService.getCurrentUserId();\n      if (!userId) return;\n      this.reunionService.getProchainesReunions(userId).subscribe({\n        next: reunions => {\n          console.log(reunions);\n          this.reunions = reunions.reunions;\n          this.loading = false;\n        },\n        error: error => {\n          // Afficher plus de détails sur l'erreur pour le débogage\n          console.error('Erreur détaillée:', JSON.stringify(error));\n          this.error = `Erreur lors du chargement des réunions: ${error.message || error.statusText || 'Erreur inconnue'}`;\n          this.loading = false;\n        }\n      });\n    }\n    getStatutClass(statut) {\n      switch (statut) {\n        case 'planifiee':\n          return 'bg-blue-100 text-blue-800';\n        case 'en_cours':\n          return 'bg-yellow-100 text-yellow-800';\n        case 'terminee':\n          return 'bg-green-100 text-green-800';\n        case 'annulee':\n          return 'bg-red-100 text-red-800';\n        default:\n          return 'bg-gray-100 text-gray-800';\n      }\n    }\n    editReunion(id) {\n      console.log(id);\n      if (this.reunions) {\n        this.router.navigate(['/reunions/modifier', id]);\n      }\n    }\n    formatDescription(description) {\n      if (!description) return this.sanitizer.bypassSecurityTrustHtml('');\n      // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n      const formattedText = description.replace(/\\(presence obligatoire\\)/gi, '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>');\n      // Sanitize le HTML pour éviter les problèmes de sécurité\n      return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n    }\n    static {\n      this.ɵfac = function ReunionListComponent_Factory(t) {\n        return new (t || ReunionListComponent)(i0.ɵɵdirectiveInject(i1.ReunionService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.DomSanitizer));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReunionListComponent,\n        selectors: [[\"app-reunion-list\"]],\n        decls: 8,\n        vars: 4,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-6\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"space-y-4\", 4, \"ngIf\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-b-2\", \"border-purple-600\", \"mx-auto\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"mx-auto\", \"h-12\", \"w-12\", \"text-gray-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"mt-2\", \"text-lg\", \"font-medium\", \"text-gray-900\"], [1, \"mt-1\", \"text-gray-500\"], [1, \"space-y-4\"], [\"class\", \"bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-4\", \"hover:shadow-lg\", \"transition-shadow\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"hover:text-purple-600\", 3, \"routerLink\"], [1, \"text-sm\", \"text-gray-600\", 3, \"innerHTML\"], [1, \"mt-2\", \"flex\", \"items-center\", \"text-sm\", \"text-gray-500\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"mt-2\", \"text-sm\", \"text-gray-600\"], [\"class\", \"mt-2 text-sm text-gray-600\", 4, \"ngIf\"], [1, \"mt-3\", \"pt-3\", \"border-t\", \"border-gray-100\", \"flex\", \"justify-between\", \"items-center\"], [1, \"flex\", \"items-center\", \"text-sm\", \"text-gray-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"px-4\", \"py-2\", \"bg-blue-700\", \"text-white\", \"rounded-md\", \"transition-colors\", 3, \"click\"], [1, \"list-disc\", \"pl-5\"], [4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"text-blue-600\", \"hover:underline\", 3, \"href\"]],\n        template: function ReunionListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵtext(3, \"Mes R\\u00E9unions\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(4, ReunionListComponent_div_4_Template, 2, 0, \"div\", 3);\n            i0.ɵɵtemplate(5, ReunionListComponent_div_5_Template, 2, 1, \"div\", 4);\n            i0.ɵɵtemplate(6, ReunionListComponent_div_6_Template, 7, 0, \"div\", 3);\n            i0.ɵɵtemplate(7, ReunionListComponent_div_7_Template, 2, 1, \"div\", 5);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunions.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunions.length > 0);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i2.RouterLink, i5.TitleCasePipe, i5.DatePipe, i6.HighlightPresencePipe]\n      });\n    }\n  }\n  return ReunionListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}