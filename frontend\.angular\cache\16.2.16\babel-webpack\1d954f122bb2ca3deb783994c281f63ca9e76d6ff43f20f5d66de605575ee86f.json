{"ast": null, "code": "import { DatePipe } from '@angular/common';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/rendus.service\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction ListRendusComponent_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", groupe_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(groupe_r6);\n  }\n}\nfunction ListRendusComponent_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const projet_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", projet_r7._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(projet_r7.titre);\n  }\n}\nfunction ListRendusComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ListRendusComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/evaluation-details\", a1];\n};\nfunction ListRendusComponent_div_22_tr_18_a_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 37);\n    i0.ɵɵtext(1, \" Voir l'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rendu_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, rendu_r9._id));\n  }\n}\nfunction ListRendusComponent_div_22_tr_18_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function ListRendusComponent_div_22_tr_18_div_25_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const rendu_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.evaluerRendu(rendu_r9._id, \"manual\"));\n    });\n    i0.ɵɵtext(2, \" \\u00C9valuer manuellement \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ListRendusComponent_div_22_tr_18_div_25_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const rendu_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.evaluerRendu(rendu_r9._id, \"ai\"));\n    });\n    i0.ɵɵtext(4, \" \\u00C9valuer par IA \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ListRendusComponent_div_22_tr_18_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ListRendusComponent_div_22_tr_18_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const rendu_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.navigateToEditEvaluation(rendu_r9._id));\n    });\n    i0.ɵɵtext(1, \" Modifier l'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ListRendusComponent_div_22_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 24)(2, \"div\", 25)(3, \"div\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 27)(6, \"div\", 28);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 29);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"td\", 24)(11, \"div\", 30);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 24)(14, \"div\", 30);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 24)(17, \"div\", 30);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"td\", 24)(20, \"span\", 31);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"td\", 32)(23, \"div\", 33);\n    i0.ɵɵtemplate(24, ListRendusComponent_div_22_tr_18_a_24_Template, 2, 3, \"a\", 34);\n    i0.ɵɵtemplate(25, ListRendusComponent_div_22_tr_18_div_25_Template, 5, 0, \"div\", 35);\n    i0.ɵɵtemplate(26, ListRendusComponent_div_22_tr_18_button_26_Template, 2, 0, \"button\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const rendu_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", rendu_r9.etudiant == null ? null : rendu_r9.etudiant.nom == null ? null : rendu_r9.etudiant.nom.charAt(0), \"\", rendu_r9.etudiant == null ? null : rendu_r9.etudiant.prenom == null ? null : rendu_r9.etudiant.prenom.charAt(0), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", rendu_r9.etudiant == null ? null : rendu_r9.etudiant.nom, \" \", rendu_r9.etudiant == null ? null : rendu_r9.etudiant.prenom, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", rendu_r9.etudiant == null ? null : rendu_r9.etudiant.email, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((rendu_r9.etudiant == null ? null : rendu_r9.etudiant.group) || \"Non sp\\u00E9cifi\\u00E9\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(rendu_r9.projet == null ? null : rendu_r9.projet.titre);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r8.formatDate(rendu_r9.dateSoumission));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.getClasseStatut(rendu_r9));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getStatutEvaluation(rendu_r9), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", rendu_r9.evaluation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !rendu_r9.evaluation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", rendu_r9.evaluation);\n  }\n}\nfunction ListRendusComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"table\", 19)(3, \"thead\", 20)(4, \"tr\")(5, \"th\", 21);\n    i0.ɵɵtext(6, \" \\u00C9tudiant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 21);\n    i0.ɵɵtext(8, \" Groupe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 21);\n    i0.ɵɵtext(10, \" Projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 21);\n    i0.ɵɵtext(12, \" Date de soumission \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 21);\n    i0.ɵɵtext(14, \" Statut \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 21);\n    i0.ɵɵtext(16, \" Actions \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"tbody\", 22);\n    i0.ɵɵtemplate(18, ListRendusComponent_div_22_tr_18_Template, 27, 13, \"tr\", 23);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.filteredRendus);\n  }\n}\nfunction ListRendusComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 42);\n    i0.ɵɵelement(2, \"path\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"h3\", 44);\n    i0.ɵɵtext(4, \"Aucun rendu disponible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 45);\n    i0.ɵɵtext(6, \"Aucun rendu ne correspond \\u00E0 vos crit\\u00E8res de filtrage\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ListRendusComponent = /*#__PURE__*/(() => {\n  class ListRendusComponent {\n    constructor(rendusService, projetService, router, datePipe) {\n      this.rendusService = rendusService;\n      this.projetService = projetService;\n      this.router = router;\n      this.datePipe = datePipe;\n      this.rendus = [];\n      this.filteredRendus = [];\n      this.isLoading = true;\n      this.error = '';\n      this.searchTerm = '';\n      this.filterStatus = 'all';\n      // Nouvelles propriétés pour les filtres\n      this.filtreGroupe = '';\n      this.filtreProjet = '';\n      this.groupes = [];\n      this.projets = [];\n    }\n    ngOnInit() {\n      this.loadRendus();\n      this.loadProjets();\n      this.extractGroupes();\n    }\n    loadRendus() {\n      this.isLoading = true;\n      this.rendusService.getAllRendus().subscribe({\n        next: data => {\n          this.rendus = data;\n          this.extractGroupes();\n          this.applyFilters();\n          this.isLoading = false;\n        },\n        error: err => {\n          console.error('Erreur lors du chargement des rendus', err);\n          this.error = 'Impossible de charger les rendus. Veuillez réessayer plus tard.';\n          this.isLoading = false;\n        }\n      });\n    }\n    loadProjets() {\n      this.projetService.getProjets().subscribe({\n        next: data => {\n          this.projets = data;\n        },\n        error: err => {\n          console.error('Erreur lors du chargement des projets', err);\n        }\n      });\n    }\n    extractGroupes() {\n      // Extraire les groupes uniques des rendus\n      if (this.rendus && this.rendus.length > 0) {\n        const groupesSet = new Set();\n        this.rendus.forEach(rendu => {\n          if (rendu.etudiant?.groupe) {\n            groupesSet.add(rendu.etudiant.groupe);\n          }\n        });\n        this.groupes = Array.from(groupesSet);\n      }\n    }\n    applyFilters() {\n      let results = this.rendus;\n      // Filtre par statut d'évaluation\n      if (this.filterStatus === 'evaluated') {\n        results = results.filter(rendu => rendu.evaluation && rendu.evaluation.scores);\n      } else if (this.filterStatus === 'pending') {\n        results = results.filter(rendu => !rendu.evaluation || !rendu.evaluation.scores);\n      }\n      // Filtre par terme de recherche\n      if (this.searchTerm.trim() !== '') {\n        const term = this.searchTerm.toLowerCase().trim();\n        results = results.filter(rendu => rendu.etudiant?.nom?.toLowerCase().includes(term) || rendu.etudiant?.prenom?.toLowerCase().includes(term) || rendu.projet?.titre?.toLowerCase().includes(term));\n      }\n      // Filtre par groupe\n      if (this.filtreGroupe) {\n        results = results.filter(rendu => rendu.etudiant?.groupe === this.filtreGroupe);\n      }\n      // Filtre par projet\n      if (this.filtreProjet) {\n        results = results.filter(rendu => rendu.projet?._id === this.filtreProjet);\n      }\n      this.filteredRendus = results;\n    }\n    // Méthode pour la compatibilité avec le template\n    filtrerRendus() {\n      return this.filteredRendus;\n    }\n    onSearchChange() {\n      this.applyFilters();\n    }\n    setFilterStatus(status) {\n      this.filterStatus = status;\n      this.applyFilters();\n    }\n    evaluateRendu(renduId) {\n      this.router.navigate(['/admin/projects/evaluate', renduId]);\n    }\n    // Méthode pour la compatibilité avec le template\n    evaluerRendu(renduId, mode) {\n      // Rediriger vers la page d'évaluation avec le mode approprié\n      this.router.navigate(['/admin/projects/evaluate', renduId], {\n        queryParams: {\n          mode: mode\n        }\n      });\n    }\n    viewEvaluationDetails(renduId) {\n      this.router.navigate(['/admin/projects/evaluation-details', renduId]);\n    }\n    getStatusClass(rendu) {\n      if (rendu.evaluation && rendu.evaluation.scores) {\n        return 'bg-green-100 text-green-800';\n      }\n      return 'bg-yellow-100 text-yellow-800';\n    }\n    // Méthode pour la compatibilité avec le template\n    getClasseStatut(rendu) {\n      return this.getStatusClass(rendu);\n    }\n    getStatusText(rendu) {\n      // Vérifier si l'évaluation existe de plusieurs façons\n      if (rendu.evaluation && rendu.evaluation._id) {\n        return 'Évalué';\n      }\n      if (rendu.statut === 'évalué') {\n        return 'Évalué';\n      }\n      return 'En attente';\n    }\n    // Méthode pour la compatibilité avec le template\n    getStatutEvaluation(rendu) {\n      return this.getStatusText(rendu);\n    }\n    getScoreTotal(rendu) {\n      if (!rendu.evaluation || !rendu.evaluation.scores) return 0;\n      const scores = rendu.evaluation.scores;\n      return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n    }\n    getScoreClass(score) {\n      if (score >= 16) return 'text-green-600';\n      if (score >= 12) return 'text-blue-600';\n      if (score >= 8) return 'text-yellow-600';\n      return 'text-red-600';\n    }\n    formatDate(date) {\n      if (!date) return '';\n      return this.datePipe.transform(date, 'dd/MM/yyyy') || '';\n    }\n    navigateToEditEvaluation(renduId) {\n      this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\n    }\n    // Méthodes pour gérer les fichiers\n    getFileUrl(filePath) {\n      if (!filePath) return '';\n      // Extraire uniquement le nom du fichier\n      let fileName = filePath;\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        fileName = parts[parts.length - 1];\n      }\n      // Utiliser la route spécifique pour le téléchargement\n      return `${environment.urlBackend}projets/telecharger/${fileName}`;\n    }\n    getFileName(filePath) {\n      if (!filePath) return 'Fichier';\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        return parts[parts.length - 1];\n      }\n      return filePath;\n    }\n    static {\n      this.ɵfac = function ListRendusComponent_Factory(t) {\n        return new (t || ListRendusComponent)(i0.ɵɵdirectiveInject(i1.RendusService), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.DatePipe));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ListRendusComponent,\n        selectors: [[\"app-list-rendus\"]],\n        features: [i0.ɵɵProvidersFeature([DatePipe])],\n        decls: 24,\n        vars: 8,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"p-4\", \"md:p-6\"], [1, \"max-w-6xl\", \"mx-auto\", \"flex\", \"justify-between\", \"items-center\", \"mb-6\"], [1, \"text-2xl\", \"md:text-3xl\", \"font-bold\", \"text-[#4f5fad]\"], [1, \"max-w-6xl\", \"mx-auto\", \"bg-white\", \"rounded-xl\", \"shadow-md\", \"p-4\", \"mb-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [1, \"w-full\", \"p-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"focus:ring-[#4f5fad]\", \"focus:border-[#4f5fad]\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"max-w-6xl mx-auto flex justify-center py-12\", 4, \"ngIf\"], [\"class\", \"max-w-6xl mx-auto bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"max-w-6xl mx-auto bg-white rounded-xl shadow-md overflow-hidden\", 4, \"ngIf\"], [\"class\", \"max-w-6xl mx-auto text-center py-12\", 4, \"ngIf\"], [3, \"value\"], [1, \"max-w-6xl\", \"mx-auto\", \"flex\", \"justify-center\", \"py-12\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\"], [1, \"max-w-6xl\", \"mx-auto\", \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"max-w-6xl\", \"mx-auto\", \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\"], [1, \"overflow-x-auto\"], [1, \"min-w-full\", \"divide-y\", \"divide-gray-200\"], [1, \"bg-gray-50\"], [\"scope\", \"col\", 1, \"px-6\", \"py-3\", \"text-left\", \"text-xs\", \"font-medium\", \"text-gray-500\", \"uppercase\", \"tracking-wider\"], [1, \"bg-white\", \"divide-y\", \"divide-gray-200\"], [4, \"ngFor\", \"ngForOf\"], [1, \"px-6\", \"py-4\", \"whitespace-nowrap\"], [1, \"flex\", \"items-center\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#6C63FF]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-xs\", \"font-bold\"], [1, \"ml-4\"], [1, \"text-sm\", \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"text-sm\", \"text-gray-900\"], [1, \"px-2\", \"inline-flex\", \"text-xs\", \"leading-5\", \"font-semibold\", \"rounded-full\", 3, \"ngClass\"], [1, \"px-6\", \"py-4\", \"whitespace-nowrap\", \"text-sm\", \"font-medium\"], [1, \"flex\", \"space-x-2\"], [\"class\", \"text-indigo-600 hover:text-indigo-900\", 3, \"routerLink\", 4, \"ngIf\"], [\"class\", \"flex space-x-2\", 4, \"ngIf\"], [\"class\", \"text-blue-600 hover:text-blue-800 mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"text-indigo-600\", \"hover:text-indigo-900\", 3, \"routerLink\"], [1, \"text-green-600\", \"hover:text-green-900\", \"bg-green-100\", \"px-2\", \"py-1\", \"rounded\", 3, \"click\"], [1, \"text-blue-600\", \"hover:text-blue-900\", \"bg-blue-100\", \"px-2\", \"py-1\", \"rounded\", 3, \"click\"], [1, \"text-blue-600\", \"hover:text-blue-800\", \"mr-2\", 3, \"click\"], [1, \"max-w-6xl\", \"mx-auto\", \"text-center\", \"py-12\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-16\", \"w-16\", \"mx-auto\", \"text-[#bdc6cc]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1\", \"d\", \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"mt-4\", \"text-lg\", \"font-medium\", \"text-[#6d6870]\"], [1, \"mt-1\", \"text-sm\", \"text-[#6d6870]\"]],\n        template: function ListRendusComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵtext(3, \"Liste des Rendus\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"div\")(7, \"label\", 5);\n            i0.ɵɵtext(8, \"Filtrer par groupe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"select\", 6);\n            i0.ɵɵlistener(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_9_listener($event) {\n              return ctx.filtreGroupe = $event;\n            })(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_9_listener() {\n              return ctx.applyFilters();\n            });\n            i0.ɵɵelementStart(10, \"option\", 7);\n            i0.ɵɵtext(11, \"Tous les groupes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(12, ListRendusComponent_option_12_Template, 2, 2, \"option\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\")(14, \"label\", 5);\n            i0.ɵɵtext(15, \"Filtrer par projet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"select\", 6);\n            i0.ɵɵlistener(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_16_listener($event) {\n              return ctx.filtreProjet = $event;\n            })(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_16_listener() {\n              return ctx.applyFilters();\n            });\n            i0.ɵɵelementStart(17, \"option\", 7);\n            i0.ɵɵtext(18, \"Tous les projets\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(19, ListRendusComponent_option_19_Template, 2, 2, \"option\", 8);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(20, ListRendusComponent_div_20_Template, 2, 0, \"div\", 9);\n            i0.ɵɵtemplate(21, ListRendusComponent_div_21_Template, 2, 1, \"div\", 10);\n            i0.ɵɵtemplate(22, ListRendusComponent_div_22_Template, 19, 1, \"div\", 11);\n            i0.ɵɵtemplate(23, ListRendusComponent_div_23_Template, 7, 0, \"div\", 12);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngModel\", ctx.filtreGroupe);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.groupes);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngModel\", ctx.filtreProjet);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.projets);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredRendus.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredRendus.length === 0);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i3.RouterLink, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n        styles: [\".loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}\"]\n      });\n    }\n  }\n  return ListRendusComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}