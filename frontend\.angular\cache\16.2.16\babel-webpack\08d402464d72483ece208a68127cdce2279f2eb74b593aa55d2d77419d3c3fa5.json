{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"src/app/services/equipe.service\";\nexport let EquipeLayoutComponent = /*#__PURE__*/(() => {\n  class EquipeLayoutComponent {\n    constructor(router, location, equipeService) {\n      this.router = router;\n      this.location = location;\n      this.equipeService = equipeService;\n      this.sidebarVisible$ = new Observable();\n      // Page properties\n      this.pageTitle = 'Gestion des Équipes';\n      this.pageSubtitle = 'Organisez et gérez vos équipes de projet';\n      // Statistics\n      this.totalEquipes = 0;\n      this.totalMembres = 0;\n      this.totalProjets = 0;\n    }\n    ngOnInit() {\n      this.loadStatistics();\n      this.updatePageTitle();\n      // Listen to route changes to update page title\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n        this.updatePageTitle();\n      });\n    }\n    loadStatistics() {\n      // Load teams statistics\n      this.equipeService.getEquipes().subscribe({\n        next: equipes => {\n          this.totalEquipes = equipes.length;\n          // Calculate total members across all teams\n          this.totalMembres = equipes.reduce((total, equipe) => {\n            return total + (equipe.members ? equipe.members.length : 0);\n          }, 0);\n          // For now, set projects to 0 (can be updated when project service is available)\n          this.totalProjets = 0;\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des statistiques:', error);\n        }\n      });\n    }\n    updatePageTitle() {\n      const url = this.router.url;\n      if (url.includes('/equipes/liste')) {\n        this.pageTitle = 'Liste des Équipes';\n        this.pageSubtitle = 'Consultez et gérez toutes vos équipes';\n      } else if (url.includes('/equipes/nouveau')) {\n        this.pageTitle = 'Créer une Équipe';\n        this.pageSubtitle = 'Créez une nouvelle équipe pour vos projets';\n      } else if (url.includes('/equipes/mes-equipes')) {\n        this.pageTitle = 'Mes Équipes';\n        this.pageSubtitle = 'Équipes dont vous êtes membre ou administrateur';\n      } else if (url.includes('/equipes/detail')) {\n        this.pageTitle = \"Détails de l'Équipe\";\n        this.pageSubtitle = 'Informations et gestion des membres';\n      } else if (url.includes('/equipes/edit')) {\n        this.pageTitle = \"Modifier l'Équipe\";\n        this.pageSubtitle = 'Modifiez les informations de votre équipe';\n      } else {\n        this.pageTitle = 'Gestion des Équipes';\n        this.pageSubtitle = 'Organisez et gérez vos équipes de projet';\n      }\n    }\n    goBack() {\n      this.location.back();\n    }\n    static {\n      this.ɵfac = function EquipeLayoutComponent_Factory(t) {\n        return new (t || EquipeLayoutComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.Location), i0.ɵɵdirectiveInject(i3.EquipeService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EquipeLayoutComponent,\n        selectors: [[\"app-equipe-layout\"]],\n        decls: 92,\n        vars: 0,\n        consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"flex\", \"h-screen\", \"relative\", \"z-10\"], [1, \"w-80\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"shadow-xl\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"border-r\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"flex\", \"flex-col\"], [1, \"p-6\", \"border-b\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-10\", \"h-10\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"rounded-xl\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-users\", \"text-white\", \"text-lg\"], [1, \"text-xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"tracking-wide\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"flex-1\", \"p-4\", \"space-y-2\"], [\"routerLink\", \"/equipes/liste\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-xl\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#00f7ff]/10\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"transition-all\", \"duration-300\"], [1, \"relative\"], [1, \"fas\", \"fa-list-ul\", \"w-5\", \"h-5\", \"mr-3\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#00f7ff]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [\"routerLink\", \"/equipes/nouveau\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-xl\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#00f7ff]/10\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"transition-all\", \"duration-300\"], [1, \"fas\", \"fa-plus-circle\", \"w-5\", \"h-5\", \"mr-3\", \"group-hover:scale-110\", \"transition-transform\"], [\"routerLink\", \"/equipes/mes-equipes\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-xl\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#00f7ff]/10\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"transition-all\", \"duration-300\"], [1, \"fas\", \"fa-user-friends\", \"w-5\", \"h-5\", \"mr-3\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"my-4\", \"border-t\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"px-4\", \"py-3\"], [1, \"text-xs\", \"font-semibold\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\", \"mb-3\"], [1, \"space-y-3\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\"], [1, \"text-sm\", \"font-medium\", \"text-[#00ff9d]\"], [1, \"text-sm\", \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\"], [1, \"p-4\", \"border-t\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [\"onclick\", \"history.back()\", 1, \"w-full\", \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"px-4\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#6d6870]/30\", \"dark:hover:bg-[#a0a0a0]/30\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-arrow-left\", \"mr-2\"], [1, \"flex-1\", \"flex\", \"flex-col\", \"overflow-hidden\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"border-b\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"px-6\", \"py-4\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"w-8\", \"h-8\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"rounded-lg\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-users\", \"text-white\", \"text-sm\"], [1, \"text-xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\"], [\"type\", \"text\", \"placeholder\", \"Rechercher...\", 1, \"w-64\", \"pl-10\", \"pr-4\", \"py-2\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [1, \"fas\", \"fa-search\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"routerLink\", \"/equipes/nouveau\", 1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-plus\", \"mr-2\"], [1, \"flex-1\", \"overflow-auto\", \"p-6\"]],\n        template: function EquipeLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"div\", 9)(20, \"div\", 10)(21, \"div\", 11);\n            i0.ɵɵelement(22, \"i\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\")(24, \"h1\", 13);\n            i0.ɵɵtext(25, \" \\u00C9quipes \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"p\", 14);\n            i0.ɵɵtext(27, \" Gestion collaborative \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(28, \"nav\", 15)(29, \"a\", 16)(30, \"div\", 17);\n            i0.ɵɵelement(31, \"i\", 18)(32, \"div\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"span\", 17);\n            i0.ɵɵtext(34, \"Liste des \\u00E9quipes\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"a\", 20)(36, \"div\", 17);\n            i0.ɵɵelement(37, \"i\", 21)(38, \"div\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"span\", 17);\n            i0.ɵɵtext(40, \"Cr\\u00E9er une \\u00E9quipe\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"a\", 22)(42, \"div\", 17);\n            i0.ɵɵelement(43, \"i\", 23)(44, \"div\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"span\", 17);\n            i0.ɵɵtext(46, \"Mes \\u00E9quipes\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(47, \"div\", 24);\n            i0.ɵɵelementStart(48, \"div\", 25)(49, \"h3\", 26);\n            i0.ɵɵtext(50, \" Statistiques \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 27)(52, \"div\", 28)(53, \"span\", 14);\n            i0.ɵɵtext(54, \"\\u00C9quipes cr\\u00E9\\u00E9es\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"span\", 29);\n            i0.ɵɵtext(56, \"0\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(57, \"div\", 28)(58, \"span\", 14);\n            i0.ɵɵtext(59, \"Membres actifs\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"span\", 30);\n            i0.ɵɵtext(61, \"0\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(62, \"div\", 28)(63, \"span\", 14);\n            i0.ɵɵtext(64, \"Projets en cours\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"span\", 31);\n            i0.ɵɵtext(66, \"0\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(67, \"div\", 32)(68, \"button\", 33);\n            i0.ɵɵelement(69, \"i\", 34);\n            i0.ɵɵtext(70, \" Retour \\u00E0 l'accueil \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(71, \"div\", 35)(72, \"header\", 36)(73, \"div\", 28)(74, \"div\", 37)(75, \"div\", 38);\n            i0.ɵɵelement(76, \"i\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"div\")(78, \"h2\", 40);\n            i0.ɵɵtext(79, \" Gestion des \\u00C9quipes \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"p\", 14);\n            i0.ɵɵtext(81, \" Organisez et g\\u00E9rez vos \\u00E9quipes de projet \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(82, \"div\", 10)(83, \"div\", 17);\n            i0.ɵɵelement(84, \"input\", 41);\n            i0.ɵɵelementStart(85, \"div\", 42);\n            i0.ɵɵelement(86, \"i\", 43);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(87, \"button\", 44);\n            i0.ɵɵelement(88, \"i\", 45);\n            i0.ɵɵtext(89, \" Nouvelle \\u00E9quipe \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(90, \"main\", 46);\n            i0.ɵɵelement(91, \"router-outlet\");\n            i0.ɵɵelementEnd()()()();\n          }\n        },\n        dependencies: [i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive],\n        styles: [\"@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideIn{0%{opacity:0;transform:translate(-20px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_glow{0%,to{box-shadow:0 0 5px #4f5fad4d}50%{box-shadow:0 0 20px #4f5fad99}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}.equipe-layout[_ngcontent-%COMP%]{min-height:100vh;background:linear-gradient(135deg,#f0f4f8 0%,#e8f2ff 100%);position:relative;overflow:hidden}.dark[_ngcontent-%COMP%]   .equipe-layout[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0a0a0a 0%,#1a1a2e 100%)}.sidebar[_ngcontent-%COMP%]{width:320px;background:rgba(255,255,255,.95);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-right:1px solid rgba(79,95,173,.2);box-shadow:0 8px 32px #0000001a;animation:_ngcontent-%COMP%_slideIn .5s ease-out}.dark[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]{background:rgba(26,26,26,.95);border-right:1px solid rgba(0,247,255,.2);box-shadow:0 8px 32px #0000004d}.nav-item[_ngcontent-%COMP%]{position:relative;overflow:hidden;border-radius:12px;transition:all .3s cubic-bezier(.4,0,.2,1)}.nav-item[_ngcontent-%COMP%]:hover{transform:translate(4px);box-shadow:0 4px 12px #4f5fad33}.dark[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover{box-shadow:0 4px 12px #00f7ff33}.nav-item.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(79,95,173,.1) 0%,rgba(120,38,181,.1) 100%);border-left:3px solid #4f5fad}.dark[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,247,255,.2) 0%,rgba(79,95,173,.2) 100%);border-left:3px solid #00f7ff}.nav-icon[_ngcontent-%COMP%]{position:relative;transition:all .3s ease}.nav-item[_ngcontent-%COMP%]:hover   .nav-icon[_ngcontent-%COMP%]{transform:scale(1.1);filter:drop-shadow(0 0 8px rgba(79,95,173,.5))}.dark[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover   .nav-icon[_ngcontent-%COMP%]{filter:drop-shadow(0 0 8px rgba(0,247,255,.5))}.stats-section[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(79,95,173,.05) 0%,rgba(120,38,181,.05) 100%);border-radius:12px;padding:16px;margin:16px 0;border:1px solid rgba(79,95,173,.1)}.dark[_ngcontent-%COMP%]   .stats-section[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,247,255,.1) 0%,rgba(79,95,173,.1) 100%);border:1px solid rgba(0,247,255,.2)}.stat-item[_ngcontent-%COMP%]{padding:8px 0;border-bottom:1px solid rgba(79,95,173,.1);transition:all .3s ease}.stat-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.stat-item[_ngcontent-%COMP%]:hover{background:rgba(79,95,173,.05);border-radius:8px;padding-left:8px}.dark[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]:hover{background:rgba(0,247,255,.1)}.main-content[_ngcontent-%COMP%]{flex:1;background:rgba(255,255,255,.8);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:20px 0 0 20px;margin:20px 0;box-shadow:0 20px 40px #0000001a;overflow:hidden;animation:_ngcontent-%COMP%_fadeIn .6s ease-out}.dark[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]{background:rgba(26,26,26,.8);box-shadow:0 20px 40px #0000004d}.header[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(255,255,255,.9) 0%,rgba(240,244,248,.9) 100%);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-bottom:1px solid rgba(79,95,173,.2)}.dark[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(26,26,26,.9) 0%,rgba(10,10,10,.9) 100%);border-bottom:1px solid rgba(0,247,255,.2)}.search-input[_ngcontent-%COMP%]{background:rgba(240,244,248,.8);border:1px solid rgba(79,95,173,.2);border-radius:12px;padding:12px 16px 12px 40px;transition:all .3s ease}.search-input[_ngcontent-%COMP%]:focus{background:rgba(255,255,255,.9);border-color:#4f5fad;box-shadow:0 0 0 3px #4f5fad1a;transform:scale(1.02)}.dark[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]{background:rgba(10,10,10,.8);border:1px solid rgba(0,247,255,.2)}.dark[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]:focus{background:rgba(26,26,26,.9);border-color:#00f7ff;box-shadow:0 0 0 3px #00f7ff1a}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);border:none;border-radius:12px;padding:12px 24px;color:#fff;font-weight:600;transition:all .3s cubic-bezier(.4,0,.2,1);position:relative;overflow:hidden}.btn-primary[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #4f5fad66}.dark[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#4f5fad 100%)}.dark[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #00f7ff66}.btn-secondary[_ngcontent-%COMP%]{background:rgba(109,104,112,.2);border:1px solid rgba(109,104,112,.3);border-radius:12px;padding:12px 24px;color:#6d6870;font-weight:500;transition:all .3s ease}.btn-secondary[_ngcontent-%COMP%]:hover{background:rgba(109,104,112,.3);transform:translateY(-1px)}.dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]{background:rgba(160,160,160,.2);border:1px solid rgba(160,160,160,.3);color:#e0e0e0}.dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover{background:rgba(160,160,160,.3)}\"]\n      });\n    }\n  }\n  return EquipeLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}