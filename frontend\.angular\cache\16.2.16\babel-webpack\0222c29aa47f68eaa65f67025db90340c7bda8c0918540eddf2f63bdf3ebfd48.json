{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/rendus.service\";\nimport * as i4 from \"src/app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nfunction ProjectDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectDetailComponent_div_8_div_18_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 28);\n    i0.ɵɵelement(4, \"path\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"span\", 30);\n    i0.ɵɵtext(6, \"Document\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"a\", 31);\n    i0.ɵɵtext(8, \" T\\u00E9l\\u00E9charger \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r6 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"href\", ctx_r5.getFileUrl(file_r6), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectDetailComponent_div_8_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"h2\", 22);\n    i0.ɵɵtext(2, \" Fichiers du projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵtemplate(4, ProjectDetailComponent_div_8_div_18_div_4_Template, 9, 1, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.projet.fichiers);\n  }\n}\nfunction ProjectDetailComponent_div_8_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 32);\n    i0.ɵɵtext(2, \" Projet d\\u00E9j\\u00E0 soumis \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/projects/submit\", a1];\n};\nfunction ProjectDetailComponent_div_8_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 33);\n    i0.ɵɵtext(2, \" Soumettre mon projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, ctx_r4.projetId));\n  }\n}\nfunction ProjectDetailComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"h1\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 13)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 15)(13, \"div\", 16)(14, \"h2\", 17);\n    i0.ɵɵtext(15, \" Description du projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 18);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, ProjectDetailComponent_div_8_div_18_Template, 5, 1, \"div\", 19);\n    i0.ɵɵelementStart(19, \"div\", 20);\n    i0.ɵɵtemplate(20, ProjectDetailComponent_div_8_ng_container_20_Template, 3, 0, \"ng-container\", 21);\n    i0.ɵɵtemplate(21, ProjectDetailComponent_div_8_ng_container_21_Template, 3, 3, \"ng-container\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.projet == null ? null : ctx_r1.projet.titre);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Groupe: \", ctx_r1.projet == null ? null : ctx_r1.projet.groupe, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Date limite: \", i0.ɵɵpipeBind2(11, 7, ctx_r1.projet == null ? null : ctx_r1.projet.dateLimite, \"dd/MM/yyyy\"), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.projet == null ? null : ctx_r1.projet.description) || \"Aucune description fournie\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.projet == null ? null : ctx_r1.projet.fichiers) && (ctx_r1.projet == null ? null : ctx_r1.projet.fichiers.length) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasSubmitted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasSubmitted);\n  }\n}\n// Composant pour afficher les détails d'un projet\nexport let ProjectDetailComponent = /*#__PURE__*/(() => {\n  class ProjectDetailComponent {\n    constructor(route, router, projetService, rendusService, authService) {\n      this.route = route;\n      this.router = router;\n      this.projetService = projetService;\n      this.rendusService = rendusService;\n      this.authService = authService;\n      this.projetId = '';\n      this.isLoading = true;\n      this.hasSubmitted = false;\n    }\n    ngOnInit() {\n      this.projetId = this.route.snapshot.paramMap.get('id') || '';\n      this.loadProjetDetails();\n      this.checkRenduStatus();\n    }\n    loadProjetDetails() {\n      this.isLoading = true;\n      this.projetService.getProjetById(this.projetId).subscribe({\n        next: projet => {\n          this.projet = projet;\n          this.isLoading = false;\n        },\n        error: err => {\n          console.error('Erreur lors du chargement du projet', err);\n          this.isLoading = false;\n          this.router.navigate(['/projects']);\n        }\n      });\n    }\n    checkRenduStatus() {\n      const etudiantId = this.authService.getCurrentUserId();\n      if (etudiantId) {\n        this.rendusService.checkRenduExists(this.projetId, etudiantId).subscribe({\n          next: exists => {\n            console.log(exists);\n            this.hasSubmitted = exists;\n          },\n          error: err => {\n            console.error('Erreur lors de la vérification du rendu', err);\n          }\n        });\n      }\n    }\n    getFileUrl(filePath) {\n      // Extraire uniquement le nom du fichier\n      let fileName = filePath;\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        fileName = parts[parts.length - 1];\n      }\n      // Utiliser l'endpoint API spécifique pour le téléchargement\n      return `http://localhost:3000/api/projets/download/${fileName}`;\n    }\n    static {\n      this.ɵfac = function ProjectDetailComponent_Factory(t) {\n        return new (t || ProjectDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.RendusService), i0.ɵɵdirectiveInject(i4.AuthuserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectDetailComponent,\n        selectors: [[\"app-project-detail\"]],\n        decls: 9,\n        vars: 2,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"p-4\", \"md:p-6\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"mb-6\"], [\"routerLink\", \"/projects\", 1, \"text-[#4f5fad]\", \"hover:text-[#3d4a85]\", \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [\"class\", \"flex justify-center my-8\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-xl shadow-md overflow-hidden\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-8\"], [1, \"w-12\", \"h-12\", \"border-4\", \"border-[#4f5fad]/20\", \"border-t-[#4f5fad]\", \"rounded-full\", \"animate-spin\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\"], [1, \"border-t-4\", \"border-[#4f5fad]\", \"p-6\", \"bg-white\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\"], [1, \"flex\", \"items-center\", \"mt-2\", \"text-sm\", \"text-[#6d6870]\"], [1, \"mx-2\"], [1, \"p-6\"], [1, \"mb-8\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"mb-2\"], [1, \"text-[#6d6870]\"], [\"class\", \"mb-8\", 4, \"ngIf\"], [1, \"flex\", \"justify-end\", \"mt-6\"], [4, \"ngIf\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [\"class\", \"border border-[#bdc6cc] rounded-lg p-4 hover:bg-[#edf1f4] transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"border\", \"border-[#bdc6cc]\", \"rounded-lg\", \"p-4\", \"hover:bg-[#edf1f4]\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-[#4f5fad]\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-[#6d6870]\", \"truncate\"], [\"download\", \"\", 1, \"text-[#4f5fad]\", \"hover:text-[#3d4a85]\", \"text-sm\", \"font-medium\", \"transition-colors\", 3, \"href\"], [1, \"bg-green-100\", \"text-green-800\", \"px-4\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\"], [1, \"bg-[#4f5fad]\", \"hover:bg-[#3d4a85]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"transition-colors\", 3, \"routerLink\"]],\n        template: function ProjectDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(4, \"svg\", 4);\n            i0.ɵɵelement(5, \"path\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(6, \" Retour aux projets \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(7, ProjectDetailComponent_div_7_Template, 2, 0, \"div\", 6);\n            i0.ɵɵtemplate(8, ProjectDetailComponent_div_8_Template, 22, 10, \"div\", 7);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i1.RouterLink, i5.DatePipe],\n        styles: [\".project-container[_ngcontent-%COMP%]{padding:1.5rem;background-color:#fff;border-radius:.5rem;box-shadow:0 2px 4px #0000001a}.project-header[_ngcontent-%COMP%]{margin-bottom:1.5rem;border-bottom:1px solid #e5e7eb;padding-bottom:1rem}.project-description[_ngcontent-%COMP%]{margin-bottom:1.5rem}.project-meta[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:1rem;margin-bottom:1.5rem}.project-meta-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}\"]\n      });\n    }\n  }\n  return ProjectDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}