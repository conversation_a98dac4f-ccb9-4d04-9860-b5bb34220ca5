{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EquipesRoutingModule } from './equipes-routing.module';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule } from '@angular/forms';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport * as i0 from \"@angular/core\";\nexport let EquipesModule = /*#__PURE__*/(() => {\n  class EquipesModule {\n    static {\n      this.ɵfac = function EquipesModule_Factory(t) {\n        return new (t || EquipesModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: EquipesModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, EquipesRoutingModule, FormsModule, DragDropModule, HttpClientModule]\n      });\n    }\n  }\n  return EquipesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}