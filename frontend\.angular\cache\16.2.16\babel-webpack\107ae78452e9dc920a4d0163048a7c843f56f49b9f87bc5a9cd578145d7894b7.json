{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport let PipesModule = /*#__PURE__*/(() => {\n  class PipesModule {\n    static {\n      this.ɵfac = function PipesModule_Factory(t) {\n        return new (t || PipesModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: PipesModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule]\n      });\n    }\n  }\n  return PipesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}